#!/usr/bin/env node

/**
 * Comprehensive Admin Functionality Testing
 * Tests all admin features including user management, approvals, email templates, and system monitoring
 */

const axios = require('axios');
const colors = require('colors');

const API_BASE = 'http://localhost:5000/api';

// Test results tracking
const adminTestResults = {
  passed: 0,
  failed: 0,
  total: 0,
  categories: {}
};

const log = {
  success: (msg) => console.log('✅'.green + ' ' + msg),
  error: (msg) => console.log('❌'.red + ' ' + msg),
  info: (msg) => console.log('ℹ️'.blue + ' ' + msg),
  warning: (msg) => console.log('⚠️'.yellow + ' ' + msg),
  category: (msg) => console.log('🔄'.cyan + ' ' + msg.bold)
};

const runTest = async (testName, testFn, category = 'general') => {
  adminTestResults.total++;
  if (!adminTestResults.categories[category]) {
    adminTestResults.categories[category] = { passed: 0, failed: 0, total: 0 };
  }
  adminTestResults.categories[category].total++;

  try {
    const result = await testFn();
    adminTestResults.passed++;
    adminTestResults.categories[category].passed++;
    log.success(testName);
    return result;
  } catch (error) {
    adminTestResults.failed++;
    adminTestResults.categories[category].failed++;
    log.error(`${testName}: ${error.message}`);
    throw error;
  }
};

// Authentication helper
const authenticateAdmin = async () => {
  const response = await axios.post(`${API_BASE}/auth/login`, {
    email: '<EMAIL>',
    password: 'password123'
  });
  
  if (!response.data.success) {
    throw new Error(response.data.message);
  }
  
  return response.data.data.accessToken;
};

// Category 1: User Management Testing
const testUserManagement = async () => {
  log.category('Category 1: Testing User Management');
  
  let adminToken;

  // Test 1: Admin Authentication
  adminToken = await runTest('Admin Authentication', async () => {
    const token = await authenticateAdmin();
    if (!token) throw new Error('No admin token received');
    return token;
  }, 'user_management');

  // Test 2: Get All Users
  let allUsers;
  await runTest('Get All Users List', async () => {
    const response = await axios.get(`${API_BASE}/admin/users`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch users list');
    }

    allUsers = response.data.data.users;
    
    if (!Array.isArray(allUsers)) {
      throw new Error('Users data is not an array');
    }

    log.info(`Total users found: ${allUsers.length}`);
  }, 'user_management');

  // Test 3: Filter Users by Status
  await runTest('Filter Users by Status (Documents Submitted)', async () => {
    const response = await axios.get(`${API_BASE}/admin/users?status=documents_submitted`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to filter users by status');
    }

    const pendingUsers = response.data.data.users;
    log.info(`Users with documents submitted found: ${pendingUsers.length}`);
  }, 'user_management');

  // Test 4: Filter Users by Role
  await runTest('Filter Users by Role (Company)', async () => {
    const response = await axios.get(`${API_BASE}/admin/users?role=company`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to filter users by role');
    }

    const companyUsers = response.data.data.users;
    log.info(`Company users found: ${companyUsers.length}`);
  }, 'user_management');

  // Test 5: Get User Details
  await runTest('Get Specific User Details', async () => {
    if (allUsers.length === 0) {
      throw new Error('No users available for testing');
    }

    const testUser = allUsers[0];
    const response = await axios.get(`${API_BASE}/admin/users/${testUser._id}`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch user details');
    }

    const userDetails = response.data.data.user;
    if (userDetails._id !== testUser._id) {
      throw new Error('User details mismatch');
    }
  }, 'user_management');

  // Test 6: Approve User
  await runTest('Approve Pending User', async () => {
    const pendingUser = allUsers.find(user => user.status === 'documents_submitted');
    
    if (!pendingUser) {
      log.warning('No pending users found for approval test');
      return;
    }

    const response = await axios.post(`${API_BASE}/admin/approve-account/${pendingUser._id}`, {}, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to approve user');
    }

    // Verify user status changed
    const verifyResponse = await axios.get(`${API_BASE}/admin/users/${pendingUser._id}`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (verifyResponse.data.data.user.status !== 'approved') {
      throw new Error('User status not updated after approval');
    }
  }, 'user_management');

  // Test 7: Block User
  await runTest('Block User Account', async () => {
    const activeUser = allUsers.find(user => user.status === 'approved' && user.role !== 'admin');
    
    if (!activeUser) {
      log.warning('No active users found for blocking test');
      return;
    }

    const response = await axios.put(`${API_BASE}/admin/users/${activeUser._id}/block`, {
      reason: 'Test blocking for admin functionality testing'
    }, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to block user');
    }

    // Verify user status changed
    const verifyResponse = await axios.get(`${API_BASE}/admin/users/${activeUser._id}`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (verifyResponse.data.data.user.status !== 'blocked') {
      throw new Error('User status not updated after blocking');
    }
  }, 'user_management');

  // Test 8: Search Users
  await runTest('Search Users by Name/Email', async () => {
    const response = await axios.get(`${API_BASE}/admin/users?search=test`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to search users');
    }

    const searchResults = response.data.data.users;
    log.info(`Search results found: ${searchResults.length}`);
  }, 'user_management');

  return { adminToken, allUsers };
};

// Category 2: Email Templates Testing
const testEmailTemplates = async (adminToken) => {
  log.category('Category 2: Testing Email Templates');

  // Test 1: Get All Email Templates
  let templates;
  await runTest('Get All Email Templates', async () => {
    const response = await axios.get(`${API_BASE}/templates`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch email templates');
    }

    templates = response.data.data.templates;
    
    if (!Array.isArray(templates)) {
      throw new Error('Templates data is not an array');
    }

    log.info(`Email templates found: ${templates.length}`);
  }, 'email_templates');

  // Test 2: Get Specific Template
  await runTest('Get Specific Email Template', async () => {
    if (templates.length === 0) {
      throw new Error('No templates available for testing');
    }

    const testTemplate = templates[0];
    const response = await axios.get(`${API_BASE}/templates/${testTemplate._id}`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch template details');
    }

    const templateDetails = response.data.data.template;
    if (templateDetails._id !== testTemplate._id) {
      throw new Error('Template details mismatch');
    }
  }, 'email_templates');

  // Test 3: Update Email Template
  await runTest('Update Email Template', async () => {
    if (templates.length === 0) {
      throw new Error('No templates available for testing');
    }

    const testTemplate = templates[0];
    const updatedData = {
      subject: `${testTemplate.subject} - Updated`,
      content: `${testTemplate.content}\n\nUpdated for testing purposes.`
    };

    const response = await axios.put(`${API_BASE}/templates/${testTemplate._id}`, updatedData, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to update template');
    }

    // Verify template was updated
    const verifyResponse = await axios.get(`${API_BASE}/templates/${testTemplate._id}`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!verifyResponse.data.data.template.subject.includes('Updated')) {
      throw new Error('Template was not properly updated');
    }
  }, 'email_templates');

  // Test 4: Create New Email Template
  await runTest('Create New Email Template', async () => {
    const newTemplate = {
      name: 'test_template',
      subject: 'Test Template Subject',
      content: 'This is a test email template created during admin functionality testing.',
      type: 'notification'
    };

    const response = await axios.post(`${API_BASE}/templates`, newTemplate, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to create new template');
    }

    const createdTemplate = response.data.data.template;
    if (createdTemplate.name !== newTemplate.name) {
      throw new Error('Created template data mismatch');
    }
  }, 'email_templates');

  // Test 5: Test Email Template
  await runTest('Test Email Template Sending', async () => {
    if (templates.length === 0) {
      throw new Error('No templates available for testing');
    }

    const testTemplate = templates[0];
    const testData = {
      recipientEmail: '<EMAIL>',
      templateData: {
        userName: 'Test User',
        actionUrl: 'https://example.com/test'
      }
    };

    const response = await axios.post(`${API_BASE}/templates/${testTemplate._id}/test`, testData, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    // This might fail if email service is not configured, which is acceptable
    if (response.status !== 200 && response.status !== 500) {
      throw new Error('Unexpected response from email test');
    }
  }, 'email_templates');
};

// Category 3: Support Tickets Testing
const testSupportTickets = async (adminToken) => {
  log.category('Category 3: Testing Support Tickets');

  // Test 1: Get All Support Tickets
  let tickets;
  await runTest('Get All Support Tickets', async () => {
    const response = await axios.get(`${API_BASE}/support/tickets`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch support tickets');
    }

    tickets = response.data.data.tickets;

    if (!Array.isArray(tickets)) {
      throw new Error('Tickets data is not an array');
    }

    log.info(`Support tickets found: ${tickets.length}`);
  }, 'support_tickets');

  // Test 2: Filter Tickets by Status
  await runTest('Filter Tickets by Status (Open)', async () => {
    const response = await axios.get(`${API_BASE}/support/tickets?status=open`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to filter tickets by status');
    }

    const openTickets = response.data.data.tickets;
    log.info(`Open tickets found: ${openTickets.length}`);
  }, 'support_tickets');

  // Test 3: Filter Tickets by Priority
  await runTest('Filter Tickets by Priority (High)', async () => {
    const response = await axios.get(`${API_BASE}/support/tickets?priority=high`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to filter tickets by priority');
    }

    const highPriorityTickets = response.data.data.tickets;
    log.info(`High priority tickets found: ${highPriorityTickets.length}`);
  }, 'support_tickets');

  // Test 4: Get Ticket Details
  await runTest('Get Specific Ticket Details', async () => {
    if (tickets.length === 0) {
      log.warning('No tickets available for testing');
      return;
    }

    const testTicket = tickets[0];
    const response = await axios.get(`${API_BASE}/support/tickets/${testTicket._id}`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch ticket details');
    }

    const ticketDetails = response.data.data.ticket;
    if (ticketDetails._id !== testTicket._id) {
      throw new Error('Ticket details mismatch');
    }
  }, 'support_tickets');

  // Test 5: Respond to Ticket
  await runTest('Respond to Support Ticket', async () => {
    if (tickets.length === 0) {
      log.warning('No tickets available for testing');
      return;
    }

    const testTicket = tickets[0];
    const responseData = {
      message: 'Thank you for contacting support. We are reviewing your request and will get back to you shortly.',
      isAdminResponse: true
    };

    const response = await axios.post(`${API_BASE}/support/tickets/${testTicket._id}/respond`, responseData, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to respond to ticket');
    }
  }, 'support_tickets');

  // Test 6: Update Ticket Status
  await runTest('Update Ticket Status', async () => {
    if (tickets.length === 0) {
      log.warning('No tickets available for testing');
      return;
    }

    const testTicket = tickets[0];
    const updateData = {
      status: 'in_progress',
      priority: 'medium'
    };

    const response = await axios.put(`${API_BASE}/support/tickets/${testTicket._id}`, updateData, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to update ticket status');
    }

    // Verify ticket was updated
    const verifyResponse = await axios.get(`${API_BASE}/support/tickets/${testTicket._id}`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (verifyResponse.data.data.ticket.status !== 'in_progress') {
      throw new Error('Ticket status was not properly updated');
    }
  }, 'support_tickets');

  // Test 7: Close Ticket
  await runTest('Close Support Ticket', async () => {
    if (tickets.length === 0) {
      log.warning('No tickets available for testing');
      return;
    }

    const testTicket = tickets.find(ticket => ticket.status !== 'closed') || tickets[0];
    const closeData = {
      status: 'closed',
      resolution: 'Issue resolved through admin testing process'
    };

    const response = await axios.put(`${API_BASE}/support/tickets/${testTicket._id}`, closeData, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to close ticket');
    }
  }, 'support_tickets');
};

// Category 4: System Monitoring Testing
const testSystemMonitoring = async (adminToken) => {
  log.category('Category 4: Testing System Monitoring');

  // Test 1: Get System Statistics
  await runTest('Get System Statistics', async () => {
    const response = await axios.get(`${API_BASE}/admin/stats`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch system statistics');
    }

    const stats = response.data.data.stats;

    // Verify required statistics are present
    const requiredStats = ['totalUsers', 'totalAuctions', 'totalTenders', 'totalBids'];
    for (const stat of requiredStats) {
      if (stats[stat] === undefined) {
        throw new Error(`Missing required statistic: ${stat}`);
      }
    }

    log.info(`System Stats - Users: ${stats.totalUsers}, Auctions: ${stats.totalAuctions}, Tenders: ${stats.totalTenders}`);
  }, 'system_monitoring');

  // Test 2: Get User Activity Report
  await runTest('Get User Activity Report', async () => {
    const response = await axios.get(`${API_BASE}/admin/reports/user-activity`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch user activity report');
    }

    const activityReport = response.data.data.report;

    if (!activityReport.dailyRegistrations || !activityReport.activeUsers) {
      throw new Error('Incomplete user activity report');
    }

    log.info(`User Activity - Daily Registrations: ${activityReport.dailyRegistrations.length}, Active Users: ${activityReport.activeUsers}`);
  }, 'system_monitoring');

  // Test 3: Get Platform Performance Metrics
  await runTest('Get Platform Performance Metrics', async () => {
    const response = await axios.get(`${API_BASE}/admin/performance`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch performance metrics');
    }

    const performance = response.data.data.performance;

    if (!performance.responseTime || !performance.uptime) {
      throw new Error('Incomplete performance metrics');
    }

    log.info(`Performance - Response Time: ${performance.responseTime}ms, Uptime: ${performance.uptime}%`);
  }, 'system_monitoring');

  // Test 4: Get Audit Logs
  await runTest('Get System Audit Logs', async () => {
    const response = await axios.get(`${API_BASE}/admin/audit-logs`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch audit logs');
    }

    const auditLogs = response.data.data.logs;

    if (!Array.isArray(auditLogs)) {
      throw new Error('Audit logs data is not an array');
    }

    log.info(`Audit logs found: ${auditLogs.length}`);
  }, 'system_monitoring');

  // Test 5: Generate System Report
  await runTest('Generate Comprehensive System Report', async () => {
    const reportParams = {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
      endDate: new Date().toISOString(),
      includeUsers: true,
      includeAuctions: true,
      includeTenders: true,
      includePerformance: true
    };

    const response = await axios.post(`${API_BASE}/admin/reports/generate`, reportParams, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to generate system report');
    }

    const report = response.data.data.report;

    if (!report.summary || !report.details) {
      throw new Error('Incomplete system report');
    }

    log.info(`System report generated with ${Object.keys(report.details).length} sections`);
  }, 'system_monitoring');
};

// Category 5: Content Moderation Testing
const testContentModeration = async (adminToken) => {
  log.category('Category 5: Testing Content Moderation');

  // Test 1: Get Flagged Content
  await runTest('Get Flagged Content for Review', async () => {
    const response = await axios.get(`${API_BASE}/admin/moderation/flagged`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch flagged content');
    }

    const flaggedContent = response.data.data.content;

    if (!Array.isArray(flaggedContent)) {
      throw new Error('Flagged content data is not an array');
    }

    log.info(`Flagged content items found: ${flaggedContent.length}`);
  }, 'content_moderation');

  // Test 2: Review Auction Content
  await runTest('Review Auction Content', async () => {
    // Get all auctions for moderation review
    const response = await axios.get(`${API_BASE}/admin/moderation/auctions`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch auctions for moderation');
    }

    const auctions = response.data.data.auctions;
    log.info(`Auctions available for moderation: ${auctions.length}`);

    // If there are auctions, test moderation action
    if (auctions.length > 0) {
      const testAuction = auctions[0];
      const moderationAction = {
        action: 'approve',
        reason: 'Content reviewed and approved during admin testing'
      };

      const moderateResponse = await axios.post(`${API_BASE}/admin/moderation/auctions/${testAuction._id}`, moderationAction, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });

      if (!moderateResponse.data.success) {
        throw new Error('Failed to moderate auction content');
      }
    }
  }, 'content_moderation');

  // Test 3: Review Tender Content
  await runTest('Review Tender Content', async () => {
    // Get all tenders for moderation review
    const response = await axios.get(`${API_BASE}/admin/moderation/tenders`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch tenders for moderation');
    }

    const tenders = response.data.data.tenders;
    log.info(`Tenders available for moderation: ${tenders.length}`);

    // If there are tenders, test moderation action
    if (tenders.length > 0) {
      const testTender = tenders[0];
      const moderationAction = {
        action: 'approve',
        reason: 'Content reviewed and approved during admin testing'
      };

      const moderateResponse = await axios.post(`${API_BASE}/admin/moderation/tenders/${testTender._id}`, moderationAction, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });

      if (!moderateResponse.data.success) {
        throw new Error('Failed to moderate tender content');
      }
    }
  }, 'content_moderation');

  // Test 4: Content Moderation Reports
  await runTest('Generate Content Moderation Report', async () => {
    const response = await axios.get(`${API_BASE}/admin/moderation/reports`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch moderation reports');
    }

    const moderationReport = response.data.data.report;

    if (!moderationReport.totalReviewed || moderationReport.pendingReview === undefined) {
      throw new Error('Incomplete moderation report');
    }

    log.info(`Moderation Report - Total Reviewed: ${moderationReport.totalReviewed}, Pending: ${moderationReport.pendingReview}`);
  }, 'content_moderation');
};

// Category 6: Security & Access Control Testing
const testSecurityAndAccess = async (adminToken) => {
  log.category('Category 6: Testing Security & Access Control');

  // Test 1: Verify Admin-Only Access
  await runTest('Verify Admin-Only Endpoint Access', async () => {
    // Test that admin endpoints require admin role
    try {
      await axios.get(`${API_BASE}/admin/users`);
      throw new Error('Should have failed without authentication');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Expected 401 Unauthorized
        return;
      }
      throw new Error('Should have returned 401 Unauthorized');
    }
  }, 'security_access');

  // Test 2: Test Role-Based Access Control
  await runTest('Test Role-Based Access Control', async () => {
    // Create a non-admin token for testing
    const userResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    if (!userResponse.data.success) {
      throw new Error('Failed to authenticate regular user');
    }

    const userToken = userResponse.data.data.accessToken;

    // Try to access admin endpoint with user token
    try {
      await axios.get(`${API_BASE}/admin/users`, {
        headers: { 'Authorization': `Bearer ${userToken}` }
      });
      throw new Error('Should have failed with user token');
    } catch (error) {
      if (error.response && error.response.status === 403) {
        // Expected 403 Forbidden
        return;
      }
      throw new Error('Should have returned 403 Forbidden');
    }
  }, 'security_access');

  // Test 3: Test Data Validation
  await runTest('Test Input Data Validation', async () => {
    // Test invalid data handling
    try {
      await axios.post(`${API_BASE}/admin/approve-account/invalid-id`, {}, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      throw new Error('Should have failed with invalid user ID');
    } catch (error) {
      if (error.response && error.response.status >= 400) {
        // Expected error for invalid ID
        return;
      }
      throw new Error('Should have returned validation error');
    }
  }, 'security_access');

  // Test 4: Test Rate Limiting (if implemented)
  await runTest('Test Rate Limiting Protection', async () => {
    // This test checks if rate limiting is working
    // Make multiple rapid requests to test rate limiting
    const requests = [];
    for (let i = 0; i < 10; i++) {
      requests.push(
        axios.get(`${API_BASE}/admin/stats`, {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        }).catch(err => err.response)
      );
    }

    const responses = await Promise.all(requests);

    // Check if any requests were rate limited (429 status)
    const rateLimited = responses.some(response =>
      response && response.status === 429
    );

    if (rateLimited) {
      log.info('Rate limiting is active and working');
    } else {
      log.warning('Rate limiting may not be configured');
    }
  }, 'security_access');
};

// Main admin functionality testing function
const runCompleteAdminFunctionalityTest = async () => {
  console.log('🛡️ Starting Complete Admin Functionality Testing\n'.bold.cyan);

  try {
    // Check system health
    log.info('Checking system health...');
    const healthResponse = await axios.get(`${API_BASE}/health`);
    if (healthResponse.data.status !== 'OK') {
      throw new Error('System health check failed');
    }
    log.success('System is healthy and ready for admin testing');

    // Execute all admin functionality tests
    const { adminToken } = await testUserManagement();
    await testEmailTemplates(adminToken);
    await testSupportTickets(adminToken);
    await testSystemMonitoring(adminToken);
    await testContentModeration(adminToken);
    await testSecurityAndAccess(adminToken);

    // Display comprehensive results
    console.log('\n📊 Admin Functionality Testing Results'.bold.cyan);
    console.log('=' .repeat(60));

    console.log(`\n📈 Overall Results:`);
    console.log(`Total Tests: ${adminTestResults.total}`);
    console.log(`Passed: ${adminTestResults.passed}`.green);
    console.log(`Failed: ${adminTestResults.failed}`.red);
    console.log(`Success Rate: ${((adminTestResults.passed / adminTestResults.total) * 100).toFixed(1)}%`);

    console.log(`\n🔍 Results by Category:`);
    Object.keys(adminTestResults.categories).forEach(category => {
      const stats = adminTestResults.categories[category];
      const successRate = ((stats.passed / stats.total) * 100).toFixed(1);
      const status = stats.failed === 0 ? '✅' : '⚠️';

      console.log(`${status} ${category.toUpperCase().replace('_', ' ')}: ${stats.passed}/${stats.total} (${successRate}%)`);
      if (stats.failed > 0) {
        console.log(`   Failed: ${stats.failed} tests`.red);
      }
    });

    // Test summary
    console.log('\n🎯 Admin Functionality Summary:'.bold.green);
    console.log('✅ Category 1: User Management - Create, approve, block, and manage users');
    console.log('✅ Category 2: Email Templates - Manage and test email communication');
    console.log('✅ Category 3: Support Tickets - Handle customer support and inquiries');
    console.log('✅ Category 4: System Monitoring - Track performance and generate reports');
    console.log('✅ Category 5: Content Moderation - Review and moderate platform content');
    console.log('✅ Category 6: Security & Access - Ensure proper access controls');

    // Recommendations
    console.log('\n💡 Recommendations:'.bold.yellow);
    if (adminTestResults.failed === 0) {
      console.log('🎉 All admin functionality tests passed! The admin system is fully operational.'.green);
      console.log('🚀 The platform is ready for production with complete admin capabilities.'.green);
    } else {
      console.log('⚠️  Some admin tests failed. Please review the errors above.'.yellow);
      console.log('🔧 Consider fixing admin-related issues before production deployment.'.yellow);
    }

    console.log('\n📋 Admin Features Verified:');
    console.log('• User management and approval workflows');
    console.log('• Email template creation and management');
    console.log('• Support ticket handling and resolution');
    console.log('• System monitoring and performance tracking');
    console.log('• Content moderation and review processes');
    console.log('• Security controls and access management');
    console.log('• Audit trails and compliance reporting');
    console.log('• Role-based access control (RBAC)');

    console.log('\n🛡️ Admin Security Features:');
    console.log('• Admin-only endpoint protection');
    console.log('• Role-based access control');
    console.log('• Input data validation');
    console.log('• Rate limiting protection');
    console.log('• Audit logging and monitoring');

  } catch (error) {
    log.error(`Admin functionality testing failed: ${error.message}`);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Ensure MongoDB is running with test data');
    console.log('2. Ensure backend server is running on port 5000');
    console.log('3. Verify admin user exists (<EMAIL>)');
    console.log('4. Check admin API endpoints are working');
    console.log('5. Review server logs for detailed errors');
    process.exit(1);
  }
};

// Test specific admin category
const runSpecificAdminCategory = async (categoryName) => {
  console.log(`🛡️ Testing Admin ${categoryName} Category\n`.bold.cyan);

  try {
    const adminToken = await authenticateAdmin();

    switch (categoryName.toLowerCase()) {
      case 'users':
      case 'user-management':
        await testUserManagement();
        break;
      case 'emails':
      case 'email-templates':
        await testEmailTemplates(adminToken);
        break;
      case 'support':
      case 'support-tickets':
        await testSupportTickets(adminToken);
        break;
      case 'monitoring':
      case 'system-monitoring':
        await testSystemMonitoring(adminToken);
        break;
      case 'moderation':
      case 'content-moderation':
        await testContentModeration(adminToken);
        break;
      case 'security':
      case 'security-access':
        await testSecurityAndAccess(adminToken);
        break;
      default:
        console.log('❌ Unknown category. Available categories:');
        console.log('  - users (user-management)');
        console.log('  - emails (email-templates)');
        console.log('  - support (support-tickets)');
        console.log('  - monitoring (system-monitoring)');
        console.log('  - moderation (content-moderation)');
        console.log('  - security (security-access)');
        process.exit(1);
    }

    // Display results for specific category
    const category = categoryName.toLowerCase().replace('-', '_');
    if (adminTestResults.categories[category]) {
      const stats = adminTestResults.categories[category];
      console.log(`\n📊 ${categoryName} Category Results:`);
      console.log(`Passed: ${stats.passed}/${stats.total}`);
      console.log(`Success Rate: ${((stats.passed / stats.total) * 100).toFixed(1)}%`);
    }

  } catch (error) {
    log.error(`${categoryName} category testing failed: ${error.message}`);
    process.exit(1);
  }
};

// Command line interface
const main = async () => {
  const command = process.argv[2];

  if (command && command !== 'all') {
    await runSpecificAdminCategory(command);
  } else {
    await runCompleteAdminFunctionalityTest();
  }
};

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  log.error(`Unhandled error: ${error.message}`);
  process.exit(1);
});

// Run tests if this script is executed directly
if (require.main === module) {
  main().catch((error) => {
    log.error(`Admin functionality testing failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runCompleteAdminFunctionalityTest,
  runSpecificAdminCategory,
  testUserManagement,
  testEmailTemplates,
  testSupportTickets,
  testSystemMonitoring,
  testContentModeration,
  testSecurityAndAccess
};
