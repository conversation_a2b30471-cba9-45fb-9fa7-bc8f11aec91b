#!/usr/bin/env node

/**
 * Complete Testing Environment Setup Script
 * This script sets up the application for comprehensive testing
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up complete testing environment...\n');

// Check if required files exist
const requiredFiles = [
  'backend/package.json',
  'frontend/package.json',
  'backend/.env.example',
  'frontend/.env.example'
];

console.log('📋 Checking required files...');
for (const file of requiredFiles) {
  if (!fs.existsSync(file)) {
    console.error(`❌ Missing required file: ${file}`);
    process.exit(1);
  }
  console.log(`✅ Found: ${file}`);
}

// Create environment files if they don't exist
console.log('\n🔧 Setting up environment files...');

// Backend .env
if (!fs.existsSync('backend/.env')) {
  console.log('📝 Creating backend/.env from example...');
  const backendEnvExample = fs.readFileSync('backend/.env.example', 'utf8');
  const backendEnv = backendEnvExample
    .replace('your_super_secret_jwt_key_here', 'test_jwt_secret_key_for_development_only')
    .replace('your_mailtrap_username', 'test_user')
    .replace('your_mailtrap_password', 'test_pass')
    .replace('your_cloudinary_cloud_name', 'test_cloud')
    .replace('your_cloudinary_api_key', 'test_key')
    .replace('your_cloudinary_api_secret', 'test_secret')
    .replace('pk_test_your_stripe_publishable_key', 'pk_test_example')
    .replace('sk_test_your_stripe_secret_key', 'sk_test_example')
    .replace('whsec_your_webhook_secret', 'whsec_example');
  
  fs.writeFileSync('backend/.env', backendEnv);
  console.log('✅ Created backend/.env');
} else {
  console.log('✅ backend/.env already exists');
}

// Frontend .env.local
if (!fs.existsSync('frontend/.env.local')) {
  console.log('📝 Creating frontend/.env.local from example...');
  const frontendEnvExample = fs.readFileSync('frontend/.env.example', 'utf8');
  const frontendEnv = frontendEnvExample
    .replace('your_cloudinary_cloud_name', 'test_cloud')
    .replace('pk_test_your_stripe_publishable_key', 'pk_test_example');
  
  fs.writeFileSync('frontend/.env.local', frontendEnv);
  console.log('✅ Created frontend/.env.local');
} else {
  console.log('✅ frontend/.env.local already exists');
}

console.log('\n📦 Installing dependencies...');

// Install backend dependencies
console.log('🔧 Installing backend dependencies...');
try {
  execSync('cd backend && npm install', { stdio: 'inherit' });
  console.log('✅ Backend dependencies installed');
} catch (error) {
  console.error('❌ Failed to install backend dependencies');
  process.exit(1);
}

// Install frontend dependencies
console.log('🔧 Installing frontend dependencies...');
try {
  execSync('cd frontend && npm install', { stdio: 'inherit' });
  console.log('✅ Frontend dependencies installed');
} catch (error) {
  console.error('❌ Failed to install frontend dependencies');
  process.exit(1);
}

console.log('\n🗄️  Database setup...');
console.log('📝 Make sure MongoDB is running on your system');
console.log('   - macOS: brew services start mongodb-community');
console.log('   - Ubuntu: sudo systemctl start mongod');
console.log('   - Windows: Start MongoDB service from Services');

console.log('\n🎯 Testing environment setup complete!');
console.log('\n📋 Next steps:');
console.log('1. Start MongoDB service');
console.log('2. Run: cd backend && npm run dev');
console.log('3. Run: cd frontend && npm run dev');
console.log('4. Open http://localhost:3000 in your browser');
console.log('\n🚀 Ready for comprehensive testing!');
