#!/usr/bin/env node

/**
 * Automated Testing Checklist
 * Runs basic health checks and API tests
 */

const axios = require('axios');
const colors = require('colors');

const API_BASE = 'http://localhost:5000/api';
const FRONTEND_URL = 'http://localhost:3000';

const testResults = {
  passed: 0,
  failed: 0,
  total: 0
};

const log = {
  success: (msg) => console.log('✅'.green + ' ' + msg),
  error: (msg) => console.log('❌'.red + ' ' + msg),
  info: (msg) => console.log('ℹ️'.blue + ' ' + msg),
  warning: (msg) => console.log('⚠️'.yellow + ' ' + msg)
};

const runTest = async (testName, testFn) => {
  testResults.total++;
  try {
    await testFn();
    testResults.passed++;
    log.success(testName);
  } catch (error) {
    testResults.failed++;
    log.error(`${testName}: ${error.message}`);
  }
};

const testHealthCheck = async () => {
  const response = await axios.get(`${API_BASE}/health`);
  if (response.status !== 200) {
    throw new Error('Health check failed');
  }
  if (response.data.status !== 'OK') {
    throw new Error('Server status not OK');
  }
  if (response.data.database !== 'connected') {
    throw new Error('Database not connected');
  }
};

const testUserRegistration = async () => {
  const userData = {
    name: 'Test User',
    email: `test${Date.now()}@example.com`,
    password: 'password123',
    role: 'individual',
    phone: '+966501234567',
    nationalId: '**********',
    dateOfBirth: '1990-01-01'
  };
  
  const response = await axios.post(`${API_BASE}/auth/register`, userData);
  if (response.status !== 201) {
    throw new Error('Registration failed');
  }
  if (!response.data.success) {
    throw new Error(response.data.message);
  }
};

const testUserLogin = async () => {
  const loginData = {
    email: '<EMAIL>',
    password: 'password123'
  };
  
  const response = await axios.post(`${API_BASE}/auth/login`, loginData);
  if (response.status !== 200) {
    throw new Error('Login failed');
  }
  if (!response.data.success) {
    throw new Error(response.data.message);
  }
  if (!response.data.data.accessToken) {
    throw new Error('No access token received');
  }
  
  return response.data.data.accessToken;
};

const testPublicStats = async () => {
  const response = await axios.get(`${API_BASE}/stats/public`);
  if (response.status !== 200) {
    throw new Error('Public stats failed');
  }
  if (!response.data.success) {
    throw new Error(response.data.message);
  }
  if (typeof response.data.data.stats.users !== 'number') {
    throw new Error('Invalid stats format');
  }
};

const testAuctionsList = async () => {
  const response = await axios.get(`${API_BASE}/auctions`);
  if (response.status !== 200) {
    throw new Error('Auctions list failed');
  }
  if (!response.data.success) {
    throw new Error(response.data.message);
  }
  if (!Array.isArray(response.data.data.auctions)) {
    throw new Error('Auctions data is not an array');
  }
};

const testTendersList = async () => {
  const response = await axios.get(`${API_BASE}/tenders`);
  if (response.status !== 200) {
    throw new Error('Tenders list failed');
  }
  if (!response.data.success) {
    throw new Error(response.data.message);
  }
  if (!Array.isArray(response.data.data.tenders)) {
    throw new Error('Tenders data is not an array');
  }
};

const testAuthenticatedEndpoint = async (token) => {
  const response = await axios.get(`${API_BASE}/user/dashboard/stats`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  if (response.status !== 200) {
    throw new Error('Authenticated endpoint failed');
  }
  if (!response.data.success) {
    throw new Error(response.data.message);
  }
};

const testFrontendAccess = async () => {
  try {
    const response = await axios.get(FRONTEND_URL, { timeout: 5000 });
    if (response.status !== 200) {
      throw new Error('Frontend not accessible');
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      throw new Error('Frontend server not running');
    }
    throw error;
  }
};

const runAllTests = async () => {
  console.log('🧪 Running Comprehensive Test Suite\n'.bold.cyan);
  
  // Basic connectivity tests
  console.log('🔌 Connectivity Tests'.bold);
  await runTest('Backend Health Check', testHealthCheck);
  await runTest('Frontend Accessibility', testFrontendAccess);
  
  // API functionality tests
  console.log('\n🔧 API Functionality Tests'.bold);
  await runTest('Public Statistics', testPublicStats);
  await runTest('Auctions List', testAuctionsList);
  await runTest('Tenders List', testTendersList);
  
  // Authentication tests
  console.log('\n🔐 Authentication Tests'.bold);
  await runTest('User Registration', testUserRegistration);
  
  let token;
  try {
    token = await testUserLogin();
    log.success('User Login');
    testResults.passed++;
    testResults.total++;
  } catch (error) {
    log.error(`User Login: ${error.message}`);
    testResults.failed++;
    testResults.total++;
  }
  
  if (token) {
    await runTest('Authenticated Endpoint', () => testAuthenticatedEndpoint(token));
  }
  
  // Results summary
  console.log('\n📊 Test Results Summary'.bold.cyan);
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed}`.green);
  console.log(`Failed: ${testResults.failed}`.red);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed === 0) {
    console.log('\n🎉 All tests passed! The application is ready for comprehensive testing.'.green.bold);
  } else {
    console.log('\n⚠️  Some tests failed. Please check the issues above.'.yellow.bold);
  }
  
  console.log('\n📋 Next Steps:');
  console.log('1. Open http://localhost:3000 in your browser');
  console.log('2. Follow the TESTING_GUIDE.md for manual testing');
  console.log('3. Test all user workflows with the provided test accounts');
};

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  log.error(`Unhandled error: ${error.message}`);
  process.exit(1);
});

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch((error) => {
    log.error(`Test suite failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runAllTests };
