'use client'

import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  ChevronDown, 
  ChevronUp, 
  Search, 
  HelpCircle, 
  Building, 
  MessageCircle,
  Phone,
  Mail
} from 'lucide-react';
import Link from 'next/link';

interface FAQItem {
  id: number;
  question: string;
  answer: string;
  category: string;
}

const faqData: FAQItem[] = [
  {
    id: 1,
    question: "كيف يمكنني إنشاء حساب جديد؟",
    answer: "يمكنك إنشاء حساب جديد من خلال النقر على 'انضم الآن' في الصفحة الرئيسية، ثم اختيار نوع الحساب المناسب (فرد، شركة، أو جهة حكومية) وملء النموذج المطلوب. ستحتاج إلى رفع الوثائق المطلوبة وانتظار موافقة الإدارة خلال 3 أيام عمل.",
    category: "الحساب"
  },
  {
    id: 2,
    question: "كيف يمكنني تفعيل حسابي؟",
    answer: "بعد التسجيل، ستحتاج إلى: 1) تأكيد بريدك الإلكتروني من خلال الرابط المرسل، 2) رفع الوثائق المطلوبة حسب نوع حسابك، 3) انتظار مراجعة الإدارة خلال 3 أيام عمل. ستصلك رسالة بالبريد الإلكتروني عند اكتمال التفعيل.",
    category: "الحساب"
  },
  {
    id: 3,
    question: "ما هي الوثائق المطلوبة للتسجيل؟",
    answer: "للأفراد: الهوية الوطنية أو الإقامة، صورة شخصية، إثبات العنوان. للشركات: السجل التجاري، الرقم الضريبي، هوية المفوض، عقد التأسيس. للجهات الحكومية: خطاب التفويض الرسمي، هوية المفوض، ختم الجهة.",
    category: "الحساب"
  },
  {
    id: 4,
    question: "كيف يمكنني المشاركة في مزاد؟",
    answer: "يجب أن يكون حسابك مفعلاً أولاً. ثم يمكنك تصفح المزادات المتاحة، اختيار المزاد المناسب، قراءة التفاصيل والشروط، وتقديم مزايدتك. يمكنك أيضاً تفعيل المزايدة التلقائية لتوفير الوقت.",
    category: "المزادات"
  },
  {
    id: 5,
    question: "ما هي رسوم المنصة؟",
    answer: "التسجيل والتصفح مجاني تماماً. نحصل على عمولة 2% فقط من قيمة المزاد أو المناقصة الناجحة. لا توجد رسوم خفية أو رسوم شهرية. الدفع يتم فقط عند نجاح المعاملة.",
    category: "الرسوم"
  },
  {
    id: 6,
    question: "كيف يتم الدفع والاستلام؟",
    answer: "نوفر نظام دفع آمن يدعم جميع وسائل الدفع المحلية والدولية. يتم حجز المبلغ عند الفوز بالمزاد وتحويله للبائع بعد تأكيد الاستلام. نوفر أيضاً خدمة الضمان لحماية المشترين والبائعين.",
    category: "الدفع"
  },
  {
    id: 7,
    question: "كيف يمكنني إنشاء مزاد جديد؟",
    answer: "للشركات المفعلة: ادخل إلى لوحة التحكم، اختر 'إنشاء مزاد جديد'، املأ تفاصيل المنتج أو الخدمة، حدد السعر الابتدائي وتاريخ الانتهاء، ارفع الصور والوثائق، ثم اضغط 'نشر المزاد'. سيتم مراجعته قبل النشر.",
    category: "المزادات"
  },
  {
    id: 8,
    question: "كيف يمكنني التقدم لمناقصة حكومية؟",
    answer: "تصفح المناقصات المتاحة، اقرأ التفاصيل والشروط بعناية، حضر الوثائق والمستندات المطلوبة، املأ نموذج التقديم، ارفع جميع المرفقات، وقدم عطاءك قبل انتهاء الموعد المحدد.",
    category: "المناقصات"
  },
  {
    id: 9,
    question: "ماذا لو واجهت مشكلة تقنية؟",
    answer: "يمكنك التواصل مع فريق الدعم الفني عبر: 1) نموذج الدعم في الموقع، 2) البريد الإلكتروني: <EMAIL>، 3) الهاتف: +966 11 123 4567. فريقنا متاح 24/7 لحل جميع المشاكل التقنية.",
    category: "الدعم"
  },
  {
    id: 10,
    question: "كيف يمكنني تغيير كلمة المرور؟",
    answer: "ادخل إلى حسابك، اذهب إلى 'الإعدادات' أو 'الملف الشخصي'، اختر 'تغيير كلمة المرور'، أدخل كلمة المرور الحالية والجديدة، ثم احفظ التغييرات. يمكنك أيضاً استخدام 'نسيت كلمة المرور' من صفحة تسجيل الدخول.",
    category: "الحساب"
  },
  {
    id: 11,
    question: "هل المنصة آمنة؟",
    answer: "نعم، نستخدم أعلى معايير الأمان والتشفير لحماية بياناتك ومعاملاتك. جميع المدفوعات محمية بتشفير SSL، ونتبع معايير الأمان الدولية. كما نوفر نظام ضمان لحماية المشترين والبائعين.",
    category: "الأمان"
  },
  {
    id: 12,
    question: "كيف يمكنني إلغاء مزايدتي؟",
    answer: "يمكن إلغاء المزايدة خلال أول 10 دقائق من تقديمها، بشرط عدم وجود مزايدات أعلى. بعد ذلك، تصبح المزايدة ملزمة. في حالات خاصة، يمكن التواصل مع الدعم الفني لمراجعة الطلب.",
    category: "المزادات"
  }
];

const categories = ["الكل", "الحساب", "المزادات", "المناقصات", "الدفع", "الرسوم", "الأمان", "الدعم"];

export default function FAQPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('الكل');
  const [openItems, setOpenItems] = useState<number[]>([]);

  const filteredFAQs = faqData.filter(item => {
    const matchesSearch = item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'الكل' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Building className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">منصة المزادات</h1>
              <p className="text-sm text-gray-600">والمناقصات</p>
            </div>
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <Badge className="mb-4 bg-blue-100 text-blue-800">الأسئلة الشائعة</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            كيف يمكننا <span className="text-blue-600">مساعدتك</span>؟
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            ابحث عن إجابات للأسئلة الأكثر شيوعاً حول منصتنا وخدماتنا
          </p>

          {/* Search */}
          <div className="max-w-md mx-auto relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="ابحث في الأسئلة الشائعة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-3 text-lg"
            />
          </div>
        </div>

        {/* Categories */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              onClick={() => setSelectedCategory(category)}
              className="mb-2"
            >
              {category}
            </Button>
          ))}
        </div>

        {/* FAQ Items */}
        <div className="max-w-4xl mx-auto space-y-4 mb-12">
          {filteredFAQs.length > 0 ? (
            filteredFAQs.map((item) => (
              <Card key={item.id} className="overflow-hidden">
                <CardHeader 
                  className="cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => toggleItem(item.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-start gap-3">
                      <HelpCircle className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
                      <CardTitle className="text-lg text-right">{item.question}</CardTitle>
                    </div>
                    {openItems.includes(item.id) ? (
                      <ChevronUp className="h-5 w-5 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-500" />
                    )}
                  </div>
                </CardHeader>
                {openItems.includes(item.id) && (
                  <CardContent className="pt-0">
                    <div className="bg-gray-50 rounded-lg p-4 mr-8">
                      <p className="text-gray-700 leading-relaxed">{item.answer}</p>
                      <Badge variant="outline" className="mt-3">
                        {item.category}
                      </Badge>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))
          ) : (
            <Card className="text-center py-12">
              <CardContent>
                <HelpCircle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">لم نجد نتائج</h3>
                <p className="text-gray-600">جرب البحث بكلمات مختلفة أو تصفح الفئات المختلفة</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Contact Support */}
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle className="text-center text-2xl">لم تجد إجابة لسؤالك؟</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center mb-8">
              <p className="text-gray-600 mb-6">
                فريق الدعم الفني جاهز لمساعدتك في أي وقت
              </p>
              
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <MessageCircle className="h-6 w-6 text-blue-600" />
                  </div>
                  <h4 className="font-semibold mb-2">الدردشة المباشرة</h4>
                  <p className="text-sm text-gray-600 mb-3">متاح 24/7</p>
                  <Link href="/support">
                    <Button variant="outline" size="sm">ابدأ المحادثة</Button>
                  </Link>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Phone className="h-6 w-6 text-green-600" />
                  </div>
                  <h4 className="font-semibold mb-2">الهاتف</h4>
                  <p className="text-sm text-gray-600 mb-3">+966 11 123 4567</p>
                  <Button variant="outline" size="sm">اتصل بنا</Button>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Mail className="h-6 w-6 text-purple-600" />
                  </div>
                  <h4 className="font-semibold mb-2">البريد الإلكتروني</h4>
                  <p className="text-sm text-gray-600 mb-3"><EMAIL></p>
                  <Link href="/contact">
                    <Button variant="outline" size="sm">أرسل رسالة</Button>
                  </Link>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
