import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Gavel, 
  Search, 
  Clock, 
  Users, 
  TrendingUp, 
  Star,
  Eye,
  Heart,
  Filter,
  ArrowRight,
  Building,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';

export default function AuctionsLandingPage() {
  // Sample auction data for demonstration
  const featuredAuctions = [
    {
      id: 1,
      title: "سيارة BMW X5 موديل 2020",
      description: "سيارة فاخرة بحالة ممتازة، قطعت 45,000 كم فقط",
      currentBid: 180000,
      startingBid: 150000,
      endTime: "2024-12-20T15:00:00Z",
      category: "سيارات",
      bidsCount: 23,
      image: "/api/placeholder/300/200",
      featured: true
    },
    {
      id: 2,
      title: "لوحة فنية أصلية للفنان محمد الشمراني",
      description: "لوحة زيتية نادرة من مجموعة خاصة",
      currentBid: 25000,
      startingBid: 15000,
      endTime: "2024-12-18T20:00:00Z",
      category: "فنون",
      bidsCount: 12,
      image: "/api/placeholder/300/200",
      featured: true
    },
    {
      id: 3,
      title: "ساعة رولكس أصلية",
      description: "ساعة رولكس سابمارينر بحالة ممتازة مع الضمان",
      currentBid: 45000,
      startingBid: 35000,
      endTime: "2024-12-19T18:00:00Z",
      category: "مجوهرات",
      bidsCount: 18,
      image: "/api/placeholder/300/200",
      featured: true
    }
  ];

  const categories = [
    { name: "سيارات", count: 45, icon: "🚗" },
    { name: "إلكترونيات", count: 32, icon: "📱" },
    { name: "مجوهرات", count: 28, icon: "💎" },
    { name: "فنون", count: 15, icon: "🎨" },
    { name: "أثاث", count: 22, icon: "🪑" },
    { name: "عقارات", count: 8, icon: "🏠" }
  ];

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(price);
  };

  const formatTimeRemaining = (endTime: string) => {
    const now = new Date();
    const end = new Date(endTime);
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return 'انتهى';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) return `${days} يوم`;
    return `${hours} ساعة`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Building className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">منصة المزادات</h1>
              <p className="text-sm text-gray-600">والمناقصات</p>
            </div>
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-blue-100 text-blue-800">المزادات</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            اكتشف <span className="text-blue-600">أفضل المزادات</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            شارك في مزادات متنوعة واحصل على أفضل الصفقات من سيارات وإلكترونيات ومجوهرات وأكثر
          </p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative mb-8">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="ابحث عن المزادات..."
              className="pl-12 pr-4 py-4 text-lg rounded-full border-2 border-gray-200 focus:border-blue-500"
            />
            <Button className="absolute right-2 top-1/2 transform -translate-y-1/2 rounded-full">
              <Search className="h-4 w-4" />
            </Button>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">150+</div>
              <div className="text-sm text-gray-600">مزاد نشط</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">1,200+</div>
              <div className="text-sm text-gray-600">مزايد نشط</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">50M+</div>
              <div className="text-sm text-gray-600">ريال تداول</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600">95%</div>
              <div className="text-sm text-gray-600">معدل الرضا</div>
            </div>
          </div>
        </div>

        {/* Categories */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">تصفح حسب الفئة</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {categories.map((category, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6 text-center">
                  <div className="text-4xl mb-3">{category.icon}</div>
                  <h3 className="font-semibold text-gray-900 mb-1">{category.name}</h3>
                  <p className="text-sm text-gray-600">{category.count} مزاد</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Featured Auctions */}
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-gray-900">المزادات المميزة</h2>
            <Button variant="outline">
              عرض الكل
              <ArrowRight className="h-4 w-4 mr-2" />
            </Button>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredAuctions.map((auction) => (
              <Card key={auction.id} className="overflow-hidden hover:shadow-xl transition-all duration-300">
                <div className="aspect-video bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                  <Gavel className="h-12 w-12 text-gray-400" />
                </div>
                
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge className="bg-red-100 text-red-800">مميز</Badge>
                    <Badge variant="outline">{auction.category}</Badge>
                  </div>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
                    {auction.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {auction.description}
                  </p>
                  
                  <div className="space-y-3 mb-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">السعر الحالي:</span>
                      <span className="text-lg font-bold text-green-600">
                        {formatPrice(auction.currentBid)}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">يبدأ من:</span>
                      <span className="text-sm text-gray-500">
                        {formatPrice(auction.startingBid)}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {formatTimeRemaining(auction.endTime)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {auction.bidsCount} مزايدة
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button className="flex-1">
                      <Gavel className="h-4 w-4 ml-2" />
                      زايد الآن
                    </Button>
                    <Button variant="outline" size="icon">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* How It Works */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">كيف تعمل المزادات؟</h2>
          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">1</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">سجل حسابك</h3>
              <p className="text-gray-600 text-sm">أنشئ حساب مجاني وأكمل عملية التحقق</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">2</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">اختر المزاد</h3>
              <p className="text-gray-600 text-sm">تصفح المزادات واختر ما يناسبك</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">قدم مزايدتك</h3>
              <p className="text-gray-600 text-sm">ضع مزايدتك وتابع المنافسة</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-orange-600">4</span>
              </div>
              <h3 className="text-lg font-semibold mb-2">اربح واستلم</h3>
              <p className="text-gray-600 text-sm">ادفع واستلم مشترياتك بأمان</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-12 text-white">
          <h2 className="text-3xl font-bold mb-4">جاهز للمشاركة في المزادات؟</h2>
          <p className="text-xl mb-8 opacity-90">
            انضم إلى آلاف المزايدين واحصل على أفضل الصفقات
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/register">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                ابدأ المزايدة الآن
              </Button>
            </Link>
            <Link href="/user/auctions">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                تصفح المزادات
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
