'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useToast } from '@/components/ui/use-toast'
import api from '@/lib/api'
import {
  ArrowLeft,
  FileText,
  Calendar,
  DollarSign,
  User,
  Building,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  MapPin,
  Users,
  Download,
  Eye,
  Award
} from 'lucide-react'

interface Tender {
  _id: string
  title: string
  description: string
  category: string
  organizer: {
    _id: string
    profile: {
      fullName?: string
      companyName?: string
      governmentEntity?: string
    }
    role: string
    email: string
  }
  budget: number
  startDate: string
  deadline: string
  status: 'open' | 'closed' | 'awarded'
  location: {
    city?: string
    region?: string
    country?: string
  }
  requirements: string[]
  proposals: Proposal[]
  awardedTo?: {
    _id: string
    profile: {
      fullName?: string
      companyName?: string
    }
  }
  awardedProposal?: string
  createdAt: string
  updatedAt: string
}

interface Proposal {
  _id: string
  proposer: {
    _id: string
    profile: {
      fullName?: string
      companyName?: string
    }
    email: string
  }
  amount: number
  proposal: string
  documents: Array<{
    name: string
    type: string
    url: string
    uploadedAt: string
    verified: boolean
  }>
  submittedAt: string
  status: 'pending' | 'accepted' | 'rejected'
}

export default function AdminTenderDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const tenderId = params.id as string

  const [tender, setTender] = useState<Tender | null>(null)
  const [loading, setLoading] = useState(true)
  const [submissions, setSubmissions] = useState<any[]>([])
  const [submissionsLoading, setSubmissionsLoading] = useState(false)

  useEffect(() => {
    if (tenderId) {
      fetchTender()
    }
  }, [tenderId])

  const fetchTender = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/admin/tenders/${tenderId}`)

      if (response.data.success) {
        setTender(response.data.data)
      }
    } catch (error) {
      console.error('Error fetching tender:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'فشل في تحميل تفاصيل المناقصة',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchSubmissions = async () => {
    try {
      setSubmissionsLoading(true);
      const response = await api.get(`/admin/tenders/${tenderId}/submissions`);
      setSubmissions(response.data.submissions || response.data);
    } catch (error) {
      console.error('Error fetching submissions:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل مقدمات المناقصة",
        variant: "destructive",
      });
    } finally {
      setSubmissionsLoading(false);
    }
  };

  const handleStatusChange = async (newStatus: 'open' | 'closed' | 'awarded') => {
    try {
      await api.put(`/admin/tenders/${tenderId}/status`, { status: newStatus });
      setTender(prev => prev ? { ...prev, status: newStatus } : null);
      toast({
        title: "نجح",
        description: `تم تحديث حالة المناقصة إلى ${newStatus}`,
      });
    } catch (error) {
      console.error('Error updating tender status:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحديث حالة المناقصة",
        variant: "destructive",
      });
    }
  };

  const handleSubmissionStatusChange = async (submissionId: string, newStatus: 'approved' | 'rejected') => {
    try {
      await api.put(`/admin/tenders/${tenderId}/submissions/${submissionId}/status`, { status: newStatus });
      setSubmissions(prev => prev.map(sub => 
        sub.id === submissionId ? { ...sub, status: newStatus } : sub
      ));
      toast({
        title: "نجح",
        description: `تم تحديث حالة التقديم إلى ${newStatus}`,
      });
    } catch (error) {
      console.error('Error updating submission status:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحديث حالة التقديم",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/3 mb-4" />
          <div className="h-4 bg-gray-300 rounded w-1/2 mb-8" />
          <div className="grid gap-4">
            <div className="h-32 bg-gray-300 rounded" />
            <div className="h-32 bg-gray-300 rounded" />
          </div>
        </div>
      </div>
    );
  }

  if (!tender) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </div>
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-gray-500">Tender not found</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <Button variant="ghost" onClick={() => router.back()}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Tender Details</h1>
      </div>

      <div className="grid gap-6">
        {/* Tender Information */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-xl">{tender.title}</CardTitle>
                <CardDescription className="mt-2">
                  {tender.organizer.profile.governmentEntity || tender.organizer.profile.companyName || 'منظم'} • {tender.category}
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge 
                  variant={
                    tender.status === 'open' ? 'default' :
                    tender.status === 'closed' ? 'secondary' :
                    tender.status === 'awarded' ? 'destructive' : 'outline'
                  }
                >
                  {tender.status}
                </Badge>
                {tender.status === 'open' && (
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleStatusChange('closed')}
                    >
                      Close Tender
                    </Button>
                    <Button 
                      variant="destructive" 
                      size="sm"
                      onClick={() => handleStatusChange('awarded')}
                    >
                      Award
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-2">Description</h3>
                <p className="text-sm text-gray-600 mb-4">{tender.description}</p>
                
                <h3 className="font-semibold mb-2">Requirements</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  {tender.requirements.map((req, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-blue-500">•</span>
                      {req}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-green-500" />
                  <span className="font-semibold">Budget: ${tender.budget.toLocaleString()}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-blue-500" />
                  <span>Start: {new Date(tender.startDate).toLocaleDateString()}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-orange-500" />
                  <span>End: {new Date(tender.deadline).toLocaleDateString()}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-purple-500" />
                  <span>Created by: {tender.organizer.profile.fullName || tender.organizer.profile.companyName || tender.organizer.email}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Building className="w-4 h-4 text-gray-500" />
                  <span>Department: {tender.organizer.profile.governmentEntity || 'N/A'}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-blue-500" />
                  <span>Submissions: {tender.proposals.length}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submissions */}
        <Card>
          <CardHeader>
            <CardTitle>Submissions ({submissions.length})</CardTitle>
            <CardDescription>
              Companies that have submitted proposals for this tender
            </CardDescription>
          </CardHeader>
          <CardContent>
            {submissionsLoading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse border rounded-lg p-4">
                    <div className="h-4 bg-gray-300 rounded w-1/3 mb-2" />
                    <div className="h-3 bg-gray-300 rounded w-1/2" />
                  </div>
                ))}
              </div>
            ) : submissions.length === 0 ? (
              <p className="text-gray-500 text-center py-8">No submissions yet</p>
            ) : (
              <div className="space-y-4">
                {submissions.map((submission) => (
                  <div key={submission.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-semibold">{submission.companyName}</h4>
                        <p className="text-sm text-gray-600">{submission.contactEmail}</p>
                        <p className="text-sm text-gray-600">{submission.contactPhone}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-lg text-green-600">
                          ${submission.proposedAmount.toLocaleString()}
                        </p>
                        <Badge 
                          variant={
                            submission.status === 'approved' ? 'default' : 
                            submission.status === 'rejected' ? 'destructive' : 'secondary'
                          }
                        >
                          {submission.status}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center mt-4">
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <Calendar className="w-4 h-4" />
                        Submitted: {new Date(submission.submittedAt).toLocaleDateString()}
                      </div>
                      
                      {submission.status === 'pending' && (
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleSubmissionStatusChange(submission.id, 'approved')}
                          >
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Approve
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleSubmissionStatusChange(submission.id, 'rejected')}
                          >
                            <XCircle className="w-4 h-4 mr-2" />
                            Reject
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
