'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { adminAPI } from '@/lib/api';
import { toast } from 'sonner';
import { UserCheck, UserX, Mail, Phone, MapPin, Calendar, Edit } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface User {
  _id: string;
  email: string;
  role: 'individual' | 'company' | 'government' | 'admin' | 'super_admin';
  status: 'approved' | 'pending' | 'suspended' | 'pending_email_verification' | 'documents_submitted' | 'under_review';
  profile: {
    fullName?: string;
    companyName?: string;
    governmentEntity?: string;
    phone?: string;
    address?: string;
  };
  createdAt: string;
  lastLogin?: string;
  emailVerified?: boolean;
}

export default function AdminUsersPage() {
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'approved' | 'pending' | 'suspended'>('all');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.users.getAll();


      // Correct API response structure: {success: true, data: {users: [...], pagination: {...}}}
      if (response.data.success && response.data.data && response.data.data.users) {
        setUsers(response.data.data.users);
      } else {
        console.error('Unexpected API response structure:', response.data);
        setUsers([]);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to fetch users');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleUserStatusChange = async (userId: string, newStatus: 'approved' | 'suspended') => {
    try {
      if (newStatus === 'approved') {
        await adminAPI.users.activate(userId);
      } else {
        await adminAPI.users.deactivate(userId);
      }
      setUsers(users.map(user =>
        user._id === userId ? { ...user, status: newStatus } : user
      ));
      toast.success(`User ${newStatus === 'approved' ? 'activated' : 'suspended'} successfully`);
    } catch (error) {
      console.error('Error updating user status:', error);
      toast.error('Failed to update user status');
    }
  };

  const filteredUsers = users.filter(user => {
    if (filter === 'all') return true;
    return user.status === filter;
  });

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">User Management</h1>
        <div className="grid gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-300 rounded-full" />
                  <div className="flex-1">
                    <div className="h-4 bg-gray-300 rounded w-1/4 mb-2" />
                    <div className="h-3 bg-gray-300 rounded w-1/3" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">User Management</h1>
        <div className="flex gap-2">
          <Button
            variant={filter === 'all' ? 'default' : 'outline'}
            onClick={() => setFilter('all')}
          >
            All Users
          </Button>
          <Button
            variant={filter === 'approved' ? 'default' : 'outline'}
            onClick={() => setFilter('approved')}
          >
            Approved
          </Button>
          <Button
            variant={filter === 'pending' ? 'default' : 'outline'}
            onClick={() => setFilter('pending')}
          >
            Pending
          </Button>
          <Button
            variant={filter === 'suspended' ? 'default' : 'outline'}
            onClick={() => setFilter('suspended')}
          >
            Suspended
          </Button>
        </div>
      </div>

      <div className="grid gap-4">
        {filteredUsers.map((user) => (
          <Card key={user._id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.profile?.fullName || user.profile?.companyName || user.email}`} />
                    <AvatarFallback>
                      {(user.profile?.fullName || user.profile?.companyName || user.email).charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg">
                      {user.profile?.fullName || user.profile?.companyName || user.profile?.governmentEntity || 'No Name'}
                    </CardTitle>
                    <CardDescription className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <Mail className="w-4 h-4" />
                        {user.email}
                      </span>
                      {user.profile?.phone && (
                        <span className="flex items-center gap-1">
                          <Phone className="w-4 h-4" />
                          {user.profile.phone}
                        </span>
                      )}
                    </CardDescription>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant={
                      user.status === 'approved' ? 'default' :
                      user.status === 'pending' || user.status === 'pending_email_verification' || user.status === 'documents_submitted' || user.status === 'under_review' ? 'secondary' :
                      'destructive'
                    }
                  >
                    {user.status === 'pending_email_verification' ? 'Email Pending' :
                     user.status === 'documents_submitted' ? 'Docs Submitted' :
                     user.status === 'under_review' ? 'Under Review' :
                     user.status}
                  </Badge>
                  <Badge variant="outline">
                    {user.role}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  {user.profile?.address && (
                    <span className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      {user.profile.address}
                    </span>
                  )}
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    Joined {new Date(user.createdAt).toLocaleDateString()}
                  </span>
                  {user.lastLogin && (
                    <span className="flex items-center gap-1">
                      Last login: {new Date(user.lastLogin).toLocaleDateString()}
                    </span>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push(`/admin/users/${user._id}/edit`)}
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                  {user.status === 'approved' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUserStatusChange(user._id, 'suspended')}
                    >
                      <UserX className="w-4 h-4 mr-2" />
                      Suspend
                    </Button>
                  )}
                  {user.status === 'suspended' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUserStatusChange(user._id, 'approved')}
                    >
                      <UserCheck className="w-4 h-4 mr-2" />
                      Activate
                    </Button>
                  )}
                  {(user.status === 'pending' || user.status === 'pending_email_verification' || user.status === 'documents_submitted' || user.status === 'under_review') && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUserStatusChange(user._id, 'approved')}
                    >
                      <UserCheck className="w-4 h-4 mr-2" />
                      Approve
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredUsers.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-gray-500">No users found for the selected filter.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
