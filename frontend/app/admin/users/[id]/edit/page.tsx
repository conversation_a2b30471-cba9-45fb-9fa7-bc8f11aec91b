'use client'

import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import DashboardLayout from '@/components/DashboardLayout'
import { useToast } from '@/components/ui/use-toast'
import { ArrowLeft, Save, X, User, Mail, Phone, MapPin } from 'lucide-react'
import { adminAPI } from '@/lib/api'

interface UserFormData {
  email: string
  role: string
  status: string
  profile: {
    fullName: string
    companyName: string
    governmentEntity: string
    phone: string
    address: string
    nationalId: string
  }
  emailVerified: boolean
}

export default function EditUserPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [formData, setFormData] = useState<UserFormData>({
    email: '',
    role: '',
    status: '',
    profile: {
      fullName: '',
      companyName: '',
      governmentEntity: '',
      phone: '',
      address: '',
      nationalId: ''
    },
    emailVerified: false
  })

  useEffect(() => {
    loadUser()
  }, [params.id])

  const loadUser = async () => {
    try {
      setLoading(true)
      const response = await adminAPI.users.getById(params.id as string)
      
      if (response.data.success) {
        const userData = response.data.data
        setUser(userData)
        
        setFormData({
          email: userData.email || '',
          role: userData.role || '',
          status: userData.status || '',
          profile: {
            fullName: userData.profile?.fullName || '',
            companyName: userData.profile?.companyName || '',
            governmentEntity: userData.profile?.governmentEntity || '',
            phone: userData.profile?.phone || '',
            address: userData.profile?.address || '',
            nationalId: userData.profile?.nationalId || ''
          },
          emailVerified: userData.emailVerified || false
        })
      } else {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المستخدم',
          variant: 'destructive'
        })
        router.push('/admin/users')
      }
    } catch (error) {
      console.error('Error loading user:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات المستخدم',
        variant: 'destructive'
      })
      router.push('/admin/users')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    if (field.startsWith('profile.')) {
      const profileField = field.replace('profile.', '')
      setFormData(prev => ({
        ...prev,
        profile: {
          ...prev.profile,
          [profileField]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.email || !formData.role || !formData.status) {
      toast({
        title: 'بيانات ناقصة',
        description: 'يرجى ملء جميع الحقول المطلوبة',
        variant: 'destructive'
      })
      return
    }

    try {
      setSaving(true)
      const response = await adminAPI.users.update(params.id as string, formData)
      
      if (response.data.success) {
        toast({
          title: 'تم التحديث',
          description: 'تم تحديث بيانات المستخدم بنجاح'
        })
        router.push('/admin/users')
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في التحديث',
        description: error.response?.data?.message || 'حدث خطأ في تحديث بيانات المستخدم',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    router.push('/admin/users')
  }

  if (loading) {
    return (
      <DashboardLayout allowedRoles={['admin', 'super_admin']}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات المستخدم...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout allowedRoles={['admin', 'super_admin']}>
      <div className="space-y-6">
        <header className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => router.push('/admin/users')}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold">تعديل المستخدم</h1>
              <p className="text-muted-foreground">تحديث بيانات المستخدم</p>
            </div>
          </div>
        </header>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              بيانات المستخدم
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="email">البريد الإلكتروني *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="أدخل البريد الإلكتروني"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">الدور *</Label>
                  <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الدور" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="individual">فرد</SelectItem>
                      <SelectItem value="company">شركة</SelectItem>
                      <SelectItem value="government">جهة حكومية</SelectItem>
                      <SelectItem value="admin">مدير</SelectItem>
                      <SelectItem value="super_admin">مدير عام</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">الحالة *</Label>
                  <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الحالة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="approved">مفعل</SelectItem>
                      <SelectItem value="pending">في الانتظار</SelectItem>
                      <SelectItem value="suspended">معلق</SelectItem>
                      <SelectItem value="pending_email_verification">في انتظار تأكيد البريد</SelectItem>
                      <SelectItem value="documents_submitted">تم تقديم الوثائق</SelectItem>
                      <SelectItem value="under_review">قيد المراجعة</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="emailVerified"
                      checked={formData.emailVerified}
                      onCheckedChange={(checked) => handleInputChange('emailVerified', checked as boolean)}
                    />
                    <Label htmlFor="emailVerified">البريد الإلكتروني مؤكد</Label>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">الملف الشخصي</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="fullName">الاسم الكامل</Label>
                    <Input
                      id="fullName"
                      value={formData.profile.fullName}
                      onChange={(e) => handleInputChange('profile.fullName', e.target.value)}
                      placeholder="أدخل الاسم الكامل"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">رقم الهاتف</Label>
                    <Input
                      id="phone"
                      value={formData.profile.phone}
                      onChange={(e) => handleInputChange('profile.phone', e.target.value)}
                      placeholder="أدخل رقم الهاتف"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="nationalId">رقم الهوية</Label>
                    <Input
                      id="nationalId"
                      value={formData.profile.nationalId}
                      onChange={(e) => handleInputChange('profile.nationalId', e.target.value)}
                      placeholder="أدخل رقم الهوية"
                    />
                  </div>

                  {formData.role === 'company' && (
                    <div className="space-y-2">
                      <Label htmlFor="companyName">اسم الشركة</Label>
                      <Input
                        id="companyName"
                        value={formData.profile.companyName}
                        onChange={(e) => handleInputChange('profile.companyName', e.target.value)}
                        placeholder="أدخل اسم الشركة"
                      />
                    </div>
                  )}

                  {formData.role === 'government' && (
                    <div className="space-y-2">
                      <Label htmlFor="governmentEntity">الجهة الحكومية</Label>
                      <Input
                        id="governmentEntity"
                        value={formData.profile.governmentEntity}
                        onChange={(e) => handleInputChange('profile.governmentEntity', e.target.value)}
                        placeholder="أدخل اسم الجهة الحكومية"
                      />
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">العنوان</Label>
                  <Textarea
                    id="address"
                    value={formData.profile.address}
                    onChange={(e) => handleInputChange('profile.address', e.target.value)}
                    placeholder="أدخل العنوان"
                    rows={3}
                  />
                </div>
              </div>

              <div className="flex gap-4 pt-4">
                <Button type="submit" disabled={saving}>
                  <Save className="h-4 w-4 ml-2" />
                  {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                </Button>
                <Button type="button" variant="outline" onClick={handleCancel}>
                  <X className="h-4 w-4 ml-2" />
                  إلغاء
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
