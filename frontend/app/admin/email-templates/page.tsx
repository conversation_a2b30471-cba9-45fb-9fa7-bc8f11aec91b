'use client'

import { useState, useEffect } from 'react'

interface EmailTemplate {
  id: string
  name: string
  subject: string
  body: string
  variables: string[]
  type: 'welcome' | 'auction' | 'tender' | 'payment' | 'notification'
  active: boolean
  lastUpdated: string
}

const defaultTemplates: EmailTemplate[] = [
  {
    id: '1',
    name: 'Welcome Email',
    subject: 'Welcome to {{siteName}}',
    body: `Dear {{userName}},

Welcome to {{siteName}}! We're excited to have you join our auction and tender platform.

Your account has been created successfully. You can now:
- Browse and participate in auctions
- Submit bids on tenders
- Track your activity in your dashboard

If you have any questions, please don't hesitate to contact our support team.

Best regards,
The {{siteName}} Team`,
    variables: ['userName', 'siteName'],
    type: 'welcome',
    active: true,
    lastUpdated: '2024-01-15'
  },
  {
    id: '2',
    name: 'Auction Ending Soon',
    subject: 'Auction ending soon: {{auctionTitle}}',
    body: `Dear {{userName}},

The auction "{{auctionTitle}}" is ending soon!

Current highest bid: {{currentBid}}
Auction ends: {{endTime}}

Don't miss your chance to place a bid.

View Auction: {{auctionLink}}

Best regards,
The {{siteName}} Team`,
    variables: ['userName', 'auctionTitle', 'currentBid', 'endTime', 'auctionLink', 'siteName'],
    type: 'auction',
    active: true,
    lastUpdated: '2024-01-15'
  },
  {
    id: '3',
    name: 'Tender Submitted',
    subject: 'Your tender has been submitted: {{tenderTitle}}',
    body: `Dear {{userName}},

Your tender submission for "{{tenderTitle}}" has been received successfully.

Tender Details:
- Title: {{tenderTitle}}
- Submission Date: {{submissionDate}}
- Tender ID: {{tenderId}}

You will be notified when the tender evaluation is complete.

Best regards,
The {{siteName}} Team`,
    variables: ['userName', 'tenderTitle', 'submissionDate', 'tenderId', 'siteName'],
    type: 'tender',
    active: true,
    lastUpdated: '2024-01-15'
  },
  {
    id: '4',
    name: 'Payment Confirmation',
    subject: 'Payment confirmation for {{itemTitle}}',
    body: `Dear {{userName}},

Your payment has been processed successfully.

Payment Details:
- Item: {{itemTitle}}
- Amount: {{amount}}
- Payment Date: {{paymentDate}}
- Transaction ID: {{transactionId}}

Thank you for your business!

Best regards,
The {{siteName}} Team`,
    variables: ['userName', 'itemTitle', 'amount', 'paymentDate', 'transactionId', 'siteName'],
    type: 'payment',
    active: true,
    lastUpdated: '2024-01-15'
  }
]

export default function EmailTemplatesPage() {
  const [templates, setTemplates] = useState<EmailTemplate[]>(defaultTemplates)
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')

  const handleSave = async (template: EmailTemplate) => {
    setLoading(true)
    setMessage('')

    try {
      const response = await fetch(`/api/templates/${template.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: template.name,
          subject: template.subject,
          content: template.content,
          active: template.active
        })
      })

      const data = await response.json()

      if (data.success) {
        const updatedTemplates = templates.map(t =>
          t.id === template.id ? { ...template, lastUpdated: new Date().toISOString().split('T')[0] } : t
        )
        setTemplates(updatedTemplates)
        setSelectedTemplate(null)
        setIsEditing(false)
        setMessage('Template saved successfully!')
      } else {
        setMessage('Error saving template: ' + data.message)
      }
    } catch (error) {
      setMessage('Error saving template')
    } finally {
      setLoading(false)
    }
  }

  const handleToggleActive = async (templateId: string) => {
    const updatedTemplates = templates.map(t => 
      t.id === templateId ? { ...t, active: !t.active } : t
    )
    setTemplates(updatedTemplates)
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'welcome': return 'bg-green-100 text-green-800'
      case 'auction': return 'bg-blue-100 text-blue-800'
      case 'tender': return 'bg-purple-100 text-purple-800'
      case 'payment': return 'bg-yellow-100 text-yellow-800'
      case 'notification': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Email Templates</h1>
          <button
            onClick={() => {
              setSelectedTemplate({
                id: Date.now().toString(),
                name: '',
                subject: '',
                body: '',
                variables: [],
                type: 'notification',
                active: true,
                lastUpdated: new Date().toISOString().split('T')[0]
              })
              setIsEditing(true)
            }}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Create New Template
          </button>
        </div>

        {message && (
          <div className={`mb-4 p-4 rounded-md ${
            message.includes('successfully') 
              ? 'bg-green-50 text-green-700 border border-green-200' 
              : 'bg-red-50 text-red-700 border border-red-200'
          }`}>
            {message}
          </div>
        )}

        {!selectedTemplate ? (
          <div className="grid grid-cols-1 gap-4">
            {templates.map((template) => (
              <div key={template.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{template.name}</h3>
                    <p className="text-sm text-gray-500 mt-1">{template.subject}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(template.type)}`}>
                      {template.type}
                    </span>
                    <button
                      onClick={() => handleToggleActive(template.id)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        template.active ? 'bg-green-600' : 'bg-gray-200'
                      }`}
                    >
                      <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        template.active ? 'translate-x-6' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>
                </div>
                
                <div className="text-sm text-gray-600 mb-3">
                  <strong>Variables:</strong> {template.variables.join(', ')}
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">
                    Last updated: {template.lastUpdated}
                  </span>
                  <button
                    onClick={() => {
                      setSelectedTemplate(template)
                      setIsEditing(true)
                    }}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    Edit Template
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">
                {isEditing ? 'Edit Template' : 'View Template'}
              </h2>
              <div className="space-x-2">
                <button
                  onClick={() => {
                    setSelectedTemplate(null)
                    setIsEditing(false)
                  }}
                  className="text-gray-600 hover:text-gray-800"
                >
                  Cancel
                </button>
                {isEditing && (
                  <button
                    onClick={() => handleSave(selectedTemplate)}
                    disabled={loading}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    {loading ? 'Saving...' : 'Save Template'}
                  </button>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template Name
                </label>
                <input
                  type="text"
                  value={selectedTemplate.name}
                  onChange={(e) => setSelectedTemplate({ ...selectedTemplate, name: e.target.value })}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Template Type
                </label>
                <select
                  value={selectedTemplate.type}
                  onChange={(e) => setSelectedTemplate({ ...selectedTemplate, type: e.target.value as any })}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50"
                >
                  <option value="welcome">Welcome</option>
                  <option value="auction">Auction</option>
                  <option value="tender">Tender</option>
                  <option value="payment">Payment</option>
                  <option value="notification">Notification</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Subject
              </label>
              <input
                type="text"
                value={selectedTemplate.subject}
                onChange={(e) => setSelectedTemplate({ ...selectedTemplate, subject: e.target.value })}
                disabled={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Body
              </label>
              <textarea
                value={selectedTemplate.body}
                onChange={(e) => setSelectedTemplate({ ...selectedTemplate, body: e.target.value })}
                disabled={!isEditing}
                rows={12}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Available Variables
              </label>
              <div className="bg-gray-50 rounded-md p-3">
                <p className="text-sm text-gray-600 mb-2">
                  Use these variables in your template by wrapping them in double curly braces:
                </p>
                <div className="flex flex-wrap gap-2">
                  {selectedTemplate.variables.map((variable) => (
                    <span
                      key={variable}
                      className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-mono"
                    >
                      {`{{${variable}}}`}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
