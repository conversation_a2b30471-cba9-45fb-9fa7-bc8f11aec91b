import React from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Building, 
  FileText, 
  Shield, 
  AlertTriangle, 
  CheckCircle,
  Scale,
  Clock,
  Users
} from 'lucide-react';
import Link from 'next/link';

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Building className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">منصة المزادات</h1>
              <p className="text-sm text-gray-600">والمناقصات</p>
            </div>
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <Badge className="mb-4 bg-blue-100 text-blue-800">الشروط والأحكام</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            الشروط والأحكام <span className="text-blue-600">العامة</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            يرجى قراءة هذه الشروط والأحكام بعناية قبل استخدام منصتنا
          </p>
          <div className="flex items-center justify-center gap-2 mt-4 text-sm text-gray-500">
            <Clock className="h-4 w-4" />
            <span>آخر تحديث: ديسمبر 2024</span>
          </div>
        </div>

        {/* Terms Content */}
        <div className="max-w-4xl mx-auto space-y-8">
          
          {/* Introduction */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-600" />
                مقدمة
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed">
                مرحباً بك في منصة المزادات والمناقصات. هذه الشروط والأحكام ("الشروط") تحكم استخدامك 
                لموقعنا الإلكتروني وخدماتنا ("الخدمة") التي تديرها شركة منصة المزادات والمناقصات ("نحن"، "لنا"، أو "الشركة").
              </p>
              <p className="text-gray-700 leading-relaxed">
                باستخدام خدمتنا، فإنك توافق على الالتزام بهذه الشروط. إذا كنت لا توافق على أي جزء من هذه الشروط، 
                فلا يجوز لك الوصول إلى الخدمة أو استخدامها.
              </p>
            </CardContent>
          </Card>

          {/* User Accounts */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-green-600" />
                حسابات المستخدمين
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">التسجيل والتفعيل</h4>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-1 flex-shrink-0" />
                    يجب أن تكون 18 سنة أو أكثر لإنشاء حساب
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-1 flex-shrink-0" />
                    يجب تقديم معلومات صحيحة ومحدثة عند التسجيل
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-1 flex-shrink-0" />
                    رفع الوثائق المطلوبة حسب نوع الحساب
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-1 flex-shrink-0" />
                    انتظار موافقة الإدارة خلال 3 أيام عمل
                  </li>
                </ul>
              </div>
              
              <Separator />
              
              <div>
                <h4 className="font-semibold mb-2">مسؤوليات المستخدم</h4>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="h-4 w-4 text-orange-600 mt-1 flex-shrink-0" />
                    الحفاظ على سرية بيانات تسجيل الدخول
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="h-4 w-4 text-orange-600 mt-1 flex-shrink-0" />
                    عدم مشاركة الحساب مع أطراف أخرى
                  </li>
                  <li className="flex items-start gap-2">
                    <AlertTriangle className="h-4 w-4 text-orange-600 mt-1 flex-shrink-0" />
                    إبلاغنا فوراً عن أي استخدام غير مصرح به
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Platform Usage */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-purple-600" />
                استخدام المنصة
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">الاستخدام المسموح</h4>
                <ul className="space-y-2 text-gray-700">
                  <li>• المشاركة في المزادات والمناقصات بحسن نية</li>
                  <li>• إنشاء مزادات لمنتجات وخدمات قانونية</li>
                  <li>• التواصل المهذب مع المستخدمين الآخرين</li>
                  <li>• الالتزام بجميع القوانين المحلية والدولية</li>
                </ul>
              </div>
              
              <Separator />
              
              <div>
                <h4 className="font-semibold mb-2">الاستخدام المحظور</h4>
                <ul className="space-y-2 text-gray-700">
                  <li>• بيع أو عرض منتجات غير قانونية أو ضارة</li>
                  <li>• التلاعب في المزادات أو المناقصات</li>
                  <li>• انتحال شخصية أو تقديم معلومات كاذبة</li>
                  <li>• استخدام المنصة لأغراض احتيالية</li>
                  <li>• إرسال رسائل غير مرغوب فيها أو محتوى ضار</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Auctions and Tenders */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Scale className="h-5 w-5 text-blue-600" />
                المزادات والمناقصات
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">قواعد المزادات</h4>
                <ul className="space-y-2 text-gray-700">
                  <li>• جميع المزايدات ملزمة ولا يمكن إلغاؤها بعد 10 دقائق</li>
                  <li>• يجب الدفع خلال 24 ساعة من الفوز بالمزاد</li>
                  <li>• البائع ملزم بتسليم المنتج كما هو موصوف</li>
                  <li>• المنصة تحتفظ بحق إلغاء المزادات المشبوهة</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">قواعد المناقصات</h4>
                <ul className="space-y-2 text-gray-700">
                  <li>• جميع العطاءات سرية حتى انتهاء الموعد المحدد</li>
                  <li>• لا يمكن تعديل العطاء بعد تقديمه</li>
                  <li>• الجهة المناقصة تحتفظ بحق رفض أي عطاء</li>
                  <li>• يجب الالتزام بجميع الشروط والمواصفات</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Payments and Fees */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-green-600" />
                المدفوعات والرسوم
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">هيكل الرسوم</h4>
                <ul className="space-y-2 text-gray-700">
                  <li>• التسجيل والتصفح مجاني تماماً</li>
                  <li>• عمولة 2% على المعاملات الناجحة فقط</li>
                  <li>• لا توجد رسوم خفية أو رسوم شهرية</li>
                  <li>• رسوم الدفع تختلف حسب وسيلة الدفع المختارة</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">سياسة الاسترداد</h4>
                <ul className="space-y-2 text-gray-700">
                  <li>• استرداد كامل في حالة عدم تسليم المنتج</li>
                  <li>• استرداد جزئي في حالة عدم مطابقة الوصف</li>
                  <li>• لا استرداد للعمولات المدفوعة للمنصة</li>
                  <li>• فترة الاستلام والمراجعة 7 أيام</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Liability and Disclaimers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
                المسؤولية وإخلاء المسؤولية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">مسؤولية المنصة</h4>
                <p className="text-gray-700 leading-relaxed">
                  المنصة تعمل كوسيط بين المشترين والبائعين. نحن لا نضمن جودة أو صحة أو قانونية 
                  المنتجات أو الخدمات المعروضة. مسؤوليتنا محدودة بتوفير منصة آمنة وموثوقة للتجارة الإلكترونية.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">مسؤولية المستخدمين</h4>
                <p className="text-gray-700 leading-relaxed">
                  المستخدمون مسؤولون بالكامل عن جميع المعاملات والتفاعلات التي يقومون بها على المنصة. 
                  يجب التحقق من صحة المعلومات والالتزام بجميع القوانين المعمول بها.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Privacy and Data */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-blue-600" />
                الخصوصية والبيانات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed mb-4">
                نحن نحترم خصوصيتك ونلتزم بحماية بياناتك الشخصية. لمزيد من التفاصيل حول كيفية 
                جمع واستخدام وحماية بياناتك، يرجى مراجعة 
                <Link href="/privacy" className="text-blue-600 hover:underline mx-1">
                  سياسة الخصوصية
                </Link>
                الخاصة بنا.
              </p>
              
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2 text-blue-900">التزامنا بالأمان</h4>
                <ul className="space-y-1 text-blue-800 text-sm">
                  <li>• تشفير جميع البيانات الحساسة</li>
                  <li>• عدم مشاركة البيانات مع أطراف ثالثة بدون موافقة</li>
                  <li>• مراقبة مستمرة للأنشطة المشبوهة</li>
                  <li>• نسخ احتياطية منتظمة لحماية البيانات</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Changes and Updates */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-purple-600" />
                التغييرات والتحديثات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed mb-4">
                نحتفظ بالحق في تعديل هذه الشروط والأحكام في أي وقت. سيتم إشعار المستخدمين 
                بأي تغييرات جوهرية عبر البريد الإلكتروني أو إشعار على المنصة قبل 30 يوماً من 
                دخول التغييرات حيز التنفيذ.
              </p>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">
                  <strong>نصيحة:</strong> ننصح بمراجعة هذه الصفحة بانتظام للاطلاع على أي تحديثات. 
                  استمرار استخدام المنصة بعد التغييرات يعني موافقتك على الشروط المحدثة.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5 text-green-600" />
                معلومات التواصل
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed mb-4">
                إذا كان لديك أي أسئلة حول هذه الشروط والأحكام، يرجى التواصل معنا:
              </p>
              
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">البريد الإلكتروني</h4>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">الهاتف</h4>
                  <p className="text-gray-600">+966 11 123 4567</p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">العنوان</h4>
                  <p className="text-gray-600">الرياض، المملكة العربية السعودية</p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">ساعات العمل</h4>
                  <p className="text-gray-600">الأحد - الخميس: 8:00 ص - 5:00 م</p>
                </div>
              </div>
            </CardContent>
          </Card>

        </div>
      </div>
    </div>
  );
}
