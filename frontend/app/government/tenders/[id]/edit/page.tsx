'use client'

import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import DashboardLayout from '@/components/DashboardLayout'
import { useToast } from '@/components/ui/use-toast'
import { ArrowLeft, Save, X } from 'lucide-react'
import api from '@/lib/api'

interface TenderFormData {
  title: string
  description: string
  category: string
  budget: string
  deadline: string
  requirements: string
  location: string
  status: string
}

export default function EditTenderPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [tender, setTender] = useState<any>(null)
  const [formData, setFormData] = useState<TenderFormData>({
    title: '',
    description: '',
    category: '',
    budget: '',
    deadline: '',
    requirements: '',
    location: '',
    status: ''
  })

  useEffect(() => {
    loadTender()
  }, [params.id])

  const loadTender = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/government/tenders/${params.id}`)
      
      if (response.data.success) {
        const tenderData = response.data.data
        setTender(tenderData)
        
        // Format deadline for datetime-local input
        const deadline = new Date(tenderData.deadline)
        const formattedDeadline = deadline.toISOString().slice(0, 16)
        
        setFormData({
          title: tenderData.title || '',
          description: tenderData.description || '',
          category: tenderData.category || '',
          budget: tenderData.budget?.toString() || '',
          deadline: formattedDeadline,
          requirements: tenderData.requirements || '',
          location: tenderData.location || '',
          status: tenderData.status || 'draft'
        })
      } else {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المناقصة',
          variant: 'destructive'
        })
        router.push('/government/tenders')
      }
    } catch (error) {
      console.error('Error loading tender:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات المناقصة',
        variant: 'destructive'
      })
      router.push('/government/tenders')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof TenderFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title || !formData.description || !formData.category || !formData.budget || !formData.deadline) {
      toast({
        title: 'بيانات ناقصة',
        description: 'يرجى ملء جميع الحقول المطلوبة',
        variant: 'destructive'
      })
      return
    }

    const budget = parseFloat(formData.budget)
    if (isNaN(budget) || budget <= 0) {
      toast({
        title: 'خطأ في الميزانية',
        description: 'يرجى إدخال ميزانية صحيحة',
        variant: 'destructive'
      })
      return
    }

    const deadline = new Date(formData.deadline)
    if (deadline <= new Date()) {
      toast({
        title: 'خطأ في التاريخ',
        description: 'يجب أن يكون تاريخ الانتهاء في المستقبل',
        variant: 'destructive'
      })
      return
    }

    try {
      setSaving(true)
      const response = await api.put(`/government/tenders/${params.id}`, {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        budget,
        deadline: deadline.toISOString(),
        requirements: formData.requirements,
        location: formData.location,
        status: formData.status
      })
      
      if (response.data.success) {
        toast({
          title: 'تم التحديث',
          description: 'تم تحديث المناقصة بنجاح'
        })
        router.push('/government/tenders')
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في التحديث',
        description: error.response?.data?.message || 'حدث خطأ في تحديث المناقصة',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    router.push('/government/tenders')
  }

  if (loading) {
    return (
      <DashboardLayout allowedRoles={['government']}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات المناقصة...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout allowedRoles={['government']}>
      <div className="space-y-6">
        <header className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => router.push('/government/tenders')}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold">تعديل المناقصة</h1>
              <p className="text-muted-foreground">تحديث تفاصيل المناقصة</p>
            </div>
          </div>
        </header>

        <Card>
          <CardHeader>
            <CardTitle>تفاصيل المناقصة</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان المناقصة *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="أدخل عنوان المناقصة"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">الفئة *</Label>
                  <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الفئة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="construction">إنشاءات</SelectItem>
                      <SelectItem value="technology">تكنولوجيا</SelectItem>
                      <SelectItem value="services">خدمات</SelectItem>
                      <SelectItem value="supplies">مستلزمات</SelectItem>
                      <SelectItem value="consulting">استشارات</SelectItem>
                      <SelectItem value="maintenance">صيانة</SelectItem>
                      <SelectItem value="other">أخرى</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="budget">الميزانية (ر.س) *</Label>
                  <Input
                    id="budget"
                    type="number"
                    value={formData.budget}
                    onChange={(e) => handleInputChange('budget', e.target.value)}
                    placeholder="أدخل الميزانية"
                    min="1"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="deadline">تاريخ الانتهاء *</Label>
                  <Input
                    id="deadline"
                    type="datetime-local"
                    value={formData.deadline}
                    onChange={(e) => handleInputChange('deadline', e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">الموقع</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    placeholder="أدخل موقع المناقصة"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">الحالة</Label>
                  <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الحالة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">مسودة</SelectItem>
                      <SelectItem value="open">مفتوحة</SelectItem>
                      <SelectItem value="closed">مغلقة</SelectItem>
                      <SelectItem value="awarded">تم الترسية</SelectItem>
                      <SelectItem value="cancelled">ملغية</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">وصف المناقصة *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="أدخل وصف تفصيلي للمناقصة"
                  rows={4}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="requirements">المتطلبات والشروط</Label>
                <Textarea
                  id="requirements"
                  value={formData.requirements}
                  onChange={(e) => handleInputChange('requirements', e.target.value)}
                  placeholder="أدخل المتطلبات والشروط الخاصة بالمناقصة"
                  rows={4}
                />
              </div>

              <div className="flex gap-4 pt-4">
                <Button type="submit" disabled={saving}>
                  <Save className="h-4 w-4 ml-2" />
                  {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                </Button>
                <Button type="button" variant="outline" onClick={handleCancel}>
                  <X className="h-4 w-4 ml-2" />
                  إلغاء
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
