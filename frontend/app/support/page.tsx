'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  MessageSquare, 
  Phone, 
  Mail, 
  HelpCircle, 
  Bug, 
  Lightbulb,
  Star,
  Send,
  Clock,
  CheckCircle
} from 'lucide-react'

const supportCategories = [
  { value: 'technical', label: 'مشكلة تقنية', icon: <Bug className="h-4 w-4" /> },
  { value: 'account', label: 'مشكلة في الحساب', icon: <HelpCircle className="h-4 w-4" /> },
  { value: 'auction', label: 'مشكلة في المزاد', icon: <MessageSquare className="h-4 w-4" /> },
  { value: 'tender', label: 'مشكلة في المناقصة', icon: <MessageSquare className="h-4 w-4" /> },
  { value: 'suggestion', label: 'اقتراح تحسين', icon: <Lightbulb className="h-4 w-4" /> },
  { value: 'other', label: 'أخرى', icon: <HelpCircle className="h-4 w-4" /> }
]

const ticketStatuses = [
  { status: 'open', label: 'مفتوح', color: 'bg-blue-500' },
  { status: 'in-progress', label: 'قيد المعالجة', color: 'bg-yellow-500' },
  { status: 'resolved', label: 'تم الحل', color: 'bg-green-500' },
  { status: 'closed', label: 'مغلق', color: 'bg-gray-500' }
]



export default function SupportPage() {
  const [formData, setFormData] = useState({
    category: '',
    subject: '',
    description: '',
    priority: 'medium',
    attachments: []
  })
  const [tickets, setTickets] = useState([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadTickets()
  }, [])

  const loadTickets = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/support/tickets', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        setTickets(data.data.tickets)
      }
    } catch (error) {
      console.error('Error loading tickets:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch('/api/support/tickets', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          category: formData.category,
          subject: formData.subject,
          description: formData.description,
          priority: formData.priority
        })
      })

      const data = await response.json()

      if (data.success) {
        // Add new ticket to the list
        setTickets(prev => [data.data.ticket, ...prev])
        setSubmitted(true)
        setFormData({
          category: '',
          subject: '',
          description: '',
          priority: 'medium',
          attachments: []
        })
      } else {
        console.error('Error creating ticket:', data.message)
      }
    } catch (error) {
      console.error('Error submitting support ticket:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getPriorityBadge = (priority: string) => {
    const colors = {
      low: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-red-100 text-red-800'
    }
    return colors[priority as keyof typeof colors] || colors.medium
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = ticketStatuses.find(s => s.status === status)
    return statusConfig ? statusConfig : ticketStatuses[0]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="container mx-auto px-4 max-w-2xl">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h1 className="text-2xl font-bold mb-2">تم إرسال طلب الدعم بنجاح</h1>
                <p className="text-muted-foreground mb-6">
                  شكراً لك على التواصل معنا. سنقوم بالرد على طلبك في أقرب وقت ممكن.
                </p>
                <div className="flex gap-4 justify-center">
                  <Button onClick={() => setSubmitted(false)}>
                    إرسال طلب آخر
                  </Button>
                  <Button variant="outline" onClick={() => window.location.href = '/'}>
                    العودة للصفحة الرئيسية
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4 max-w-6xl">
        <header className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">مركز الدعم والمساعدة</h1>
          <p className="text-xl text-muted-foreground">
            نحن هنا لمساعدتك في حل أي مشكلة أو الإجابة على استفساراتك
          </p>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Contact Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>طرق التواصل</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="font-medium">الهاتف</p>
                    <p className="text-sm text-muted-foreground">+966 11 123 4567</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="font-medium">البريد الإلكتروني</p>
                    <p className="text-sm text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="font-medium">ساعات العمل</p>
                    <p className="text-sm text-muted-foreground">الأحد - الخميس: 8 ص - 5 م</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* My Tickets */}
            <Card>
              <CardHeader>
                <CardTitle>طلبات الدعم السابقة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {tickets.map(ticket => (
                    <div key={ticket.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-sm">{ticket.id}</span>
                        <Badge className={`${getStatusBadge(ticket.status).color} text-white`}>
                          {getStatusBadge(ticket.status).label}
                        </Badge>
                      </div>
                      <p className="text-sm mb-2">{ticket.subject}</p>
                      <div className="flex items-center gap-2">
                        <Badge className={getPriorityBadge(ticket.priority)}>
                          {ticket.priority === 'high' ? 'عالية' : 
                           ticket.priority === 'medium' ? 'متوسطة' : 'منخفضة'}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {formatDate(ticket.createdAt)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Support Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>إرسال طلب دعم جديد</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="category">فئة المشكلة *</Label>
                      <Select onValueChange={(value) => handleSelectChange('category', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر فئة المشكلة" />
                        </SelectTrigger>
                        <SelectContent>
                          {supportCategories.map(category => (
                            <SelectItem key={category.value} value={category.value}>
                              <div className="flex items-center gap-2">
                                {category.icon}
                                {category.label}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="priority">الأولوية *</Label>
                      <Select 
                        value={formData.priority} 
                        onValueChange={(value) => handleSelectChange('priority', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">منخفضة</SelectItem>
                          <SelectItem value="medium">متوسطة</SelectItem>
                          <SelectItem value="high">عالية</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subject">موضوع المشكلة *</Label>
                    <Input
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      placeholder="اكتب موضوع المشكلة باختصار"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">وصف تفصيلي للمشكلة *</Label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      className="w-full h-32 p-3 border rounded-md resize-none"
                      placeholder="اشرح المشكلة بالتفصيل مع ذكر الخطوات التي قمت بها"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="attachments">مرفقات (اختياري)</Label>
                    <Input
                      id="attachments"
                      name="attachments"
                      type="file"
                      multiple
                      accept="image/*,.pdf,.doc,.docx"
                      className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />
                    <p className="text-xs text-muted-foreground">
                      يمكنك إرفاق صور أو ملفات توضيحية (الحد الأقصى: 10 ميجابايت)
                    </p>
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={isSubmitting || !formData.category || !formData.subject || !formData.description}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        جاري الإرسال...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        إرسال طلب الدعم
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* FAQ Section */}
            <Card className="mt-8">
              <CardHeader>
                <CardTitle>الأسئلة الشائعة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border-b pb-4">
                    <h4 className="font-medium mb-2">كيف يمكنني تفعيل حسابي؟</h4>
                    <p className="text-sm text-muted-foreground">
                      بعد التسجيل، ستحتاج إلى تأكيد بريدك الإلكتروني ثم رفع الوثائق المطلوبة. سيتم مراجعة حسابك خلال 3 أيام عمل.
                    </p>
                  </div>
                  <div className="border-b pb-4">
                    <h4 className="font-medium mb-2">كيف يمكنني المشاركة في مزاد؟</h4>
                    <p className="text-sm text-muted-foreground">
                      يجب أن يكون حسابك مفعلاً أولاً. ثم يمكنك تصفح المزادات المتاحة وتقديم مزايدتك.
                    </p>
                  </div>
                  <div className="border-b pb-4">
                    <h4 className="font-medium mb-2">ما هي رسوم المنصة؟</h4>
                    <p className="text-sm text-muted-foreground">
                      التسجيل والتصفح مجاني. يتم تحصيل عمولة 2% من قيمة المزاد الناجح فقط.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
