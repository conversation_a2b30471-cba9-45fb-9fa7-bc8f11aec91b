import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Building, 
  Users, 
  Star, 
  MapPin, 
  Mail, 
  Linkedin,
  Twitter,
  Github,
  Award,
  Target,
  Heart
} from 'lucide-react';
import Link from 'next/link';

export default function TeamPage() {
  const teamMembers = [
    {
      id: 1,
      name: "أحمد محمد العلي",
      position: "الرئيس التنفيذي",
      department: "الإدارة العليا",
      image: "/api/placeholder/150/150",
      bio: "خبرة أكثر من 15 عاماً في مجال التجارة الإلكترونية والتقنية المالية",
      email: "<EMAIL>",
      linkedin: "#",
      twitter: "#",
      specialties: ["القيادة الاستراتيجية", "التجارة الإلكترونية", "التقنية المالية"]
    },
    {
      id: 2,
      name: "فاطمة سعد الزهراني",
      position: "مديرة التقنية",
      department: "التقنية",
      image: "/api/placeholder/150/150",
      bio: "مهندسة برمجيات متخصصة في تطوير المنصات الرقمية والأمان السيبراني",
      email: "<EMAIL>",
      linkedin: "#",
      github: "#",
      specialties: ["تطوير البرمجيات", "الأمان السيبراني", "هندسة النظم"]
    },
    {
      id: 3,
      name: "خالد عبدالله النمر",
      position: "مدير العمليات",
      department: "العمليات",
      image: "/api/placeholder/150/150",
      bio: "خبير في إدارة العمليات التجارية وتحسين الأداء التشغيلي",
      email: "<EMAIL>",
      linkedin: "#",
      specialties: ["إدارة العمليات", "تحسين الأداء", "إدارة المشاريع"]
    },
    {
      id: 4,
      name: "نورا أحمد الشهري",
      position: "مديرة التسويق",
      department: "التسويق",
      image: "/api/placeholder/150/150",
      bio: "متخصصة في التسويق الرقمي واستراتيجيات نمو المنصات الإلكترونية",
      email: "<EMAIL>",
      linkedin: "#",
      twitter: "#",
      specialties: ["التسويق الرقمي", "نمو المنتج", "تحليل البيانات"]
    },
    {
      id: 5,
      name: "محمد سالم القحطاني",
      position: "مدير المالية",
      department: "المالية",
      image: "/api/placeholder/150/150",
      bio: "محاسب قانوني معتمد مع خبرة واسعة في الإدارة المالية للشركات التقنية",
      email: "<EMAIL>",
      linkedin: "#",
      specialties: ["الإدارة المالية", "المحاسبة", "التخطيط المالي"]
    },
    {
      id: 6,
      name: "سارة عبدالرحمن الدوسري",
      position: "مديرة خدمة العملاء",
      department: "خدمة العملاء",
      image: "/api/placeholder/150/150",
      bio: "متخصصة في تجربة العملاء وإدارة علاقات العملاء في البيئة الرقمية",
      email: "<EMAIL>",
      linkedin: "#",
      specialties: ["تجربة العملاء", "إدارة العلاقات", "الدعم الفني"]
    }
  ];

  const departments = [
    { name: "الإدارة العليا", count: 3, color: "blue" },
    { name: "التقنية", count: 12, color: "green" },
    { name: "العمليات", count: 8, color: "purple" },
    { name: "التسويق", count: 6, color: "orange" },
    { name: "المالية", count: 4, color: "red" },
    { name: "خدمة العملاء", count: 10, color: "indigo" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Building className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">منصة المزادات</h1>
              <p className="text-sm text-gray-600">والمناقصات</p>
            </div>
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-blue-100 text-blue-800">فريق العمل</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            تعرف على <span className="text-blue-600">فريقنا المتميز</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            فريق من الخبراء والمتخصصين يعملون بشغف لتقديم أفضل الخدمات وتطوير منصتنا باستمرار
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">43+</div>
              <div className="text-sm text-gray-600">عضو فريق</div>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Building className="h-6 w-6 text-green-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">6</div>
              <div className="text-sm text-gray-600">أقسام</div>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Award className="h-6 w-6 text-purple-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">15+</div>
              <div className="text-sm text-gray-600">سنة خبرة</div>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Star className="h-6 w-6 text-orange-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">98%</div>
              <div className="text-sm text-gray-600">رضا الفريق</div>
            </CardContent>
          </Card>
        </div>

        {/* Leadership Team */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">الفريق القيادي</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              قادة ذوو خبرة واسعة يوجهون رؤية الشركة ويقودون الفريق نحو تحقيق الأهداف
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member) => (
              <Card key={member.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="text-center mb-4">
                    <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <span className="text-white text-2xl font-bold">
                        {member.name.split(' ')[0].charAt(0)}
                      </span>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-1">{member.name}</h3>
                    <p className="text-blue-600 font-medium mb-2">{member.position}</p>
                    <Badge variant="outline" className="mb-3">{member.department}</Badge>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 text-center">{member.bio}</p>
                  
                  <div className="flex flex-wrap gap-1 mb-4">
                    {member.specialties.map((specialty, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {specialty}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="flex justify-center gap-3">
                    <Button size="sm" variant="outline" className="p-2">
                      <Mail className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="p-2">
                      <Linkedin className="h-4 w-4" />
                    </Button>
                    {member.twitter && (
                      <Button size="sm" variant="outline" className="p-2">
                        <Twitter className="h-4 w-4" />
                      </Button>
                    )}
                    {member.github && (
                      <Button size="sm" variant="outline" className="p-2">
                        <Github className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Departments */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">أقسام الشركة</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              فرق متخصصة تعمل معاً لتحقيق رؤية الشركة وتقديم أفضل الخدمات
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {departments.map((dept, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6 text-center">
                  <div className={`w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center ${
                    dept.color === 'blue' ? 'bg-blue-100' :
                    dept.color === 'green' ? 'bg-green-100' :
                    dept.color === 'purple' ? 'bg-purple-100' :
                    dept.color === 'orange' ? 'bg-orange-100' :
                    dept.color === 'red' ? 'bg-red-100' : 'bg-indigo-100'
                  }`}>
                    <Building className={`h-8 w-8 ${
                      dept.color === 'blue' ? 'text-blue-600' :
                      dept.color === 'green' ? 'text-green-600' :
                      dept.color === 'purple' ? 'text-purple-600' :
                      dept.color === 'orange' ? 'text-orange-600' :
                      dept.color === 'red' ? 'text-red-600' : 'text-indigo-600'
                    }`} />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{dept.name}</h3>
                  <p className="text-gray-600">{dept.count} عضو</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Values */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">قيم فريقنا</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              القيم التي توجه عملنا وتحدد ثقافة شركتنا
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Target className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3">التميز</h3>
                <p className="text-gray-600">
                  نسعى للتميز في كل ما نقوم به ونلتزم بأعلى معايير الجودة
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Users className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3">العمل الجماعي</h3>
                <p className="text-gray-600">
                  نؤمن بقوة العمل الجماعي والتعاون لتحقيق أهدافنا المشتركة
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Heart className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3">الشغف</h3>
                <p className="text-gray-600">
                  نعمل بشغف وحماس لتطوير حلول مبتكرة تخدم عملاءنا
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Join Us */}
        <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <CardContent className="p-12 text-center">
            <h2 className="text-3xl font-bold mb-4">انضم إلى فريقنا</h2>
            <p className="text-xl mb-8 opacity-90">
              هل تريد أن تكون جزءاً من فريق متميز يعمل على تطوير مستقبل التجارة الإلكترونية؟
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/careers">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                  تصفح الوظائف
                </Button>
              </Link>
              <Link href="/contact">
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                  تواصل معنا
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
