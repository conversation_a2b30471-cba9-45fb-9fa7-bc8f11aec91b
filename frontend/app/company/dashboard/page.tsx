'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import DashboardLayout from '@/components/DashboardLayout'
import { useRouter } from 'next/navigation'
import api from '@/lib/api'
import {
  Gavel,
  FileText,
  TrendingUp,
  PlusCircle,
  Calendar,
  DollarSign,
  Activity,
  Clock,
  Users,
  Eye,
  ArrowRight,
  BarChart3,
  PieChart,
  LineChart,
  Target,
  Zap,
  Trophy,
  RefreshCw
} from 'lucide-react'
import {
  <PERSON><PERSON>hart as <PERSON><PERSON>rts<PERSON>ine<PERSON>hart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'

interface DashboardStats {
  myAuctions: number
  myTenders: number
  activeBids: number
  completedAuctions: number
  totalRevenue: number
  pendingPayments: number
  totalBidders: number
  averageAuctionValue: number
  successRate: number
}

interface RecentAuction {
  _id: string
  title: string
  description: string
  startingBid: number
  currentBid: number
  endTime: string
  status: string
  category: string
  bidsCount: number
  viewsCount: number
}

interface RecentBid {
  _id: string
  bidder: {
    _id: string
    profile: {
      fullName: string
    }
  }
  amount: number
  createdAt: string
  auction: {
    _id: string
    title: string
  }
}

interface Notification {
  _id: string
  type: string
  title: string
  message: string
  createdAt: string
  read: boolean
}

export default function CompanyDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentAuctions, setRecentAuctions] = useState<RecentAuction[]>([])
  const [recentBids, setRecentBids] = useState<RecentBid[]>([])
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const [chartData, setChartData] = useState<any>({
    revenueOverTime: [],
    auctionPerformance: [],
    categoryDistribution: [],
    bidderActivity: []
  })
  const { toast } = useToast()
  const router = useRouter()

  // Chart colors
  const COLORS = {
    primary: '#3B82F6',
    secondary: '#8B5CF6',
    success: '#10B981',
    warning: '#F59E0B',
    danger: '#EF4444',
    info: '#06B6D4'
  }

  const PIE_COLORS = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444', '#06B6D4']

  useEffect(() => {
    const userData = localStorage.getItem('user')
    if (userData) {
      setUser(JSON.parse(userData))
    }

    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // Load dashboard stats
      const statsResponse = await api.get('/company/dashboard/stats')
      setStats(statsResponse.data)

      // Load recent auctions
      const auctionsResponse = await api.get('/company/auctions?limit=5')
      setRecentAuctions(auctionsResponse.data.auctions || [])

      // Load recent bids
      const bidsResponse = await api.get('/company/bids/recent?limit=5')
      setRecentBids(bidsResponse.data.bids || [])

      // Load notifications
      const notificationsResponse = await api.get('/notifications?limit=5')
      setNotifications(notificationsResponse.data.notifications || [])

      // Load chart data
      const chartResponse = await api.get('/company/dashboard/charts')
      setChartData(chartResponse.data)

    } catch (error) {
      console.error('Error loading dashboard data:', error)
      toast({
        title: "خطأ",
        description: "حدث خطأ في تحميل بيانات لوحة التحكم",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diff = now.getTime() - date.getTime()

    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days > 0) return `منذ ${days} يوم`
    if (hours > 0) return `منذ ${hours} ساعة`
    if (minutes > 0) return `منذ ${minutes} دقيقة`
    return 'الآن'
  }

  const getAuctionStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>
      case 'completed':
        return <Badge className="bg-blue-100 text-blue-800">مكتمل</Badge>
      case 'ended':
        return <Badge variant="secondary">منتهي</Badge>
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل بيانات الشركة...</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout allowedRoles={["company"]}>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">لوحة تحكم الشركة</h1>
          <p className="text-gray-600">مرحباً بك في لوحة تحكم شركتك</p>
        </div>
      </div>
    </DashboardLayout>
  )
}
