'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import DashboardLayout from '@/components/DashboardLayout'
import { useRouter } from 'next/navigation'
import api from '@/lib/api'
import {
  Gavel,
  FileText,
  TrendingUp,
  PlusCircle,
  Calendar,
  DollarSign,
  Activity,
  Clock,
  Users,
  Eye,
  ArrowRight,
  BarChart3,
  PieChart,
  LineChart,
  Target,
  Zap,
  Trophy,
  RefreshCw
} from 'lucide-react'
import {
  <PERSON><PERSON>hart as <PERSON><PERSON>rts<PERSON>ine<PERSON>hart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'

interface DashboardStats {
  myAuctions: number
  myTenders: number
  activeBids: number
  completedAuctions: number
  totalRevenue: number
  pendingPayments: number
  totalBidders: number
  averageAuctionValue: number
  successRate: number
}

interface RecentAuction {
  _id: string
  title: string
  description: string
  startingBid: number
  currentBid: number
  endTime: string
  status: string
  category: string
  bidsCount: number
  viewsCount: number
}

interface RecentBid {
  _id: string
  bidder: {
    _id: string
    profile: {
      fullName: string
    }
  }
  amount: number
  createdAt: string
  auction: {
    _id: string
    title: string
  }
}

interface Notification {
  _id: string
  type: string
  title: string
  message: string
  createdAt: string
  read: boolean
}

export default function CompanyDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentAuctions, setRecentAuctions] = useState<RecentAuction[]>([])
  const [recentBids, setRecentBids] = useState<RecentBid[]>([])
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const [chartData, setChartData] = useState<any>({
    revenueOverTime: [],
    auctionPerformance: [],
    categoryDistribution: [],
    bidderActivity: []
  })
  const { toast } = useToast()
  const router = useRouter()

  // Chart colors
  const COLORS = {
    primary: '#3B82F6',
    secondary: '#8B5CF6',
    success: '#10B981',
    warning: '#F59E0B',
    danger: '#EF4444',
    info: '#06B6D4'
  }

  const PIE_COLORS = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444', '#06B6D4']

  useEffect(() => {
    const userData = localStorage.getItem('user')
    if (userData) {
      setUser(JSON.parse(userData))
    }

    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // Load dashboard stats
      const statsResponse = await api.get('/company/dashboard/stats')
      setStats(statsResponse.data)

      // Load recent auctions
      const auctionsResponse = await api.get('/company/auctions?limit=5')
      setRecentAuctions(auctionsResponse.data.auctions || [])

      // Load recent bids
      const bidsResponse = await api.get('/company/bids/recent?limit=5')
      setRecentBids(bidsResponse.data.bids || [])

      // Load notifications
      const notificationsResponse = await api.get('/notifications?limit=5')
      setNotifications(notificationsResponse.data.notifications || [])

      // Load chart data
      const chartResponse = await api.get('/company/dashboard/charts')
      setChartData(chartResponse.data)

    } catch (error) {
      console.error('Error loading dashboard data:', error)
      toast({
        title: "خطأ",
        description: "حدث خطأ في تحميل بيانات لوحة التحكم",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diff = now.getTime() - date.getTime()

    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days > 0) return `منذ ${days} يوم`
    if (hours > 0) return `منذ ${hours} ساعة`
    if (minutes > 0) return `منذ ${minutes} دقيقة`
    return 'الآن'
  }

  const getAuctionStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>
      case 'completed':
        return <Badge className="bg-blue-100 text-blue-800">مكتمل</Badge>
      case 'ended':
        return <Badge variant="secondary">منتهي</Badge>
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل بيانات الشركة...</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout allowedRoles={["company"]}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">لوحة تحكم الشركة</h1>
            <p className="text-gray-600">مرحباً بك {user?.profile?.fullName || 'في لوحة تحكم شركتك'}</p>
          </div>
          <Button onClick={loadDashboardData} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            تحديث البيانات
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100">مزاداتي</p>
                  <p className="text-3xl font-bold">{stats?.myAuctions || 0}</p>
                  <p className="text-sm text-blue-100">إجمالي المزادات</p>
                </div>
                <Gavel className="w-12 h-12 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100">العطاءات النشطة</p>
                  <p className="text-3xl font-bold">{stats?.activeBids || 0}</p>
                  <p className="text-sm text-green-100">عطاءات جارية</p>
                </div>
                <Activity className="w-12 h-12 text-green-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100">الإيرادات</p>
                  <p className="text-3xl font-bold">{formatPrice(stats?.totalRevenue || 0)}</p>
                  <p className="text-sm text-purple-100">إجمالي الإيرادات</p>
                </div>
                <DollarSign className="w-12 h-12 text-purple-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100">معدل النجاح</p>
                  <p className="text-3xl font-bold">{stats?.successRate || 0}%</p>
                  <p className="text-sm text-orange-100">نسبة الفوز</p>
                </div>
                <Trophy className="w-12 h-12 text-orange-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue Over Time */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="w-5 h-5" />
                الإيرادات الشهرية
              </CardTitle>
              <CardDescription>تطور الإيرادات خلال الأشهر الماضية</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={chartData.revenueOverTime}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [formatPrice(Number(value)), 'الإيرادات']} />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke={COLORS.primary}
                      fill={COLORS.primary}
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Auction Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="w-5 h-5" />
                أداء المزادات
              </CardTitle>
              <CardDescription>توزيع المزادات حسب الحالة</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Pie
                      data={chartData.auctionPerformance}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {chartData.auctionPerformance?.map((entry: any, index: number) => (
                        <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activities */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Auctions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gavel className="w-5 h-5" />
                المزادات الأخيرة
              </CardTitle>
              <CardDescription>آخر المزادات التي أنشأتها</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentAuctions.length === 0 ? (
                  <div className="text-center py-8">
                    <Gavel className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">لا توجد مزادات حديثة</p>
                    <Button
                      className="mt-4"
                      onClick={() => router.push('/company/create-auction')}
                    >
                      <PlusCircle className="w-4 h-4 mr-2" />
                      إنشاء مزاد جديد
                    </Button>
                  </div>
                ) : (
                  recentAuctions.map((auction) => (
                    <div key={auction._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{auction.title}</h4>
                        <p className="text-sm text-gray-600 mt-1">{auction.category}</p>
                        <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <DollarSign className="w-3 h-3" />
                            {formatPrice(auction.currentBid)}
                          </span>
                          <span className="flex items-center gap-1">
                            <Users className="w-3 h-3" />
                            {auction.bidsCount} عطاء
                          </span>
                          <span className="flex items-center gap-1">
                            <Eye className="w-3 h-3" />
                            {auction.viewsCount} مشاهدة
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getAuctionStatusBadge(auction.status)}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => router.push(`/auctions/${auction._id}`)}
                        >
                          <ArrowRight className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recent Bids */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                العطاءات الأخيرة
              </CardTitle>
              <CardDescription>آخر العطاءات المقدمة</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentBids.length === 0 ? (
                  <div className="text-center py-8">
                    <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">لا توجد عطاءات حديثة</p>
                    <Button
                      className="mt-4"
                      onClick={() => router.push('/auctions')}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      تصفح المزادات
                    </Button>
                  </div>
                ) : (
                  recentBids.map((bid) => (
                    <div key={bid._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{bid.auction.title}</h4>
                        <p className="text-sm text-gray-600 mt-1">عطاء بقيمة {formatPrice(bid.amount)}</p>
                        <p className="text-xs text-gray-500 mt-1">{getTimeAgo(bid.createdAt)}</p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/auctions/${bid.auction._id}`)}
                      >
                        <ArrowRight className="w-4 h-4" />
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5" />
              إجراءات سريعة
            </CardTitle>
            <CardDescription>الإجراءات الأكثر استخداماً</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                className="h-20 flex-col gap-2"
                variant="outline"
                onClick={() => router.push('/company/create-auction')}
              >
                <PlusCircle className="w-6 h-6" />
                إنشاء مزاد جديد
              </Button>
              <Button
                className="h-20 flex-col gap-2"
                variant="outline"
                onClick={() => router.push('/company/auctions')}
              >
                <Gavel className="w-6 h-6" />
                إدارة المزادات
              </Button>
              <Button
                className="h-20 flex-col gap-2"
                variant="outline"
                onClick={() => router.push('/company/bids')}
              >
                <Target className="w-6 h-6" />
                متابعة العطاءات
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Notifications */}
        {notifications.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5" />
                الإشعارات الأخيرة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {notifications.slice(0, 5).map((notification) => (
                  <div key={notification._id} className="flex items-start gap-3 p-3 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{notification.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{notification.message}</p>
                      <p className="text-xs text-gray-500 mt-1">{getTimeAgo(notification.createdAt)}</p>
                    </div>
                    {!notification.read && (
                      <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                    )}
                  </div>
                ))}
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push('/company/notifications')}
                >
                  عرض جميع الإشعارات
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
