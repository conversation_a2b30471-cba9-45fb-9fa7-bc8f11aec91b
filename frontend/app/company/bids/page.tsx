'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import DashboardLayout from '@/components/DashboardLayout'
import { Eye, CheckCircle, XCircle, Clock, User, TrendingUp } from 'lucide-react'


export default function CompanyBidsPage() {
  const [bids, setBids] = useState<any[]>([])
  const [filteredBids, setFilteredBids] = useState<any[]>([])
  const [selectedAuction, setSelectedAuction] = useState<string>('all')
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadBids()
  }, [])

  const loadBids = async () => {
    try {
      setLoading(true)
      // TODO: Replace with actual API call to get company's auction bids
      // const response = await companyAPI.getBids()
      // setBids(response.data.bids)
      // setFilteredBids(response.data.bids)

      // For now, set empty array until API is implemented
      setBids([])
      setFilteredBids([])
    } catch (error) {
      console.error('Error loading bids:', error)
      setBids([])
      setFilteredBids([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (selectedAuction === 'all') {
      setFilteredBids(bids)
    } else {
      setFilteredBids(bids.filter(bid => bid.auctionId === selectedAuction))
    }
  }, [selectedAuction, bids])

  const getStatusBadge = (bid: any) => {
    if (bid.isLeading) {
      return <Badge variant="default" className="bg-green-500">متقدم</Badge>
    } else {
      return <Badge variant="secondary">مُتجاوَز</Badge>
    }
  }

  const getUniqueAuctions = () => {
    const auctions = Array.from(new Set(bids.map(bid => bid.auctionTitle)))
    return auctions.map(title => {
      const bid = bids.find(b => b.auctionTitle === title)
      return { title, id: bid?.auctionId }
    })
  }

  const handleAcceptBid = (bidId: string) => {
    // TODO: Implement accept bid logic
    console.log('Accepting bid:', bidId)
  }

  const handleRejectBid = (bidId: string) => {
    // TODO: Implement reject bid logic
    console.log('Rejecting bid:', bidId)
  }

  const handleContactBidder = (phone: string) => {
    // TODO: Implement contact bidder logic
    window.open(`tel:${phone}`)
  }

  return (
    <DashboardLayout allowedRoles={['company']}>
      <div className="space-y-6">
        <header>
          <h1 className="text-3xl font-bold">عطاءات المزادات</h1>
          <p className="text-muted-foreground">إدارة ومتابعة العطاءات على مزاداتك</p>
        </header>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-col items-center">
              <CardTitle className="text-lg">إجمالي العطاءات</CardTitle>
              <CardDescription className="text-2xl font-bold text-gray-900">
                {filteredBids.length}
              </CardDescription>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader className="flex flex-col items-center">
              <CardTitle className="text-lg">العطاءات المتقدمة</CardTitle>
              <CardDescription className="text-2xl font-bold text-green-600">
                {filteredBids.filter(b => b.isLeading).length}
              </CardDescription>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader className="flex flex-col items-center">
              <CardTitle className="text-lg">أعلى عطاء</CardTitle>
              <CardDescription className="text-2xl font-bold text-blue-600">
                {filteredBids.length > 0 ? Math.max(...filteredBids.map(b => b.bidAmount)).toLocaleString() : 0} ر.س
              </CardDescription>
            </CardHeader>
          </Card>
          <Card>
            <CardHeader className="flex flex-col items-center">
              <CardTitle className="text-lg">عدد المزايدين</CardTitle>
              <CardDescription className="text-2xl font-bold text-purple-600">
                {new Set(filteredBids.map(b => b.bidderName)).size}
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* Filter */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <label className="text-sm font-medium">تصفية حسب المزاد:</label>
              <Select value={selectedAuction} onValueChange={setSelectedAuction}>
                <SelectTrigger className="w-64">
                  <SelectValue placeholder="اختر مزاد" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المزادات</SelectItem>
                  {getUniqueAuctions().map((auction) => (
                    <SelectItem key={auction.id} value={auction.id!}>
                      {auction.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Bids Table */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة العطاءات</CardTitle>
            <CardDescription>
              جميع العطاءات المقدمة على مزاداتك
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>المزاد</TableHead>
                  <TableHead>اسم المزايد</TableHead>
                  <TableHead>مبلغ العطاء</TableHead>
                  <TableHead>تاريخ العطاء</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredBids.map((bid) => (
                  <TableRow key={bid.id}>
                    <TableCell className="font-medium">{bid.auctionTitle}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        {bid.bidderName}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        <span className="font-semibold text-green-600">
                          {bid.bidAmount.toLocaleString()} ر.س
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        {new Date(bid.bidDate).toLocaleDateString('ar')}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(bid)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="outline" 
                          size="icon"
                          onClick={() => handleContactBidder(bid.bidderPhone)}
                          title="الاتصال بالمزايد"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        {bid.isLeading && (
                          <>
                            <Button 
                              variant="default" 
                              size="icon"
                              onClick={() => handleAcceptBid(bid.id)}
                              title="قبول العطاء"
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="destructive" 
                              size="icon"
                              onClick={() => handleRejectBid(bid.id)}
                              title="رفض العطاء"
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {filteredBids.length === 0 && (
          <Card>
            <CardContent className="py-12 text-center">
              <p className="text-muted-foreground">لا توجد عطاءات للمزاد المحدد</p>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
