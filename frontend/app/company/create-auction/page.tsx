'use client'

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import DashboardLayout from '@/components/DashboardLayout';
import { auctionAPI } from '@/lib/api';

export default function CreateAuctionPage() {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    startPrice: '',
    endDate: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const response = await auctionAPI.create({
        title: formData.title,
        description: formData.description,
        startingPrice: parseFloat(formData.startPrice),
        endDate: formData.endDate,
        category: 'other', // Default category
        condition: 'good', // Default condition
        startDate: new Date().toISOString() // Start immediately
      });

      if (response.data.success) {
        alert('تم إنشاء المزاد بنجاح!');
        setFormData({
          title: '',
          description: '',
          startPrice: '',
          endDate: '',
        });
      } else {
        alert('حدث خطأ في إنشاء المزاد: ' + response.data.message);
      }
    } catch (error) {
      console.error('Error creating auction:', error);
      alert('حدث خطأ في إنشاء المزاد');
    }
  };

  return (
    <DashboardLayout allowedRoles={['company']}>
      <div className="space-y-6">
        <header>
          <h1 className="text-3xl font-bold">إنشاء مزاد جديد</h1>
          <p className="text-muted-foreground">املأ المعلومات التالية لإنشاء مزاد جديد.</p>
        </header>
        <Card>
          <CardHeader>
            <CardTitle>تفاصيل المزاد</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان المزاد</Label>
                  <Input id="title" name="title" value={formData.title} onChange={handleChange} placeholder="أدخل عنوان المزاد" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="startPrice">السعر المبدئي</Label>
                  <Input id="startPrice" name="startPrice" value={formData.startPrice} onChange={handleChange} placeholder="أدخل السعر المبدئي" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">تاريخ الانتهاء</Label>
                  <Input id="endDate" name="endDate" type="date" value={formData.endDate} onChange={handleChange} />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">وصف المزاد</Label>
                <textarea id="description" name="description" value={formData.description} onChange={handleChange} className="p-2 w-full h-28 border rounded-md"></textarea>
              </div>
              <Button type="submit">إنشاء المزاد</Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}

