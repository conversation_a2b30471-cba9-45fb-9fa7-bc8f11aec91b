'use client'

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import DashboardLayout from '@/components/DashboardLayout';
import { Edit, Save, X, Building, Mail, Phone, MapPin, User } from 'lucide-react';
import { companyAPI } from '@/lib/api';

interface CompanyProfile {
  email: string;
  profile: {
    fullName: string;
    companyName: string;
    phone: string;
    address: string;
    nationalId: string;
    commercialRegister: string;
    taxNumber: string;
    website: string;
    description: string;
  };
}

export default function CompanyProfile() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<CompanyProfile | null>(null);
  const [formData, setFormData] = useState<CompanyProfile>({
    email: '',
    profile: {
      fullName: '',
      companyName: '',
      phone: '',
      address: '',
      nationalId: '',
      commercialRegister: '',
      taxNumber: '',
      website: '',
      description: ''
    }
  });

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const response = await companyAPI.getProfile();

      if (response.data.success) {
        const profileData = response.data.data;
        setProfile(profileData);
        setFormData({
          email: profileData.email || '',
          profile: {
            fullName: profileData.profile?.fullName || '',
            companyName: profileData.profile?.companyName || '',
            phone: profileData.profile?.phone || '',
            address: profileData.profile?.address || '',
            nationalId: profileData.profile?.nationalId || '',
            commercialRegister: profileData.profile?.commercialRegister || '',
            taxNumber: profileData.profile?.taxNumber || '',
            website: profileData.profile?.website || '',
            description: profileData.profile?.description || ''
          }
        });
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات الملف الشخصي',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith('profile.')) {
      const profileField = field.replace('profile.', '');
      setFormData(prev => ({
        ...prev,
        profile: {
          ...prev.profile,
          [profileField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      const response = await companyAPI.updateProfile(formData);

      if (response.data.success) {
        setProfile(formData);
        setEditing(false);
        toast({
          title: 'تم التحديث',
          description: 'تم تحديث الملف الشخصي بنجاح'
        });
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في التحديث',
        description: error.response?.data?.message || 'حدث خطأ في تحديث الملف الشخصي',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (profile) {
      setFormData({
        email: profile.email || '',
        profile: {
          fullName: profile.profile?.fullName || '',
          companyName: profile.profile?.companyName || '',
          phone: profile.profile?.phone || '',
          address: profile.profile?.address || '',
          nationalId: profile.profile?.nationalId || '',
          commercialRegister: profile.profile?.commercialRegister || '',
          taxNumber: profile.profile?.taxNumber || '',
          website: profile.profile?.website || '',
          description: profile.profile?.description || ''
        }
      });
    }
    setEditing(false);
  };

  if (loading) {
    return (
      <DashboardLayout allowedRoles={['company']}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل الملف الشخصي...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout allowedRoles={['company']}>
      <div className="space-y-6">
        <header className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">الملف الشخصي للشركة</h1>
            <p className="text-muted-foreground">معلومات عن الشركة وإدارة الحساب</p>
          </div>
          <div className="flex gap-2">
            {!editing ? (
              <Button onClick={() => setEditing(true)}>
                <Edit className="h-4 w-4 ml-2" />
                تعديل الملف الشخصي
              </Button>
            ) : (
              <>
                <Button onClick={handleSave} disabled={saving}>
                  <Save className="h-4 w-4 ml-2" />
                  {saving ? 'جاري الحفظ...' : 'حفظ'}
                </Button>
                <Button variant="outline" onClick={handleCancel}>
                  <X className="h-4 w-4 ml-2" />
                  إلغاء
                </Button>
              </>
            )}
          </div>
        </header>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              تفاصيل الشركة
            </CardTitle>
          </CardHeader>
          <CardContent>
            {editing ? (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="companyName">اسم الشركة</Label>
                    <Input
                      id="companyName"
                      value={formData.profile.companyName}
                      onChange={(e) => handleInputChange('profile.companyName', e.target.value)}
                      placeholder="أدخل اسم الشركة"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="fullName">اسم المسؤول</Label>
                    <Input
                      id="fullName"
                      value={formData.profile.fullName}
                      onChange={(e) => handleInputChange('profile.fullName', e.target.value)}
                      placeholder="أدخل اسم المسؤول"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">البريد الإلكتروني</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="أدخل البريد الإلكتروني"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">رقم الهاتف</Label>
                    <Input
                      id="phone"
                      value={formData.profile.phone}
                      onChange={(e) => handleInputChange('profile.phone', e.target.value)}
                      placeholder="أدخل رقم الهاتف"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nationalId">رقم الهوية</Label>
                    <Input
                      id="nationalId"
                      value={formData.profile.nationalId}
                      onChange={(e) => handleInputChange('profile.nationalId', e.target.value)}
                      placeholder="أدخل رقم الهوية"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="commercialRegister">السجل التجاري</Label>
                    <Input
                      id="commercialRegister"
                      value={formData.profile.commercialRegister}
                      onChange={(e) => handleInputChange('profile.commercialRegister', e.target.value)}
                      placeholder="أدخل رقم السجل التجاري"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="taxNumber">الرقم الضريبي</Label>
                    <Input
                      id="taxNumber"
                      value={formData.profile.taxNumber}
                      onChange={(e) => handleInputChange('profile.taxNumber', e.target.value)}
                      placeholder="أدخل الرقم الضريبي"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="website">الموقع الإلكتروني</Label>
                    <Input
                      id="website"
                      value={formData.profile.website}
                      onChange={(e) => handleInputChange('profile.website', e.target.value)}
                      placeholder="أدخل الموقع الإلكتروني"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">العنوان</Label>
                  <Textarea
                    id="address"
                    value={formData.profile.address}
                    onChange={(e) => handleInputChange('profile.address', e.target.value)}
                    placeholder="أدخل عنوان الشركة"
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">وصف الشركة</Label>
                  <Textarea
                    id="description"
                    value={formData.profile.description}
                    onChange={(e) => handleInputChange('profile.description', e.target.value)}
                    placeholder="أدخل وصف مختصر عن الشركة وأنشطتها"
                    rows={4}
                  />
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center gap-3">
                  <Building className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">اسم الشركة</label>
                    <p className="font-medium">{profile?.profile?.companyName || 'غير محدد'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <User className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">اسم المسؤول</label>
                    <p className="font-medium">{profile?.profile?.fullName || 'غير محدد'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">البريد الإلكتروني</label>
                    <p className="font-medium">{profile?.email || 'غير محدد'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">رقم الهاتف</label>
                    <p className="font-medium">{profile?.profile?.phone || 'غير محدد'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">العنوان</label>
                    <p className="font-medium">{profile?.profile?.address || 'غير محدد'}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">السجل التجاري</label>
                  <p className="font-medium">{profile?.profile?.commercialRegister || 'غير محدد'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">الرقم الضريبي</label>
                  <p className="font-medium">{profile?.profile?.taxNumber || 'غير محدد'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">الموقع الإلكتروني</label>
                  <p className="font-medium">{profile?.profile?.website || 'غير محدد'}</p>
                </div>
                {profile?.profile?.description && (
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-muted-foreground">وصف الشركة</label>
                    <p className="font-medium mt-1">{profile.profile.description}</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}

