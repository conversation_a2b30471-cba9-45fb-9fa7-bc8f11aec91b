'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import DashboardLayout from '@/components/DashboardLayout'
import { useRouter } from 'next/navigation'
import api from '@/lib/api'
import {
  FileText,
  Calendar,
  DollarSign,
  Clock,
  Eye,
  ArrowRight,
  Filter,
  Search,
  Download,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  XCircle,
  Hourglass
} from 'lucide-react'

interface TenderApplication {
  _id: string
  tenderId: string
  tender: {
    _id: string
    title: string
    description: string
    budget: number
    deadline: string
    category: string
    status: string
    government: {
      name: string
    }
  }
  proposal: {
    amount: number
    timeline: number
    technicalScore?: number
    financialScore?: number
    experienceScore?: number
    overallScore?: number
    status: 'pending' | 'accepted' | 'rejected'
  }
  submittedAt: string
  documents: string[]
}

export default function CompanyTenders() {
  const [applications, setApplications] = useState<TenderApplication[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    loadApplications()
  }, [])

  const loadApplications = async () => {
    try {
      setLoading(true)
      const response = await api.get('/company/applications')
      setApplications(response.data.data.proposals || [])
    } catch (error) {
      console.error('Error loading applications:', error)
      toast({
        title: "خطأ",
        description: "حدث خطأ في تحميل طلبات المناقصات",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0,
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge className="bg-yellow-100 text-yellow-800">
            <Hourglass className="w-3 h-3 mr-1" />
            قيد المراجعة
          </Badge>
        )
      case 'accepted':
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            مقبول
          </Badge>
        )
      case 'rejected':
        return (
          <Badge className="bg-red-100 text-red-800">
            <XCircle className="w-3 h-3 mr-1" />
            مرفوض
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTenderStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return <Badge className="bg-blue-100 text-blue-800">مفتوح</Badge>
      case 'closed':
        return <Badge className="bg-gray-100 text-gray-800">مغلق</Badge>
      case 'awarded':
        return <Badge className="bg-green-100 text-green-800">تم الترسية</Badge>
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const filteredApplications = applications.filter(app => {
    const matchesFilter = filter === 'all' || app.proposal.status === filter
    const matchesSearch = app.tender.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.tender.category.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesFilter && matchesSearch
  })

  const stats = {
    total: applications.length,
    pending: applications.filter(app => app.proposal.status === 'pending').length,
    accepted: applications.filter(app => app.proposal.status === 'accepted').length,
    rejected: applications.filter(app => app.proposal.status === 'rejected').length
  }

  if (loading) {
    return (
      <DashboardLayout allowedRoles={["company"]}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل طلبات المناقصات...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout allowedRoles={["company"]}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">طلبات المناقصات</h1>
            <p className="text-gray-600">إدارة طلبات المشاركة في المناقصات</p>
          </div>
          <Button onClick={loadApplications} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            تحديث
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي الطلبات</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <FileText className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">قيد المراجعة</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
                </div>
                <Hourglass className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">مقبولة</p>
                  <p className="text-2xl font-bold text-green-600">{stats.accepted}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">مرفوضة</p>
                  <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
                </div>
                <XCircle className="w-8 h-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="البحث في المناقصات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={filter}
                  onChange={(e) => setFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="pending">قيد المراجعة</option>
                  <option value="accepted">مقبولة</option>
                  <option value="rejected">مرفوضة</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Applications List */}
        <div className="space-y-4">
          {filteredApplications.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات مناقصات</h3>
                <p className="text-gray-600">لم تقم بتقديم أي طلبات للمناقصات بعد</p>
              </CardContent>
            </Card>
          ) : (
            filteredApplications.map((application) => (
              <Card key={application._id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {application.tender.title}
                      </h3>
                      <p className="text-gray-600 mb-3 line-clamp-2">
                        {application.tender.description}
                      </p>
                      <div className="flex flex-wrap gap-2 mb-3">
                        {getStatusBadge(application.proposal.status)}
                        {getTenderStatusBadge(application.tender.status)}
                        <Badge variant="outline">{application.tender.category}</Badge>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="flex items-center text-sm text-gray-600">
                      <DollarSign className="w-4 h-4 mr-2" />
                      <span>عطاؤنا: {formatPrice(application.proposal.amount)}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="w-4 h-4 mr-2" />
                      <span>تاريخ التقديم: {formatDate(application.submittedAt)}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Clock className="w-4 h-4 mr-2" />
                      <span>مدة التنفيذ: {application.proposal.timeline} شهر</span>
                    </div>
                  </div>

                  {application.proposal.overallScore && (
                    <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">نتائج التقييم</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                        <div>النتيجة الإجمالية: <span className="font-medium">{application.proposal.overallScore?.toFixed(1)}</span></div>
                        <div>التقني: <span className="font-medium">{application.proposal.technicalScore?.toFixed(1)}</span></div>
                        <div>المالي: <span className="font-medium">{application.proposal.financialScore?.toFixed(1)}</span></div>
                        <div>الخبرة: <span className="font-medium">{application.proposal.experienceScore?.toFixed(1)}</span></div>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-600">
                      الجهة الحكومية: {application.tender.government.name}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/tenders/${application.tender._id}`)}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        عرض المناقصة
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  )
}
