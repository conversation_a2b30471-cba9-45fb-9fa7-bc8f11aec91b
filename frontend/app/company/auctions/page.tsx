'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import DashboardLayout from '@/components/DashboardLayout'
import { Eye, Edit, Trash2, PlusCircle, Users, TrendingUp } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { auctionAPI } from '@/lib/api'
import { useToast } from '@/components/ui/use-toast'


export default function CompanyAuctionsPage() {
  const [auctions, setAuctions] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    loadCompanyAuctions()
  }, [])

  const loadCompanyAuctions = async () => {
    try {
      setLoading(true)
      const response = await auctionAPI.getAll()
      // Filter auctions by company (in real app, this would be server-side)
      setAuctions(response.data || [])
    } catch (error) {
      console.error('Error loading company auctions:', error)
      setAuctions([])

      toast({
        title: 'فشل في تحميل المزادات',
        description: 'حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default">نشط</Badge>
      case 'ended':
        return <Badge variant="destructive">منتهي</Badge>
      case 'draft':
        return <Badge variant="secondary">مسودة</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  const handleDeleteAuction = async (auctionId: string) => {
    const auction = auctions.find(a => a.id === auctionId)
    if (!auction) {
      toast({
        title: 'خطأ',
        description: 'لم يتم العثور على المزاد',
        variant: 'destructive'
      })
      return
    }
    
    const confirmMessage = `هل أنت متأكد من حذف المزاد:\n\n"${auction.title}"\n\nهذه العملية غير قابلة للتراجع وسيتم حذف جميع المزايدات المرتبطة به.`
    
    if (confirm(confirmMessage)) {
      // Store previous state for potential rollback
      const previousAuctions = [...auctions]

      try {
        setLoading(true)

        // Optimistic UI update
        setAuctions(prev => prev.filter(a => a.id !== auctionId))

        await auctionAPI.delete(auctionId)

        toast({
          title: '🗑️ تم حذف المزاد بنجاح',
          description: `تم حذف "${auction.title}" وجميع البيانات المرتبطة به`
        })
      } catch (error: any) {
        console.error('Error deleting auction:', error)
        // Revert optimistic update on error
        setAuctions(previousAuctions)
        toast({
          title: 'فشل في حذف المزاد',
          description: error.response?.data?.message || 'يرجى المحاولة مرة أخرى',
          variant: 'destructive'
        })
      } finally {
        setLoading(false)
      }
    }
  }

  const handleViewAuction = async (auctionId: string) => {
    try {
      const auction = auctions.find(a => a.id === auctionId)
      if (!auction) {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المزاد',
          variant: 'destructive'
        })
        return
      }
      
      // Show auction management details
      const details = `تفاصيل المزاد:\n\n` +
                     `العنوان: ${auction.title}\n` +
                     `الحالة: ${auction.status}\n` +
                     `تاريخ الانتهاء: ${auction.endDate}\n` +
                     `أعلى مزايدة: ${(auction.currentBid || 0).toLocaleString()} ر.س\n` +
                     `عدد المزايدات: ${auction.bidsCount || 0}\n` +
                     `عدد المشاهدات: ${auction.views || 0}`
      
      alert(details)
      
      // Try to fetch fresh data from API for management purposes
      try {
        const response = await auctionAPI.getById(auctionId)
        console.log('Fresh auction management data:', response.data)
        toast({
          title: '📊 تم عرض بيانات الإدارة',
          description: 'تم تحديث بيانات المزاد من الخادم'
        })
      } catch (apiError) {
        console.log('Could not fetch fresh data, showing cached data')
        toast({
          title: '📊 عرض بيانات الإدارة',
          description: 'يتم عرض البيانات المحفوظة محلياً'
        })
      }
    } catch (error: any) {
      console.error('Error viewing auction management:', error)
      toast({
        title: 'فشل في عرض بيانات الإدارة',
        description: error.response?.data?.message || 'يرجى المحاولة مرة أخرى',
        variant: 'destructive'
      })
    }
  }

  const handleEditAuction = (auctionId: string) => {
    const auction = auctions.find(a => a.id === auctionId)
    if (!auction) {
      toast({
        title: 'خطأ',
        description: 'لم يتم العثور على المزاد',
        variant: 'destructive'
      })
      return
    }
    
    // For now, show edit options in prompt - in real app this would navigate to edit page
    const editAction = prompt(
      `تحرير المزاد: "${auction.title}"\n\n` +
      `اختر ما تريد تحريره:\n` +
      `1 - العنوان\n` +
      `2 - تاريخ الانتهاء\n` +
      `3 - الحالة\n\n` +
      `أدخل رقم الخيار:`,
      '1'
    )
    
    if (!editAction) return // User cancelled
    
    let updateData: any = {}
    let updateField = ''
    
    switch (editAction) {
      case '1':
        const newTitle = prompt('أدخل العنوان الجديد:', auction.title)
        if (newTitle && newTitle.trim()) {
          updateData.title = newTitle.trim()
          updateField = 'العنوان'
        }
        break
      case '2':
        const newEndDate = prompt('أدخل تاريخ الانتهاء الجديد (YYYY-MM-DD):', auction.endDate)
        if (newEndDate && newEndDate.trim()) {
          updateData.endDate = newEndDate.trim()
          updateField = 'تاريخ الانتهاء'
        }
        break
      case '3':
        const newStatus = prompt('أدخل الحالة الجديدة (active/draft/ended):', auction.status)
        if (newStatus && ['active', 'draft', 'ended'].includes(newStatus)) {
          updateData.status = newStatus
          updateField = 'الحالة'
        }
        break
      default:
        toast({
          title: 'خيار غير صالح',
          description: 'يرجى اختيار رقم صحيح',
          variant: 'destructive'
        })
        return
    }
    
    if (Object.keys(updateData).length === 0) {
      toast({
        title: 'لم يتم التحديث',
        description: 'لم يتم إدخال قيمة صالحة',
        variant: 'destructive'
      })
      return
    }
    
    // Perform the update
    performAuctionUpdate(auctionId, updateData, updateField)
  }
  
  const performAuctionUpdate = async (auctionId: string, updateData: any, fieldName: string) => {
    // Store previous state for potential rollback
    const previousAuctions = [...auctions]

    try {
      setLoading(true)

      // Optimistic UI update
      setAuctions(prev => prev.map(a =>
        a.id === auctionId ? { ...a, ...updateData } : a
      ))

      await auctionAPI.update(auctionId, updateData)

      toast({
        title: '✏️ تم تحديث المزاد بنجاح',
        description: `تم تحديث ${fieldName} بنجاح`
      })
    } catch (error: any) {
      console.error('Error updating auction:', error)
      // Revert optimistic update on error
      setAuctions(previousAuctions)
      toast({
        title: 'فشل في تحديث المزاد',
        description: error.response?.data?.message || 'يرجى المحاولة مرة أخرى',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <DashboardLayout allowedRoles={['company']}>
      <div className="space-y-6">
        <header className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">مزاداتي</h1>
            <p className="text-muted-foreground">إدارة المزادات التي أنشأتها شركتك</p>
          </div>
          <Button onClick={() => router.push('/company/create-auction')}>
            <PlusCircle className="h-4 w-4 ml-2" />
            إنشاء مزاد جديد
          </Button>
        </header>

        {/* Stats Cards - Only show when there are auctions */}
        {auctions.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-col items-center">
                <CardTitle className="text-lg">إجمالي المزادات</CardTitle>
                <CardDescription className="text-2xl font-bold text-gray-900">
                  {auctions.length}
                </CardDescription>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader className="flex flex-col items-center">
                <CardTitle className="text-lg">المزادات النشطة</CardTitle>
                <CardDescription className="text-2xl font-bold text-green-600">
                  {auctions.filter(a => a.status === 'active').length}
                </CardDescription>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader className="flex flex-col items-center">
                <CardTitle className="text-lg">إجمالي المزايدات</CardTitle>
                <CardDescription className="text-2xl font-bold text-blue-600">
                  {auctions.reduce((sum, a) => sum + (a.bidsCount || 0), 0)}
                </CardDescription>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader className="flex flex-col items-center">
                <CardTitle className="text-lg">إجمالي المشاهدات</CardTitle>
                <CardDescription className="text-2xl font-bold text-purple-600">
                  {auctions.reduce((sum, a) => sum + (a.views || 0), 0)}
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        )}

        {/* Auctions Table or No Data State */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المزادات</CardTitle>
            <CardDescription>
              {auctions.length > 0 
                ? 'جميع المزادات التي أنشأتها شركتك'
                : 'لا توجد مزادات منشورة حالياً'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="py-16 text-center">
                <div className="flex flex-col items-center gap-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <p className="text-muted-foreground">جاري تحميل مزاداتك...</p>
                </div>
              </div>
            ) : auctions.length === 0 ? (
              <div className="py-16 text-center">
                <div className="flex flex-col items-center gap-4">
                  <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center">
                    <PlusCircle className="h-8 w-8 text-gray-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">لا توجد مزادات بعد</h3>
                    <p className="text-muted-foreground mb-4">
                      ابدأ بإنشاء أول مزاد لشركتك
                    </p>
                    <Button onClick={() => router.push('/company/create-auction')}>
                      <PlusCircle className="h-4 w-4 ml-2" />
                      إنشاء مزاد جديد
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>عنوان المزاد</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>تاريخ الانتهاء</TableHead>
                    <TableHead>أعلى مزايدة</TableHead>
                    <TableHead>عدد المزايدات</TableHead>
                    <TableHead>المشاهدات</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auctions.map((auction) => (
                    <TableRow key={auction.id}>
                      <TableCell className="font-medium">{auction.title}</TableCell>
                      <TableCell>{getStatusBadge(auction.status)}</TableCell>
                      <TableCell>{auction.endDate}</TableCell>
                      <TableCell>
                        {(auction.currentBid || 0) > 0 ? `${auction.currentBid.toLocaleString()} ر.س` : 'لا توجد مزايدات'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          {auction.bidsCount || 0}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4 text-muted-foreground" />
                          {auction.views || 0}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button 
                            variant="outline" 
                            size="icon"
                            onClick={(e) => {
                              e.preventDefault()
                              e.stopPropagation()
                              alert('View button clicked for: ' + auction.title)
                              console.log('VIEW BUTTON CLICKED for auction:', auction.id)
                              handleViewAuction(auction.id)
                            }}
                            disabled={loading}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="icon"
                            onClick={(e) => {
                              e.preventDefault()
                              e.stopPropagation()
                              alert('Edit button clicked for: ' + auction.title)
                              console.log('EDIT BUTTON CLICKED for auction:', auction.id)
                              handleEditAuction(auction.id)
                            }}
                            disabled={loading}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="destructive" 
                            size="icon"
                            onClick={(e) => {
                              e.preventDefault()
                              e.stopPropagation()
                              alert('Delete button clicked for: ' + auction.title)
                              console.log('DELETE BUTTON CLICKED for auction:', auction.id)
                              handleDeleteAuction(auction.id)
                            }}
                            disabled={loading}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
