'use client'

import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  User, 
  Building, 
  Shield, 
  FileText, 
  CreditCard, 
  Gavel, 
  Search,
  CheckCircle,
  ArrowRight,
  Play,
  Download,
  BookOpen
} from 'lucide-react';
import Link from 'next/link';

export default function GuidePage() {
  const [activeGuide, setActiveGuide] = useState('individual');

  const guides = {
    individual: {
      title: "دليل الأفراد",
      icon: User,
      color: "blue",
      steps: [
        {
          title: "إنشاء الحساب",
          description: "سجل حساب جديد وارفع الوثائق المطلوبة",
          details: [
            "اختر 'فرد' عند التسجيل",
            "املأ البيانات الشخصية",
            "ارفع الهوية الوطنية أو الإقامة",
            "ارفع صورة شخصية وإثبات العنوان",
            "انتظر موافقة الإدارة خلال 3 أيام"
          ]
        },
        {
          title: "تصفح المزادات",
          description: "ابحث عن المزادات التي تهمك",
          details: [
            "استخدم فلاتر البحث للعثور على ما تريد",
            "اقرأ تفاصيل المنتج بعناية",
            "تحقق من تقييمات البائع",
            "راجع شروط المزاد وتاريخ الانتهاء"
          ]
        },
        {
          title: "المزايدة",
          description: "شارك في المزادات وقدم عروضك",
          details: [
            "ادخل مبلغ المزايدة",
            "فعل المزايدة التلقائية إذا رغبت",
            "تابع حالة مزايدتك",
            "استلم إشعارات عند تجاوز مزايدتك"
          ]
        },
        {
          title: "الدفع والاستلام",
          description: "أكمل عملية الشراء بأمان",
          details: [
            "ادفع خلال 24 ساعة من الفوز",
            "اختر طريقة الدفع المناسبة",
            "تواصل مع البائع لترتيب الاستلام",
            "أكد استلام المنتج في النظام"
          ]
        }
      ]
    },
    company: {
      title: "دليل الشركات",
      icon: Building,
      color: "green",
      steps: [
        {
          title: "تسجيل الشركة",
          description: "سجل شركتك وأكمل التوثيق",
          details: [
            "اختر 'شركة' عند التسجيل",
            "ارفع السجل التجاري والرقم الضريبي",
            "ارفع هوية المفوض وعقد التأسيس",
            "انتظر مراجعة الوثائق والموافقة"
          ]
        },
        {
          title: "إنشاء المزادات",
          description: "أنشئ مزادات لمنتجاتك وخدماتك",
          details: [
            "اذهب إلى 'إنشاء مزاد جديد'",
            "املأ تفاصيل المنتج أو الخدمة",
            "ارفع صور عالية الجودة",
            "حدد السعر الابتدائي وتاريخ الانتهاء",
            "انشر المزاد بعد المراجعة"
          ]
        },
        {
          title: "إدارة المزايدات",
          description: "تابع مزاداتك وتفاعل مع المزايدين",
          details: [
            "راقب المزايدات في الوقت الفعلي",
            "رد على استفسارات المزايدين",
            "قم بتحديث تفاصيل المزاد إذا لزم الأمر",
            "أغلق المزاد في الوقت المحدد"
          ]
        },
        {
          title: "التقدم للمناقصات",
          description: "شارك في المناقصات الحكومية والخاصة",
          details: [
            "تصفح المناقصات المتاحة",
            "اقرأ الشروط والمتطلبات بعناية",
            "حضر الوثائق والمستندات المطلوبة",
            "قدم عطاءك قبل انتهاء الموعد"
          ]
        }
      ]
    },
    government: {
      title: "دليل الجهات الحكومية",
      icon: Shield,
      color: "purple",
      steps: [
        {
          title: "التسجيل الحكومي",
          description: "سجل جهتك الحكومية بالوثائق الرسمية",
          details: [
            "اختر 'جهة حكومية' عند التسجيل",
            "ارفع خطاب التفويض الرسمي",
            "ارفع هوية المفوض وختم الجهة",
            "انتظر التحقق من صحة الوثائق"
          ]
        },
        {
          title: "إنشاء المناقصات",
          description: "أنشئ مناقصات حكومية شفافة",
          details: [
            "حدد نوع المناقصة والمتطلبات",
            "اكتب وصف تفصيلي للمشروع",
            "حدد الميزانية وتاريخ الانتهاء",
            "ارفع الوثائق والمخططات المطلوبة"
          ]
        },
        {
          title: "إدارة العطاءات",
          description: "راجع وقيم العطاءات المقدمة",
          details: [
            "راجع جميع العطاءات المقدمة",
            "قيم العطاءات حسب المعايير المحددة",
            "تواصل مع مقدمي العطاءات للتوضيحات",
            "اختر العطاء الفائز وأعلن النتائج"
          ]
        },
        {
          title: "متابعة التنفيذ",
          description: "تابع تنفيذ المشاريع المرسية",
          details: [
            "وقع العقد مع المقاول الفائز",
            "تابع مراحل تنفيذ المشروع",
            "راجع التقارير الدورية",
            "أكمل عملية الاستلام والدفع"
          ]
        }
      ]
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Building className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">منصة المزادات</h1>
              <p className="text-sm text-gray-600">والمناقصات</p>
            </div>
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <Badge className="mb-4 bg-blue-100 text-blue-800">دليل المستخدم</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            تعلم كيفية استخدام <span className="text-blue-600">المنصة</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            دليل شامل لجميع أنواع المستخدمين لتحقيق أقصى استفادة من منصتنا
          </p>
        </div>

        {/* User Type Selection */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          {Object.entries(guides).map(([key, guide]) => {
            const IconComponent = guide.icon;
            const isActive = activeGuide === key;
            
            return (
              <Card 
                key={key} 
                className={`cursor-pointer transition-all ${
                  isActive ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:shadow-lg'
                }`}
                onClick={() => setActiveGuide(key)}
              >
                <CardContent className="pt-6 text-center">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
                    guide.color === 'blue' ? 'bg-blue-100' :
                    guide.color === 'green' ? 'bg-green-100' : 'bg-purple-100'
                  }`}>
                    <IconComponent className={`h-8 w-8 ${
                      guide.color === 'blue' ? 'text-blue-600' :
                      guide.color === 'green' ? 'text-green-600' : 'text-purple-600'
                    }`} />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{guide.title}</h3>
                  <p className="text-gray-600 text-sm">
                    {key === 'individual' && 'للأفراد الراغبين في المشاركة في المزادات'}
                    {key === 'company' && 'للشركات التي تريد إنشاء مزادات والتقدم للمناقصات'}
                    {key === 'government' && 'للجهات الحكومية لإنشاء المناقصات الرسمية'}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Active Guide Content */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="text-2xl flex items-center gap-3">
              {React.createElement(guides[activeGuide].icon, { 
                className: `h-6 w-6 ${
                  guides[activeGuide].color === 'blue' ? 'text-blue-600' :
                  guides[activeGuide].color === 'green' ? 'text-green-600' : 'text-purple-600'
                }` 
              })}
              {guides[activeGuide].title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-8">
              {guides[activeGuide].steps.map((step, index) => (
                <div key={index} className="flex gap-6">
                  <div className="flex-shrink-0">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold ${
                      guides[activeGuide].color === 'blue' ? 'bg-blue-600' :
                      guides[activeGuide].color === 'green' ? 'bg-green-600' : 'bg-purple-600'
                    }`}>
                      {index + 1}
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
                    <p className="text-gray-600 mb-4">{step.description}</p>
                    <ul className="space-y-2">
                      {step.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex items-start gap-3">
                          <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Additional Resources */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="h-5 w-5" />
                فيديوهات تعليمية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-semibold">كيفية إنشاء حساب جديد</h4>
                  <p className="text-sm text-gray-600">5 دقائق</p>
                </div>
                <Button size="sm" variant="outline">
                  <Play className="h-4 w-4 ml-2" />
                  مشاهدة
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-semibold">المشاركة في المزادات</h4>
                  <p className="text-sm text-gray-600">8 دقائق</p>
                </div>
                <Button size="sm" variant="outline">
                  <Play className="h-4 w-4 ml-2" />
                  مشاهدة
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-semibold">إنشاء مزاد جديد</h4>
                  <p className="text-sm text-gray-600">12 دقيقة</p>
                </div>
                <Button size="sm" variant="outline">
                  <Play className="h-4 w-4 ml-2" />
                  مشاهدة
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                ملفات للتحميل
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-semibold">دليل المستخدم الكامل</h4>
                  <p className="text-sm text-gray-600">PDF - 2.5 MB</p>
                </div>
                <Button size="sm" variant="outline">
                  <Download className="h-4 w-4 ml-2" />
                  تحميل
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-semibold">نماذج الوثائق المطلوبة</h4>
                  <p className="text-sm text-gray-600">ZIP - 1.2 MB</p>
                </div>
                <Button size="sm" variant="outline">
                  <Download className="h-4 w-4 ml-2" />
                  تحميل
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-semibold">شروط وأحكام المنصة</h4>
                  <p className="text-sm text-gray-600">PDF - 800 KB</p>
                </div>
                <Button size="sm" variant="outline">
                  <Download className="h-4 w-4 ml-2" />
                  تحميل
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Links */}
        <Card>
          <CardHeader>
            <CardTitle className="text-center">روابط مفيدة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-4 gap-4">
              <Link href="/faq">
                <Button variant="outline" className="w-full">
                  <BookOpen className="h-4 w-4 ml-2" />
                  الأسئلة الشائعة
                </Button>
              </Link>
              <Link href="/contact">
                <Button variant="outline" className="w-full">
                  <ArrowRight className="h-4 w-4 ml-2" />
                  تواصل معنا
                </Button>
              </Link>
              <Link href="/support">
                <Button variant="outline" className="w-full">
                  <ArrowRight className="h-4 w-4 ml-2" />
                  مركز الدعم
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button className="w-full">
                  <ArrowRight className="h-4 w-4 ml-2" />
                  ابدأ الآن
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
