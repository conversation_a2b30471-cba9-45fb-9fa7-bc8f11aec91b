'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import DashboardLayout from '@/components/DashboardLayout'
import { Search, Eye, Heart, Clock, TrendingUp } from 'lucide-react'
import { auctionAPI, favoritesAPI } from '@/lib/api'
import { useRouter } from 'next/navigation'
import { useToast } from '@/components/ui/use-toast'


export default function UserAuctionsPage() {
  const [auctions, setAuctions] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredAuctions, setFilteredAuctions] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [savedAuctions, setSavedAuctions] = useState<Set<string>>(new Set())
  const [user, setUser] = useState<any>(null)
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    // Get user data and check permissions
    const userData = localStorage.getItem('user')
    if (userData) {
      const parsedUser = JSON.parse(userData)
      setUser(parsedUser)

      // Redirect admins to their proper dashboard
      if (parsedUser.role === 'admin' || parsedUser.role === 'super_admin') {
        toast({
          title: 'وصول غير مسموح',
          description: 'المديرون يجب أن يستخدموا لوحة إدارة المزادات',
          variant: 'destructive'
        })
        router.push('/admin/auctions')
        return
      }

      // Redirect government users to their dashboard
      if (parsedUser.role === 'government') {
        toast({
          title: 'وصول غير مسموح',
          description: 'الجهات الحكومية تستخدم نظام المناقصات',
          variant: 'destructive'
        })
        router.push('/government/dashboard')
        return
      }
    }
  }, [])

  useEffect(() => {
    loadAuctions()
    loadSavedAuctions()
  }, [])

  const loadAuctions = async () => {
    try {
      setLoading(true)
      const response = await auctionAPI.getAll()

      console.log('Auctions API response:', response.data)

      // Handle the API response structure properly
      let auctionData = []
      if (response.data && response.data.success) {
        // If the response has a success field, extract the auctions from data.data.auctions
        auctionData = response.data.data?.auctions || []
      } else if (response.data && Array.isArray(response.data)) {
        // If response.data is directly an array
        auctionData = response.data
      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        // If the auctions are in response.data.data
        auctionData = response.data.data
      }

      // Transform the data to match the expected format
      const transformedData = auctionData.map((auction: any) => ({
        id: auction._id || auction.id,
        title: auction.title,
        description: auction.description,
        currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,
        endDate: auction.endDate,
        bidsCount: auction.bids?.length || auction.bidsCount || 0,
        seller: auction.seller?.profile?.fullName || auction.seller?.profile?.companyName || auction.seller || 'غير محدد',
        category: auction.category,
        status: auction.status,
        views: auction.views || 0,
        location: auction.location
      }))

      setAuctions(transformedData)
      setFilteredAuctions(transformedData)
    } catch (error) {
      console.error('Error loading auctions:', error)
      setAuctions([])
      setFilteredAuctions([])

      toast({
        title: 'فشل في تحميل المزادات',
        description: 'حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const loadSavedAuctions = async () => {
    try {
      const response = await favoritesAPI.getFavorites({ type: 'auction' })
      const savedIds = new Set<string>(response.data.map((fav: any) => fav.itemId))
      setSavedAuctions(savedIds)
    } catch (error) {
      console.error('Error loading saved auctions:', error)
    }
  }

  useEffect(() => {
    // Ensure auctions is an array before filtering
    if (Array.isArray(auctions)) {
      const filtered = auctions.filter(auction =>
        auction.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        auction.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredAuctions(filtered)
    } else {
      setFilteredAuctions([])
    }
  }, [searchTerm, auctions])

  const getTimeRemaining = (endDate: string) => {
    const now = new Date()
    const end = new Date(endDate)
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return 'انتهى'

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(hours / 24)

    if (days > 0) return `${days} يوم`
    return `${hours} ساعة`
  }

  // Check if user can bid
  const canUserBid = (auction: any) => {
    if (!user) return false

    // Only individuals and companies can bid
    if (user.role !== 'individual' && user.role !== 'company') {
      return false
    }

    // Companies cannot bid on their own auctions
    if (user.role === 'company' && auction.seller === user.profile?.companyName) {
      return false
    }

    return true
  }

  const handleBid = async (auctionId: string) => {
    console.log('🎯 Bid button clicked for auction:', auctionId)

    try {
      const currentAuction = auctions.find(a => a.id === auctionId)
      if (!currentAuction) {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المزاد',
          variant: 'destructive'
        })
        return
      }

      // Check if user can bid
      if (!canUserBid(currentAuction)) {
        if (user?.role === 'admin' || user?.role === 'super_admin') {
          toast({
            title: 'وصول غير مسموح',
            description: 'المديرون لا يمكنهم المزايدة على المزادات',
            variant: 'destructive'
          })
        } else if (user?.role === 'government') {
          toast({
            title: 'وصول غير مسموح',
            description: 'الجهات الحكومية لا تزايد على المزادات',
            variant: 'destructive'
          })
        } else if (user?.role === 'company') {
          toast({
            title: 'وصول غير مسموح',
            description: 'لا يمكنك المزايدة على مزادك الخاص',
            variant: 'destructive'
          })
        } else {
          toast({
            title: 'وصول غير مسموح',
            description: 'غير مسموح لك بالمزايدة',
            variant: 'destructive'
          })
        }
        return
      }

      // Show immediate feedback that button was clicked
      toast({
        title: '💆 تم الضغط على زر المزايدة',
        description: 'جاري معالجة المزايدة...'
      })
      
      // Prompt user for bid amount
      const bidAmountStr = prompt(
        `المزايدة الحالية: ${currentAuction.currentBid?.toLocaleString() || 0} ر.س\n\nأدخل مقدار مزايدتك:`,
        String((currentAuction.currentBid || 0) + 1000)
      )
      
      if (!bidAmountStr) return // User cancelled
      
      const bidAmount = parseFloat(bidAmountStr)
      if (isNaN(bidAmount) || bidAmount <= (currentAuction.currentBid || 0)) {
        toast({
          title: 'مزايدة غير صالحة',
          description: 'يجب أن تكون المزايدة أكبر من المزايدة الحالية',
          variant: 'destructive'
        })
        return
      }
      
      setLoading(true)
      await auctionAPI.placeBid(auctionId, bidAmount)
      
      // Update local state optimistically
      setAuctions(prev => prev.map(auction => 
        auction.id === auctionId 
          ? { 
              ...auction, 
              currentBid: bidAmount, 
              bidsCount: (auction.bidsCount || 0) + 1 
            }
          : auction
      ))
      
      setFilteredAuctions(prev => prev.map(auction => 
        auction.id === auctionId 
          ? { 
              ...auction, 
              currentBid: bidAmount, 
              bidsCount: (auction.bidsCount || 0) + 1 
            }
          : auction
      ))
      
      toast({
        title: 'تم تقديم المزايدة بنجاح! 🎉',
        description: `مقدار المزايدة: ${bidAmount.toLocaleString()} ر.س`
      })
    } catch (error: any) {
      console.error('Error placing bid:', error)
      toast({
        title: 'فشل في تقديم المزايدة',
        description: error.response?.data?.message || 'يرجى المحاولة مرة أخرى',
        variant: 'destructive'
      })
      // Reload data on error to sync with server
      loadAuctions()
    } finally {
      setLoading(false)
    }
  }

  const handleSaveAuction = async (auctionId: string) => {
    console.log('❤️ Save button clicked for auction:', auctionId)
    
    // Show immediate feedback that button was clicked
    toast({
      title: '💆 تم الضغط على زر الحفظ',
      description: 'جاري معالجة الطلب...'
    })
    
    try {
      const auction = auctions.find(a => a.id === auctionId)
      if (!auction) {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المزاد',
          variant: 'destructive'
        })
        return
      }
      
      const isCurrentlySaved = savedAuctions.has(auctionId)
      
      // Optimistic UI update
      setSavedAuctions(prev => {
        const newSet = new Set(prev)
        if (isCurrentlySaved) {
          newSet.delete(auctionId)
        } else {
          newSet.add(auctionId)
        }
        return newSet
      })
      
      if (isCurrentlySaved) {
        // Remove from favorites
        await favoritesAPI.removeFavorite('auction', auctionId)
        toast({
          title: '❤️ تم إزالة من المفضلة',
          description: `تم إزالة "${auction.title}" من المفضلة`
        })
      } else {
        // Add to favorites
        await favoritesAPI.addFavorite({
          itemType: 'auction',
          itemId: auctionId,
          notifications: {
            bidUpdates: true,
            statusChanges: true,
            endingSoon: true
          }
        })
        toast({
          title: '💖 تم إضافة إلى المفضلة',
          description: `تم إضافة "${auction.title}" إلى المفضلة`
        })
      }
    } catch (error: any) {
      console.error('Error saving auction:', error)
      // Revert optimistic update on error
      setSavedAuctions(prev => {
        const newSet = new Set(prev)
        if (savedAuctions.has(auctionId)) {
          newSet.add(auctionId)
        } else {
          newSet.delete(auctionId)
        }
        return newSet
      })
      toast({
        title: 'فشل في حفظ المزاد',
        description: error.response?.data?.message || 'يرجى المحاولة مرة أخرى',
        variant: 'destructive'
      })
    }
  }

  const handleViewAuction = async (auctionId: string) => {
    console.log('👁️ View button clicked for auction:', auctionId)
    
    // Show immediate feedback that button was clicked
    toast({
      title: '💆 تم الضغط على زر العرض',
      description: 'جاري تحميل التفاصيل...'
    })
    
    try {
      const auction = auctions.find(a => a.id === auctionId)
      if (!auction) {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المزاد',
          variant: 'destructive'
        })
        return
      }
      
      // For now, show auction details in an alert - in real app this would navigate to detail page
      const details = `عنوان المزاد: ${auction.title}\n` +
                     `الوصف: ${auction.description || 'غير متوفر'}\n` +
                     `المزايدة الحالية: ${(auction.currentBid || 0).toLocaleString()} ر.س\n` +
                     `عدد المزايدات: ${auction.bidsCount || 0}\n` +
                     `البائع: ${auction.seller || 'غير معروف'}\n` +
                     `ينتهي: ${auction.endDate || 'غير محدد'}`
      
      alert(details)
      
      // Try to fetch fresh data from API
      try {
        const response = await auctionAPI.getById(auctionId)
        console.log('Fresh auction data:', response.data)
        toast({
          title: '👁️ تم عرض التفاصيل',
          description: 'تم تحديث بيانات المزاد من الخادم'
        })
      } catch (apiError) {
        console.log('Could not fetch fresh data, showing cached data')
        toast({
          title: '👁️ عرض التفاصيل',
          description: 'يتم عرض البيانات المحفوظة محلياً'
        })
      }
    } catch (error: any) {
      console.error('Error viewing auction:', error)
      toast({
        title: 'فشل في عرض تفاصيل المزاد',
        description: error.response?.data?.message || 'يرجى المحاولة مرة أخرى',
        variant: 'destructive'
      })
    }
  }

  return (
    <DashboardLayout allowedRoles={['individual']}>
      <div className="space-y-6">
        <header>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">المزادات المتاحة</h1>
              <p className="text-muted-foreground">استكشف المزادات الحالية وشارك في المزايدة</p>
            </div>
            <Button 
              onClick={() => {
                alert('🎉 Test button works! Buttons are functional.')
                console.log('Test button clicked successfully!')
                toast({
                  title: '✅ الأزرار تعمل بشكل طبيعي',
                  description: 'يمكنك الآن استخدام أزرار المزادات'
                })
              }}
              variant="outline"
            >
              🧪 اختبار الأزرار
            </Button>
          </div>
        </header>

        {/* Search Bar */}
        <Card>
          <CardContent className="pt-6">
            <div className="relative">
              <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="ابحث في المزادات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        {Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">إجمالي المزادات</CardTitle>
                <CardDescription className="text-2xl font-bold text-blue-600">
                  {filteredAuctions.length}
                </CardDescription>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">المزايدات النشطة</CardTitle>
                <CardDescription className="text-2xl font-bold text-green-600">
                  {filteredAuctions.reduce((sum, a) => sum + (a.bidsCount || 0), 0)}
                </CardDescription>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">أعلى مزايدة</CardTitle>
                <CardDescription className="text-2xl font-bold text-purple-600">
                  {filteredAuctions.length > 0
                    ? Math.max(...filteredAuctions.map(a => a.currentBid || 0)).toLocaleString() + ' ر.س'
                    : '0 ر.س'
                  }
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        )}

        {/* Auctions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.isArray(filteredAuctions) && filteredAuctions.map((auction) => (
            <Card key={auction.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="aspect-video bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
                  <span className="text-gray-500">صورة المنتج</span>
                </div>
                <CardTitle className="text-lg line-clamp-2">{auction.title}</CardTitle>
                <CardDescription className="line-clamp-2">
                  {auction.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">أعلى مزايدة</p>
                    <p className="text-xl font-bold text-green-600">
                      {(auction.currentBid || 0).toLocaleString()} ر.س
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">المزايدات</p>
                    <p className="text-lg font-semibold">{auction.bidsCount || 0}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    ينتهي خلال: {getTimeRemaining(auction.endDate)}
                  </span>
                </div>

                <div className="text-xs text-muted-foreground">
                  البائع: {auction.seller}
                </div>

                <div className="flex gap-2">
                  <Button 
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      alert('Bid button clicked for auction: ' + auction.id)
                      console.log('BID BUTTON CLICKED for auction:', auction.id)
                      handleBid(auction.id)
                    }}
                    className="flex-1"
                    disabled={loading}
                  >
                    <TrendingUp className="h-4 w-4 ml-2" />
                    {loading ? 'جاري المزايدة...' : 'مزايدة'}
                  </Button>
                  <Button 
                    variant={savedAuctions.has(auction.id) ? "default" : "outline"}
                    size="icon"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      alert('Save button clicked for auction: ' + auction.id)
                      console.log('SAVE BUTTON CLICKED for auction:', auction.id)
                      handleSaveAuction(auction.id)
                    }}
                    disabled={loading}
                  >
                    <Heart className={`h-4 w-4 ${savedAuctions.has(auction.id) ? 'fill-current' : ''}`} />
                  </Button>
                  <Button 
                    variant="outline" 
                    size="icon"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      alert('View button clicked for auction: ' + auction.id)
                      console.log('VIEW BUTTON CLICKED for auction:', auction.id)
                      handleViewAuction(auction.id)
                    }}
                    disabled={loading}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No data state */}
        {!loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && (
          <Card>
            <CardContent className="py-16 text-center">
              <div className="flex flex-col items-center gap-4">
                <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center">
                  <TrendingUp className="h-8 w-8 text-gray-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">
                    {searchTerm ? 'لا توجد مزادات تطابق البحث' : 'لا توجد مزادات متاحة حالياً'}
                  </h3>
                  <p className="text-muted-foreground">
                    {searchTerm 
                      ? 'جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات'
                      : 'سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات'
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Loading state */}
        {loading && (
          <Card>
            <CardContent className="py-16 text-center">
              <div className="flex flex-col items-center gap-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p className="text-muted-foreground">جاري تحميل المزادات...</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
