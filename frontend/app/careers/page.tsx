'use client'

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Building, 
  Briefcase, 
  MapPin, 
  Clock, 
  DollarSign, 
  Users, 
  Search,
  Filter,
  Heart,
  Star,
  Award,
  TrendingUp,
  Coffee,
  Zap
} from 'lucide-react';
import Link from 'next/link';

export default function CareersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [selectedType, setSelectedType] = useState('all');

  const jobs = [
    {
      id: 1,
      title: "مطور Full Stack",
      department: "التقنية",
      type: "دوام كامل",
      location: "الرياض",
      salary: "15,000 - 25,000 ريال",
      experience: "3-5 سنوات",
      description: "نبحث عن مطور Full Stack متمرس للانضمام إلى فريق التطوير لدينا",
      requirements: ["خبرة في React و Node.js", "معرفة بقواعد البيانات", "خبرة في AWS"],
      posted: "منذ 3 أيام",
      urgent: false
    },
    {
      id: 2,
      title: "مصمم UI/UX",
      department: "التصميم",
      type: "دوام كامل",
      location: "الرياض",
      salary: "12,000 - 18,000 ريال",
      experience: "2-4 سنوات",
      description: "مصمم واجهات مستخدم مبدع لتطوير تجربة مستخدم استثنائية",
      requirements: ["إتقان Figma و Adobe Creative Suite", "خبرة في تصميم المنصات", "فهم UX principles"],
      posted: "منذ 5 أيام",
      urgent: true
    },
    {
      id: 3,
      title: "محلل أمان سيبراني",
      department: "الأمان",
      type: "دوام كامل",
      location: "الرياض",
      salary: "18,000 - 28,000 ريال",
      experience: "4-7 سنوات",
      description: "خبير أمان سيبراني لحماية منصتنا وبيانات عملائنا",
      requirements: ["شهادات أمان معتمدة", "خبرة في penetration testing", "معرفة بـ compliance frameworks"],
      posted: "منذ أسبوع",
      urgent: false
    },
    {
      id: 4,
      title: "مختص تسويق رقمي",
      department: "التسويق",
      type: "دوام كامل",
      location: "الرياض",
      salary: "10,000 - 15,000 ريال",
      experience: "2-3 سنوات",
      description: "مختص تسويق رقمي لإدارة حملاتنا الإعلانية وزيادة نمو المنصة",
      requirements: ["خبرة في Google Ads و Facebook Ads", "تحليل البيانات", "إدارة وسائل التواصل"],
      posted: "منذ يومين",
      urgent: true
    },
    {
      id: 5,
      title: "مطور تطبيقات الجوال",
      department: "التقنية",
      type: "دوام جزئي",
      location: "عن بُعد",
      salary: "8,000 - 12,000 ريال",
      experience: "2-4 سنوات",
      description: "مطور تطبيقات جوال لتطوير تطبيق المنصة على iOS و Android",
      requirements: ["React Native أو Flutter", "خبرة في App Store deployment", "معرفة بـ mobile UX"],
      posted: "منذ 4 أيام",
      urgent: false
    },
    {
      id: 6,
      title: "مدير منتج",
      department: "المنتج",
      type: "دوام كامل",
      location: "الرياض",
      salary: "20,000 - 30,000 ريال",
      experience: "5-8 سنوات",
      description: "مدير منتج لقيادة استراتيجية تطوير المنصة وتحسين تجربة المستخدم",
      requirements: ["خبرة في product management", "فهم عميق للـ marketplace", "مهارات قيادية"],
      posted: "منذ 6 أيام",
      urgent: true
    }
  ];

  const benefits = [
    { icon: DollarSign, title: "راتب تنافسي", description: "رواتب تنافسية مع مراجعة سنوية" },
    { icon: Heart, title: "تأمين صحي", description: "تأمين صحي شامل للموظف والعائلة" },
    { icon: Clock, title: "مرونة في العمل", description: "ساعات عمل مرنة وإمكانية العمل عن بُعد" },
    { icon: TrendingUp, title: "نمو مهني", description: "برامج تدريب وتطوير مستمرة" },
    { icon: Coffee, title: "بيئة عمل مميزة", description: "مكتب حديث مع جميع وسائل الراحة" },
    { icon: Award, title: "مكافآت الأداء", description: "مكافآت ربع سنوية وسنوية للأداء المتميز" }
  ];

  const departments = ["all", "التقنية", "التصميم", "التسويق", "المنتج", "الأمان", "العمليات"];
  const jobTypes = ["all", "دوام كامل", "دوام جزئي", "عن بُعد", "تدريب"];

  const filteredJobs = jobs.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDepartment = selectedDepartment === 'all' || job.department === selectedDepartment;
    const matchesType = selectedType === 'all' || job.type === selectedType;
    return matchesSearch && matchesDepartment && matchesType;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Building className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">منصة المزادات</h1>
              <p className="text-sm text-gray-600">والمناقصات</p>
            </div>
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-blue-100 text-blue-800">الوظائف</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            انضم إلى <span className="text-blue-600">فريقنا المتميز</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            اكتشف الفرص الوظيفية المتاحة وكن جزءاً من رحلة تطوير مستقبل التجارة الإلكترونية
          </p>

          {/* Search and Filters */}
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-3 gap-4 mb-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  type="text"
                  placeholder="ابحث عن وظيفة..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4"
                />
              </div>
              
              <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر القسم" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأقسام</SelectItem>
                  {departments.slice(1).map((dept) => (
                    <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="نوع الوظيفة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  {jobTypes.slice(1).map((type) => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Job Listings */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                الوظائف المتاحة ({filteredJobs.length})
              </h2>
            </div>

            <div className="space-y-6">
              {filteredJobs.map((job) => (
                <Card key={job.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-xl font-semibold text-gray-900">{job.title}</h3>
                          {job.urgent && (
                            <Badge className="bg-red-100 text-red-800">عاجل</Badge>
                          )}
                        </div>
                        <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                          <div className="flex items-center gap-1">
                            <Building className="h-4 w-4" />
                            {job.department}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {job.type}
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            {job.location}
                          </div>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4" />
                            {job.salary}
                          </div>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">{job.posted}</div>
                    </div>
                    
                    <p className="text-gray-700 mb-4">{job.description}</p>
                    
                    <div className="mb-4">
                      <h4 className="font-semibold mb-2">المتطلبات:</h4>
                      <div className="flex flex-wrap gap-2">
                        {job.requirements.map((req, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {req}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-600">
                        الخبرة المطلوبة: {job.experience}
                      </div>
                      <Button>تقدم للوظيفة</Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredJobs.length === 0 && (
              <Card className="text-center py-12">
                <CardContent>
                  <Briefcase className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد وظائف</h3>
                  <p className="text-gray-600">جرب تغيير معايير البحث أو تصفح جميع الوظائف</p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Company Stats */}
            <Card>
              <CardHeader>
                <CardTitle>إحصائيات الشركة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">عدد الموظفين</span>
                  <span className="font-semibold">43+</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">معدل النمو</span>
                  <span className="font-semibold">200%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">رضا الموظفين</span>
                  <span className="font-semibold">98%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">الوظائف المتاحة</span>
                  <span className="font-semibold">{jobs.length}</span>
                </div>
              </CardContent>
            </Card>

            {/* Quick Apply */}
            <Card>
              <CardHeader>
                <CardTitle>تقديم سريع</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  لم تجد الوظيفة المناسبة؟ أرسل سيرتك الذاتية وسنتواصل معك عند توفر فرصة مناسبة
                </p>
                <Button className="w-full">أرسل السيرة الذاتية</Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Benefits */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">مزايا العمل معنا</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              نوفر بيئة عمل محفزة ومزايا متميزة لضمان رضا وسعادة فريقنا
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <benefit.icon className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">{benefit.title}</h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* CTA */}
        <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <CardContent className="p-12 text-center">
            <h2 className="text-3xl font-bold mb-4">جاهز للانضمام إلينا؟</h2>
            <p className="text-xl mb-8 opacity-90">
              ابدأ رحلتك المهنية معنا وكن جزءاً من قصة نجاح مميزة
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                تصفح الوظائف
              </Button>
              <Link href="/team">
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                  تعرف على الفريق
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
