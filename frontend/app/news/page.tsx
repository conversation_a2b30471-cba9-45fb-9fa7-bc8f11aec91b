'use client'

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Building, 
  Calendar, 
  User, 
  Search, 
  TrendingUp,
  Award,
  Zap,
  Globe,
  Users,
  ArrowRight,
  Clock,
  Eye
} from 'lucide-react';
import Link from 'next/link';

export default function NewsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const newsArticles = [
    {
      id: 1,
      title: "منصة المزادات والمناقصات تحصل على جائزة أفضل منصة تقنية لعام 2024",
      excerpt: "حصلت منصتنا على جائزة التميز في التجارة الإلكترونية من غرفة التجارة السعودية تقديراً لجهودنا في تطوير القطاع",
      content: "في إنجاز مميز للشركة، حصلت منصة المزادات والمناقصات على جائزة أفضل منصة تقنية لعام 2024...",
      category: "جوائز",
      author: "فريق التحرير",
      date: "2024-12-15",
      readTime: "3 دقائق",
      views: 1250,
      featured: true,
      image: "/api/placeholder/400/250"
    },
    {
      id: 2,
      title: "إطلاق ميزة المزايدة التلقائية الذكية",
      excerpt: "ميزة جديدة تتيح للمستخدمين تعيين حد أقصى للمزايدة والمشاركة التلقائية في المزادات",
      content: "أعلنت الشركة عن إطلاق ميزة المزايدة التلقائية الذكية التي تستخدم الذكاء الاصطناعي...",
      category: "منتج",
      author: "نورا الشهري",
      date: "2024-12-10",
      readTime: "5 دقائق",
      views: 890,
      featured: false,
      image: "/api/placeholder/400/250"
    },
    {
      id: 3,
      title: "شراكة استراتيجية مع وزارة التجارة لرقمنة المناقصات الحكومية",
      excerpt: "توقيع اتفاقية شراكة مع وزارة التجارة لتطوير منصة موحدة للمناقصات الحكومية",
      content: "وقعت الشركة اتفاقية شراكة استراتيجية مع وزارة التجارة والاستثمار...",
      category: "شراكات",
      author: "أحمد العلي",
      date: "2024-12-05",
      readTime: "4 دقائق",
      views: 2100,
      featured: true,
      image: "/api/placeholder/400/250"
    },
    {
      id: 4,
      title: "نمو 200% في عدد المستخدمين خلال العام الماضي",
      excerpt: "تحقيق نمو استثنائي في قاعدة المستخدمين مع تجاوز 10,000 مستخدم نشط شهرياً",
      content: "حققت المنصة نمواً استثنائياً بنسبة 200% في عدد المستخدمين النشطين...",
      category: "نمو",
      author: "فاطمة الزهراني",
      date: "2024-11-28",
      readTime: "3 دقائق",
      views: 1560,
      featured: false,
      image: "/api/placeholder/400/250"
    },
    {
      id: 5,
      title: "إطلاق تطبيق الجوال الجديد مع ميزات متقدمة",
      excerpt: "تطبيق جوال محدث بواجهة مستخدم جديدة وميزات ذكية لتحسين تجربة المزايدة",
      content: "أطلقت الشركة النسخة الجديدة من تطبيق الجوال مع تحسينات جذرية...",
      category: "منتج",
      author: "خالد النمر",
      date: "2024-11-20",
      readTime: "4 دقائق",
      views: 980,
      featured: false,
      image: "/api/placeholder/400/250"
    },
    {
      id: 6,
      title: "افتتاح مكتب جديد في جدة لخدمة المنطقة الغربية",
      excerpt: "توسع الشركة جغرافياً مع افتتاح مكتب جديد في جدة لتقديم خدمات أفضل للعملاء",
      content: "في إطار خطة التوسع الاستراتيجية، افتتحت الشركة مكتباً جديداً في مدينة جدة...",
      category: "توسع",
      author: "سارة الدوسري",
      date: "2024-11-15",
      readTime: "3 دقائق",
      views: 750,
      featured: false,
      image: "/api/placeholder/400/250"
    }
  ];

  const categories = ["all", "جوائز", "منتج", "شراكات", "نمو", "توسع", "تقنية"];

  const filteredNews = newsArticles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const featuredNews = newsArticles.filter(article => article.featured);
  const recentNews = newsArticles.slice(0, 5);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Building className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">منصة المزادات</h1>
              <p className="text-sm text-gray-600">والمناقصات</p>
            </div>
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-blue-100 text-blue-800">الأخبار</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            آخر <span className="text-blue-600">الأخبار والتحديثات</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            تابع آخر أخبار المنصة والتطورات الجديدة في عالم المزادات والمناقصات
          </p>

          {/* Search and Filter */}
          <div className="max-w-2xl mx-auto">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  type="text"
                  placeholder="ابحث في الأخبار..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4"
                />
              </div>
              
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر الفئة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  {categories.slice(1).map((category) => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Featured News */}
        {featuredNews.length > 0 && (
          <div className="mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">الأخبار المميزة</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {featuredNews.map((article) => (
                <Card key={article.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="aspect-video bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                    <span className="text-white text-lg font-semibold">صورة الخبر</span>
                  </div>
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <Badge className="bg-red-100 text-red-800">مميز</Badge>
                      <Badge variant="outline">{article.category}</Badge>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                      {article.title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">{article.excerpt}</p>
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          {article.author}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {new Date(article.date).toLocaleDateString('ar-SA')}
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        {article.views}
                      </div>
                    </div>
                    <Button className="w-full">
                      اقرأ المزيد
                      <ArrowRight className="h-4 w-4 mr-2" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* News Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold text-gray-900">
                جميع الأخبار ({filteredNews.length})
              </h2>
            </div>

            <div className="space-y-6">
              {filteredNews.map((article) => (
                <Card key={article.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="grid md:grid-cols-4 gap-6">
                      <div className="md:col-span-1">
                        <div className="aspect-square bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                          <span className="text-white text-sm font-semibold">صورة</span>
                        </div>
                      </div>
                      <div className="md:col-span-3">
                        <div className="flex items-center gap-2 mb-3">
                          <Badge variant="outline">{article.category}</Badge>
                          <div className="flex items-center gap-1 text-sm text-gray-500">
                            <Clock className="h-4 w-4" />
                            {article.readTime}
                          </div>
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-3">
                          {article.title}
                        </h3>
                        <p className="text-gray-600 mb-4">{article.excerpt}</p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center gap-1">
                              <User className="h-4 w-4" />
                              {article.author}
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {new Date(article.date).toLocaleDateString('ar-SA')}
                            </div>
                            <div className="flex items-center gap-1">
                              <Eye className="h-4 w-4" />
                              {article.views}
                            </div>
                          </div>
                          <Button variant="outline" size="sm">
                            اقرأ المزيد
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredNews.length === 0 && (
              <Card className="text-center py-12">
                <CardContent>
                  <Search className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد أخبار</h3>
                  <p className="text-gray-600">جرب تغيير معايير البحث أو تصفح جميع الأخبار</p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Recent News */}
            <Card>
              <CardHeader>
                <CardTitle>الأخبار الحديثة</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {recentNews.map((article) => (
                  <div key={article.id} className="border-b border-gray-200 last:border-0 pb-4 last:pb-0">
                    <h4 className="font-semibold text-sm mb-2 line-clamp-2">{article.title}</h4>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Calendar className="h-3 w-3" />
                      {new Date(article.date).toLocaleDateString('ar-SA')}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Categories */}
            <Card>
              <CardHeader>
                <CardTitle>الفئات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {categories.slice(1).map((category) => {
                    const count = newsArticles.filter(article => article.category === category).length;
                    return (
                      <div key={category} className="flex items-center justify-between">
                        <span className="text-gray-700">{category}</span>
                        <Badge variant="outline">{count}</Badge>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Newsletter */}
            <Card>
              <CardHeader>
                <CardTitle>النشرة الإخبارية</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  اشترك في نشرتنا الإخبارية لتصلك آخر الأخبار والتحديثات
                </p>
                <div className="space-y-3">
                  <Input placeholder="بريدك الإلكتروني" />
                  <Button className="w-full">اشتراك</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Stats */}
        <div className="mt-16">
          <Card>
            <CardContent className="p-8">
              <div className="grid md:grid-cols-4 gap-8 text-center">
                <div>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <TrendingUp className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">50+</div>
                  <div className="text-sm text-gray-600">خبر منشور</div>
                </div>
                
                <div>
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Users className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">10K+</div>
                  <div className="text-sm text-gray-600">قارئ شهرياً</div>
                </div>
                
                <div>
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Award className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">5</div>
                  <div className="text-sm text-gray-600">جوائز حصلنا عليها</div>
                </div>
                
                <div>
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Globe className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">3</div>
                  <div className="text-sm text-gray-600">دول نخدمها</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
