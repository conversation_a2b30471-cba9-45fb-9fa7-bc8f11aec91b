import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Building, 
  Users, 
  Target, 
  Award, 
  Shield, 
  Globe, 
  TrendingUp,
  CheckCircle,
  Star,
  Heart,
  Zap,
  Clock
} from 'lucide-react';
import Link from 'next/link';

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Building className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">منصة المزادات</h1>
              <p className="text-sm text-gray-600">والمناقصات</p>
            </div>
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-blue-100 text-blue-800">من نحن</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            نحن نعيد تعريف <span className="text-blue-600">التجارة الإلكترونية</span> في المنطقة
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            منصة متطورة تجمع بين أحدث التقنيات والخبرة العميقة في مجال المزادات والمناقصات، 
            نهدف إلى توفير بيئة آمنة وشفافة للتجارة الإلكترونية تخدم جميع فئات المجتمع.
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">10,000+</div>
              <div className="text-sm text-gray-600">مستخدم نشط</div>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">5,000+</div>
              <div className="text-sm text-gray-600">مزاد مكتمل</div>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Building className="h-6 w-6 text-purple-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">500+</div>
              <div className="text-sm text-gray-600">شركة مسجلة</div>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Award className="h-6 w-6 text-orange-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">50M+</div>
              <div className="text-sm text-gray-600">ريال تداول</div>
            </CardContent>
          </Card>
        </div>

        {/* Vision & Mission */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Target className="h-6 w-6 text-blue-600" />
                رؤيتنا
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 leading-relaxed">
                أن نكون المنصة الرائدة والأكثر ثقة في منطقة الشرق الأوسط للمزادات والمناقصات الإلكترونية، 
                نربط بين الشركات والأفراد والجهات الحكومية لتحقيق أفضل النتائج للجميع.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Heart className="h-6 w-6 text-red-600" />
                مهمتنا
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 leading-relaxed">
                توفير منصة تقنية متطورة وآمنة تسهل عمليات التجارة الإلكترونية وتضمن الشفافية والعدالة 
                في جميع المعاملات، مع تقديم خدمة عملاء استثنائية ودعم مستمر لجميع المستخدمين.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Values */}
        <Card className="mb-16">
          <CardHeader>
            <CardTitle className="text-center text-2xl">قيمنا الأساسية</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3">الأمان والثقة</h3>
                <p className="text-gray-600">
                  نضع أمان بياناتك ومعاملاتك في المقدمة مع أعلى معايير الحماية والتشفير
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3">الشفافية</h3>
                <p className="text-gray-600">
                  جميع العمليات واضحة ومفهومة مع إمكانية تتبع كامل لجميع المراحل
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold mb-3">الابتكار</h3>
                <p className="text-gray-600">
                  نستخدم أحدث التقنيات لتوفير تجربة مستخدم متميزة وحلول مبتكرة
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Why Choose Us */}
        <Card className="mb-16">
          <CardHeader>
            <CardTitle className="text-center text-2xl">لماذا تختارنا؟</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">أكثر من 10,000 مستخدم نشط</h4>
                    <p className="text-gray-600 text-sm">مجتمع كبير ونشط من المشترين والبائعين</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">معدل نجاح 95% في إتمام المعاملات</h4>
                    <p className="text-gray-600 text-sm">نظام موثوق يضمن إتمام معاملاتك بنجاح</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">دعم فني متاح 24/7</h4>
                    <p className="text-gray-600 text-sm">فريق دعم متخصص جاهز لمساعدتك في أي وقت</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">رسوم تنافسية وشفافة</h4>
                    <p className="text-gray-600 text-sm">لا توجد رسوم خفية، عمولة 2% فقط على المعاملات الناجحة</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">تقنية متطورة وآمنة</h4>
                    <p className="text-gray-600 text-sm">منصة حديثة مبنية بأحدث التقنيات وأعلى معايير الأمان</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">متاح 24/7 من أي مكان</h4>
                    <p className="text-gray-600 text-sm">وصول كامل للمنصة من أي جهاز وفي أي وقت</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Awards & Recognition */}
        <Card className="mb-16">
          <CardHeader>
            <CardTitle className="text-center text-2xl">الجوائز والتقديرات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="h-8 w-8 text-yellow-600" />
                </div>
                <h3 className="text-lg font-semibold mb-2">أفضل منصة 2024</h3>
                <p className="text-gray-600 text-sm">جائزة التميز في التجارة الإلكترونية</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold mb-2">نمو 200%</h3>
                <p className="text-gray-600 text-sm">زيادة في عدد المستخدمين سنوياً</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Star className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold mb-2">رضا 98%</h3>
                <p className="text-gray-600 text-sm">معدل رضا العملاء عن خدماتنا</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-12 text-white">
          <h2 className="text-3xl font-bold mb-4">جاهز للانضمام إلى منصتنا؟</h2>
          <p className="text-xl mb-8 opacity-90">
            ابدأ رحلتك معنا اليوم واكتشف عالم جديد من الفرص التجارية
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/register">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                ابدأ مجاناً
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                تواصل معنا
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
