'use client'

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  Building, 
  Send,
  MessageCircle,
  Globe,
  Facebook,
  Twitter,
  Linkedin,
  Instagram
} from 'lucide-react';
import Link from 'next/link';

export default function ContactPage() {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    category: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.email || !formData.subject || !formData.message) {
      toast({
        title: 'بيانات ناقصة',
        description: 'يرجى ملء جميع الحقول المطلوبة',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      toast({
        title: 'تم إرسال الرسالة',
        description: 'شكراً لتواصلك معنا. سنرد عليك في أقرب وقت ممكن.'
      });
      
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        category: '',
        message: ''
      });
      
      setIsSubmitting(false);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Building className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">منصة المزادات</h1>
              <p className="text-sm text-gray-600">والمناقصات</p>
            </div>
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <Badge className="mb-4 bg-blue-100 text-blue-800">تواصل معنا</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            نحن هنا <span className="text-blue-600">لمساعدتك</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            تواصل معنا في أي وقت وسنكون سعداء للإجابة على استفساراتك ومساعدتك
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Contact Information */}
          <div className="lg:col-span-1 space-y-6">
            {/* Contact Methods */}
            <Card>
              <CardHeader>
                <CardTitle>طرق التواصل</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Phone className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">الهاتف</h4>
                    <p className="text-gray-600">+966 11 123 4567</p>
                    <p className="text-sm text-gray-500">متاح 24/7</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Mail className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">البريد الإلكتروني</h4>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-sm text-gray-500">للاستفسارات العامة</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <MessageCircle className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">الدعم الفني</h4>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-sm text-gray-500">للمساعدة التقنية</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <MapPin className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">العنوان</h4>
                    <p className="text-gray-600">الرياض، المملكة العربية السعودية</p>
                    <p className="text-sm text-gray-500">مركز الملك عبدالله المالي</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Working Hours */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  ساعات العمل
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">الأحد - الخميس</span>
                  <span className="font-semibold">8:00 ص - 5:00 م</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">الجمعة</span>
                  <span className="font-semibold">مغلق</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">السبت</span>
                  <span className="font-semibold">10:00 ص - 2:00 م</span>
                </div>
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>الدعم الفني متاح 24/7</strong> عبر الموقع والبريد الإلكتروني
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Social Media */}
            <Card>
              <CardHeader>
                <CardTitle>تابعنا على</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-3">
                  <a href="#" className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center hover:bg-blue-200 transition-colors">
                    <Facebook className="h-5 w-5 text-blue-600" />
                  </a>
                  <a href="#" className="w-10 h-10 bg-sky-100 rounded-lg flex items-center justify-center hover:bg-sky-200 transition-colors">
                    <Twitter className="h-5 w-5 text-sky-600" />
                  </a>
                  <a href="#" className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center hover:bg-blue-200 transition-colors">
                    <Linkedin className="h-5 w-5 text-blue-700" />
                  </a>
                  <a href="#" className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center hover:bg-pink-200 transition-colors">
                    <Instagram className="h-5 w-5 text-pink-600" />
                  </a>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">أرسل لنا رسالة</CardTitle>
                <p className="text-gray-600">املأ النموذج أدناه وسنتواصل معك في أقرب وقت ممكن</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="name">الاسم الكامل *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="أدخل اسمك الكامل"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">البريد الإلكتروني *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="أدخل بريدك الإلكتروني"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">رقم الهاتف</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="أدخل رقم هاتفك"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="category">فئة الاستفسار</Label>
                      <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر فئة الاستفسار" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="general">استفسار عام</SelectItem>
                          <SelectItem value="technical">دعم فني</SelectItem>
                          <SelectItem value="billing">الفواتير والمدفوعات</SelectItem>
                          <SelectItem value="account">مشاكل الحساب</SelectItem>
                          <SelectItem value="partnership">شراكات تجارية</SelectItem>
                          <SelectItem value="complaint">شكوى</SelectItem>
                          <SelectItem value="suggestion">اقتراح</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subject">موضوع الرسالة *</Label>
                    <Input
                      id="subject"
                      value={formData.subject}
                      onChange={(e) => handleInputChange('subject', e.target.value)}
                      placeholder="أدخل موضوع رسالتك"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message">الرسالة *</Label>
                    <Textarea
                      id="message"
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      placeholder="اكتب رسالتك هنا..."
                      rows={6}
                      required
                    />
                  </div>

                  <div className="flex gap-4">
                    <Button type="submit" disabled={isSubmitting} className="flex-1">
                      <Send className="h-4 w-4 ml-2" />
                      {isSubmitting ? 'جاري الإرسال...' : 'إرسال الرسالة'}
                    </Button>
                    <Link href="/support">
                      <Button type="button" variant="outline">
                        <MessageCircle className="h-4 w-4 ml-2" />
                        الدردشة المباشرة
                      </Button>
                    </Link>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Quick Links */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle className="text-center">روابط مفيدة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-4 gap-4">
                <Link href="/faq">
                  <Button variant="outline" className="w-full">
                    الأسئلة الشائعة
                  </Button>
                </Link>
                <Link href="/guide">
                  <Button variant="outline" className="w-full">
                    دليل المستخدم
                  </Button>
                </Link>
                <Link href="/support">
                  <Button variant="outline" className="w-full">
                    مركز الدعم
                  </Button>
                </Link>
                <Link href="/about">
                  <Button variant="outline" className="w-full">
                    من نحن
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
