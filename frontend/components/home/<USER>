'use client'

import { useState, useEffect, useRef } from 'react'
import { TrendingUp, Users, Gavel, Building, DollarSign, Award } from 'lucide-react'

export default function StatsSection() {
  const [isVisible, setIsVisible] = useState(false)
  const [counters, setCounters] = useState({
    users: 0,
    auctions: 0,
    companies: 0,
    volume: 0
  })
  const sectionRef = useRef<HTMLElement>(null)

  const [finalValues, setFinalValues] = useState({
    users: 0,
    auctions: 0,
    companies: 0,
    volume: 0
  })

  useEffect(() => {
    // Load real stats from API
    loadStats()
  }, [])

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          // Start counter animation
          animateCounters()
        }
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [finalValues])

  const loadStats = async () => {
    try {
      const response = await fetch('/api/stats/public')
      const data = await response.json()

      if (data.success) {
        setFinalValues(data.data.stats)
      } else {
        setFinalValues({
          users: 0,
          auctions: 0,
          companies: 0,
          volume: 0
        })
      }
    } catch (error) {
      console.error('Error loading stats:', error)
      setFinalValues({
        users: 0,
        auctions: 0,
        companies: 0,
        volume: 0
      })
    }
  }

  const animateCounters = () => {
    const duration = 2000 // 2 seconds
    const steps = 60
    const stepDuration = duration / steps

    let currentStep = 0

    const timer = setInterval(() => {
      currentStep++
      const progress = currentStep / steps

      setCounters({
        users: Math.floor(finalValues.users * progress),
        auctions: Math.floor(finalValues.auctions * progress),
        companies: Math.floor(finalValues.companies * progress),
        volume: Math.floor(finalValues.volume * progress)
      })

      if (currentStep >= steps) {
        clearInterval(timer)
        setCounters(finalValues)
      }
    }, stepDuration)
  }

  const stats = [
    {
      icon: Users,
      value: counters.users.toLocaleString(),
      suffix: '+',
      label: 'مستخدم نشط',
      description: 'يثقون بمنصتنا يومياً',
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: Gavel,
      value: counters.auctions.toLocaleString(),
      suffix: '+',
      label: 'مزاد مكتمل',
      description: 'تم إنجازها بنجاح',
      color: 'from-green-500 to-green-600'
    },
    {
      icon: Building,
      value: counters.companies.toLocaleString(),
      suffix: '+',
      label: 'شركة مسجلة',
      description: 'تستخدم منصتنا',
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: DollarSign,
      value: counters.volume.toLocaleString(),
      suffix: 'M+',
      label: 'مليون ريال',
      description: 'إجمالي قيمة المعاملات',
      color: 'from-orange-500 to-orange-600'
    }
  ]

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute top-0 right-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-0 left-1/3 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className={`text-center mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
            <TrendingUp className="w-4 h-4" />
            إحصائيات المنصة
          </div>
          
          <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
            أرقام تتحدث عن
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400"> نجاحنا</span>
          </h2>
          
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            نفخر بالثقة التي يضعها عملاؤنا فينا والنمو المستمر الذي نحققه معاً
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div
              key={index}
              className={`group relative transition-all duration-1000 ${
                isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
              }`}
              style={{ transitionDelay: `${index * 200}ms` }}
            >
              {/* Card */}
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl">
                {/* Icon */}
                <div className={`w-16 h-16 bg-gradient-to-br ${stat.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <stat.icon className="w-8 h-8 text-white" />
                </div>

                {/* Number */}
                <div className="space-y-2 mb-4">
                  <div className="text-4xl lg:text-5xl font-bold text-white">
                    {stat.value}
                    <span className="text-2xl text-blue-400">{stat.suffix}</span>
                  </div>
                  <div className="text-lg font-semibold text-blue-400">
                    {stat.label}
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-300 text-sm leading-relaxed">
                  {stat.description}
                </p>

                {/* Glow Effect */}
                <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300 pointer-events-none`}></div>
              </div>
            </div>
          ))}
        </div>

        {/* Achievement Badges */}
        <div className={`mt-16 transition-all duration-1000 delay-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: Award,
                title: 'أفضل منصة 2024',
                description: 'جائزة التميز في التجارة الإلكترونية'
              },
              {
                icon: TrendingUp,
                title: 'نمو 200%',
                description: 'زيادة في عدد المستخدمين سنوياً'
              },
              {
                icon: Users,
                title: 'رضا 98%',
                description: 'معدل رضا العملاء عن خدماتنا'
              }
            ].map((achievement, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <achievement.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{achievement.title}</h3>
                <p className="text-gray-300 text-sm">{achievement.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
