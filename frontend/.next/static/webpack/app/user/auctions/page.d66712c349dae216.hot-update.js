"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./app/user/auctions/page.tsx":
/*!************************************!*\
  !*** ./app/user/auctions/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAuctionsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction UserAuctionsPage() {\n    _s();\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredAuctions, setFilteredAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAuctions, setSavedAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data and check permissions\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // Redirect admins to their proper dashboard\n            if (parsedUser.role === \"admin\" || parsedUser.role === \"super_admin\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"المديرون يجب أن يستخدموا لوحة إدارة المزادات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/admin/auctions\");\n                return;\n            }\n            // Redirect government users to their dashboard\n            if (parsedUser.role === \"government\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"الجهات الحكومية تستخدم نظام المناقصات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/dashboard\");\n                return;\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAuctions();\n        loadSavedAuctions();\n    }, []);\n    const loadAuctions = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.auctionAPI.getAll();\n            console.log(\"Auctions API response:\", response.data);\n            // Handle the API response structure properly\n            let auctionData = [];\n            if (response.data && response.data.success) {\n                var _response_data_data;\n                // If the response has a success field, extract the auctions from data.data.auctions\n                auctionData = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.auctions) || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                // If response.data is directly an array\n                auctionData = response.data;\n            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n                // If the auctions are in response.data.data\n                auctionData = response.data.data;\n            }\n            // Transform the data to match the expected format\n            const transformedData = auctionData.map((auction)=>{\n                var _auction_bids, _auction_seller_profile, _auction_seller, _auction_seller_profile1, _auction_seller1;\n                return {\n                    id: auction._id || auction.id,\n                    title: auction.title,\n                    description: auction.description,\n                    currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,\n                    endDate: auction.endDate,\n                    bidsCount: ((_auction_bids = auction.bids) === null || _auction_bids === void 0 ? void 0 : _auction_bids.length) || auction.bidsCount || 0,\n                    seller: ((_auction_seller = auction.seller) === null || _auction_seller === void 0 ? void 0 : (_auction_seller_profile = _auction_seller.profile) === null || _auction_seller_profile === void 0 ? void 0 : _auction_seller_profile.fullName) || ((_auction_seller1 = auction.seller) === null || _auction_seller1 === void 0 ? void 0 : (_auction_seller_profile1 = _auction_seller1.profile) === null || _auction_seller_profile1 === void 0 ? void 0 : _auction_seller_profile1.companyName) || auction.seller || \"غير محدد\",\n                    category: auction.category,\n                    status: auction.status,\n                    views: auction.views || 0,\n                    location: auction.location\n                };\n            });\n            setAuctions(transformedData);\n            setFilteredAuctions(transformedData);\n        } catch (error) {\n            console.error(\"Error loading auctions:\", error);\n            setAuctions([]);\n            setFilteredAuctions([]);\n            toast({\n                title: \"فشل في تحميل المزادات\",\n                description: \"حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadSavedAuctions = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.favoritesAPI.getFavorites({\n                type: \"auction\"\n            });\n            const savedIds = new Set(response.data.map((fav)=>fav.itemId));\n            setSavedAuctions(savedIds);\n        } catch (error) {\n            console.error(\"Error loading saved auctions:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure auctions is an array before filtering\n        if (Array.isArray(auctions)) {\n            const filtered = auctions.filter((auction)=>{\n                var _auction_title, _auction_description;\n                return ((_auction_title = auction.title) === null || _auction_title === void 0 ? void 0 : _auction_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_auction_description = auction.description) === null || _auction_description === void 0 ? void 0 : _auction_description.toLowerCase().includes(searchTerm.toLowerCase()));\n            });\n            setFilteredAuctions(filtered);\n        } else {\n            setFilteredAuctions([]);\n        }\n    }, [\n        searchTerm,\n        auctions\n    ]);\n    const getTimeRemaining = (endDate)=>{\n        const now = new Date();\n        const end = new Date(endDate);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        return \"\".concat(hours, \" ساعة\");\n    };\n    // Check if user can bid\n    const canUserBid = (auction)=>{\n        var _user_profile;\n        if (!user) return false;\n        // Only individuals and companies can bid\n        if (user.role !== \"individual\" && user.role !== \"company\") {\n            return false;\n        }\n        // Companies cannot bid on their own auctions\n        if (user.role === \"company\" && auction.seller === ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.companyName)) {\n            return false;\n        }\n        return true;\n    };\n    const handleBid = async (auctionId)=>{\n        console.log(\"\\uD83C\\uDFAF Bid button clicked for auction:\", auctionId);\n        try {\n            var _currentAuction_currentBid;\n            const currentAuction = auctions.find((a)=>a.id === auctionId);\n            if (!currentAuction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Check if user can bid\n            if (!canUserBid(currentAuction)) {\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"المديرون لا يمكنهم المزايدة على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"government\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"الجهات الحكومية لا تزايد على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"company\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"لا يمكنك المزايدة على مزادك الخاص\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"غير مسموح لك بالمزايدة\",\n                        variant: \"destructive\"\n                    });\n                }\n                return;\n            }\n            // Show immediate feedback that button was clicked\n            toast({\n                title: \"\\uD83D\\uDC86 تم الضغط على زر المزايدة\",\n                description: \"جاري معالجة المزايدة...\"\n            });\n            // Prompt user for bid amount\n            const bidAmountStr = prompt(\"المزايدة الحالية: \".concat(((_currentAuction_currentBid = currentAuction.currentBid) === null || _currentAuction_currentBid === void 0 ? void 0 : _currentAuction_currentBid.toLocaleString()) || 0, \" ر.س\\n\\nأدخل مقدار مزايدتك:\"), String((currentAuction.currentBid || 0) + 1000));\n            if (!bidAmountStr) return; // User cancelled\n            const bidAmount = parseFloat(bidAmountStr);\n            if (isNaN(bidAmount) || bidAmount <= (currentAuction.currentBid || 0)) {\n                toast({\n                    title: \"مزايدة غير صالحة\",\n                    description: \"يجب أن تكون المزايدة أكبر من المزايدة الحالية\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setLoading(true);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_6__.auctionAPI.placeBid(auctionId, bidAmount);\n            // Update local state optimistically\n            setAuctions((prev)=>prev.map((auction)=>auction.id === auctionId ? {\n                        ...auction,\n                        currentBid: bidAmount,\n                        bidsCount: (auction.bidsCount || 0) + 1\n                    } : auction));\n            setFilteredAuctions((prev)=>prev.map((auction)=>auction.id === auctionId ? {\n                        ...auction,\n                        currentBid: bidAmount,\n                        bidsCount: (auction.bidsCount || 0) + 1\n                    } : auction));\n            toast({\n                title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                description: \"مقدار المزايدة: \".concat(bidAmount.toLocaleString(), \" ر.س\")\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error placing bid:\", error);\n            toast({\n                title: \"فشل في تقديم المزايدة\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n            // Reload data on error to sync with server\n            loadAuctions();\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSaveAuction = async (auctionId)=>{\n        console.log(\"❤️ Save button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر الحفظ\",\n            description: \"جاري معالجة الطلب...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            const isCurrentlySaved = savedAuctions.has(auctionId);\n            // Optimistic UI update\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (isCurrentlySaved) {\n                    newSet.delete(auctionId);\n                } else {\n                    newSet.add(auctionId);\n                }\n                return newSet;\n            });\n            if (isCurrentlySaved) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_6__.favoritesAPI.removeFavorite(\"auction\", auctionId);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(auction.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_6__.favoritesAPI.addFavorite({\n                    itemType: \"auction\",\n                    itemId: auctionId,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(auction.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving auction:\", error);\n            // Revert optimistic update on error\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (savedAuctions.has(auctionId)) {\n                    newSet.add(auctionId);\n                } else {\n                    newSet.delete(auctionId);\n                }\n                return newSet;\n            });\n            toast({\n                title: \"فشل في حفظ المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewAuction = async (auctionId)=>{\n        console.log(\"\\uD83D\\uDC41️ View button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر العرض\",\n            description: \"جاري تحميل التفاصيل...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // For now, show auction details in an alert - in real app this would navigate to detail page\n            const details = \"عنوان المزاد: \".concat(auction.title, \"\\n\") + \"الوصف: \".concat(auction.description || \"غير متوفر\", \"\\n\") + \"المزايدة الحالية: \".concat((auction.currentBid || 0).toLocaleString(), \" ر.س\\n\") + \"عدد المزايدات: \".concat(auction.bidsCount || 0, \"\\n\") + \"البائع: \".concat(auction.seller || \"غير معروف\", \"\\n\") + \"ينتهي: \".concat(auction.endDate || \"غير محدد\");\n            alert(details);\n            // Try to fetch fresh data from API\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_6__.auctionAPI.getById(auctionId);\n                console.log(\"Fresh auction data:\", response.data);\n                toast({\n                    title: \"\\uD83D\\uDC41️ تم عرض التفاصيل\",\n                    description: \"تم تحديث بيانات المزاد من الخادم\"\n                });\n            } catch (apiError) {\n                console.log(\"Could not fetch fresh data, showing cached data\");\n                toast({\n                    title: \"\\uD83D\\uDC41️ عرض التفاصيل\",\n                    description: \"يتم عرض البيانات المحفوظة محلياً\"\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error viewing auction:\", error);\n            toast({\n                title: \"فشل في عرض تفاصيل المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        allowedRoles: [\n            \"individual\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"المزادات المتاحة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"استكشف المزادات الحالية وشارك في المزايدة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    alert(\"\\uD83C\\uDF89 Test button works! Buttons are functional.\");\n                                    console.log(\"Test button clicked successfully!\");\n                                    toast({\n                                        title: \"✅ الأزرار تعمل بشكل طبيعي\",\n                                        description: \"يمكنك الآن استخدام أزرار المزادات\"\n                                    });\n                                },\n                                variant: \"outline\",\n                                children: \"\\uD83E\\uDDEA اختبار الأزرار\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    placeholder: \"ابحث في المزادات...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"pr-10\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 9\n                }, this),\n                Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg\",\n                                        children: \"إجمالي المزادات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: filteredAuctions.length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg\",\n                                        children: \"المزايدات النشطة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: filteredAuctions.reduce((sum, a)=>sum + (a.bidsCount || 0), 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg\",\n                                        children: \"أعلى مزايدة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"text-2xl font-bold text-purple-600\",\n                                        children: filteredAuctions.length > 0 ? Math.max(...filteredAuctions.map((a)=>a.currentBid || 0)).toLocaleString() + \" ر.س\" : \"0 ر.س\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    children: Array.isArray(filteredAuctions) && filteredAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"hover:shadow-lg transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-video bg-gray-200 rounded-lg mb-4 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"صورة المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-lg line-clamp-2\",\n                                            children: auction.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            className: \"line-clamp-2\",\n                                            children: auction.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"أعلى مزايدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-green-600\",\n                                                            children: [\n                                                                (auction.currentBid || 0).toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"المزايدات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold\",\n                                                            children: auction.bidsCount || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: [\n                                                        \"ينتهي خلال: \",\n                                                        getTimeRemaining(auction.endDate)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"البائع: \",\n                                                auction.seller\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        alert(\"Bid button clicked for auction: \" + auction.id);\n                                                        console.log(\"BID BUTTON CLICKED for auction:\", auction.id);\n                                                        handleBid(auction.id);\n                                                    },\n                                                    className: \"flex-1\",\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        loading ? \"جاري المزايدة...\" : \"مزايدة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: savedAuctions.has(auction.id) ? \"default\" : \"outline\",\n                                                    size: \"icon\",\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        alert(\"Save button clicked for auction: \" + auction.id);\n                                                        console.log(\"SAVE BUTTON CLICKED for auction:\", auction.id);\n                                                        handleSaveAuction(auction.id);\n                                                    },\n                                                    disabled: loading,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(savedAuctions.has(auction.id) ? \"fill-current\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"icon\",\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        alert(\"View button clicked for auction: \" + auction.id);\n                                                        console.log(\"VIEW BUTTON CLICKED for auction:\", auction.id);\n                                                        handleViewAuction(auction.id);\n                                                    },\n                                                    disabled: loading,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, auction.id, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 482,\n                    columnNumber: 9\n                }, this),\n                !loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"py-16 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: searchTerm ? \"لا توجد مزادات تطابق البحث\" : \"لا توجد مزادات متاحة حالياً\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: searchTerm ? \"جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات\" : \"سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 11\n                }, this),\n                loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"py-16 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"جاري تحميل المزادات...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 596,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 595,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 594,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n            lineNumber: 410,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n        lineNumber: 409,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAuctionsPage, \"6SomYB603PhCI9dVoioSOuqDBi4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = UserAuctionsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAuctionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/auctions/page.tsx\n"));

/***/ })

});