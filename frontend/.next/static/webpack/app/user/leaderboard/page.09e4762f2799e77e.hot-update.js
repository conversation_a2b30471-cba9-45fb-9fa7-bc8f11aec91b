"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/leaderboard/page",{

/***/ "(app-pages-browser)/./app/user/leaderboard/page.tsx":
/*!***************************************!*\
  !*** ./app/user/leaderboard/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeaderboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/medal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst getBadgeIcon = (badge)=>{\n    switch(badge){\n        case \"Champion\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 48,\n                columnNumber: 14\n            }, undefined);\n        case \"Master\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 50,\n                columnNumber: 14\n            }, undefined);\n        case \"Expert\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 52,\n                columnNumber: 14\n            }, undefined);\n        case \"Pro\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 54,\n                columnNumber: 14\n            }, undefined);\n        case \"Active\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 56,\n                columnNumber: 14\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 58,\n                columnNumber: 14\n            }, undefined);\n    }\n};\nconst getBadgeColor = (badge)=>{\n    switch(badge){\n        case \"Champion\":\n            return \"bg-yellow-500 text-white\";\n        case \"Master\":\n            return \"bg-purple-500 text-white\";\n        case \"Expert\":\n            return \"bg-blue-500 text-white\";\n        case \"Pro\":\n            return \"bg-green-500 text-white\";\n        case \"Active\":\n            return \"bg-orange-500 text-white\";\n        default:\n            return \"bg-gray-500 text-white\";\n    }\n};\nconst getRankIcon = (rank)=>{\n    if (rank === 1) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        className: \"h-6 w-6 text-yellow-500\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 80,\n        columnNumber: 26\n    }, undefined);\n    if (rank === 2) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        className: \"h-6 w-6 text-gray-400\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 81,\n        columnNumber: 26\n    }, undefined);\n    if (rank === 3) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        className: \"h-6 w-6 text-amber-600\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 82,\n        columnNumber: 26\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-lg font-bold text-gray-600\",\n        children: [\n            \"#\",\n            rank\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 83,\n        columnNumber: 10\n    }, undefined);\n};\nfunction LeaderboardPage() {\n    _s();\n    const [leaderboard, setLeaderboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all-time\");\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const fetchLeaderboard = async function() {\n        let period = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : selectedPeriod;\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/users/leaderboard?period=\".concat(period, \"&limit=50\"));\n            if (response.data.success) {\n                setLeaderboard(response.data.data.leaderboard);\n                setError(null);\n            } else {\n                throw new Error(\"فشل في تحميل لوحة الصدارة\");\n            }\n        } catch (err) {\n            console.error(\"Leaderboard error:\", err);\n            setError(err instanceof Error ? err.message : \"حدث خطأ غير متوقع\");\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات المتصدرين\",\n                variant: \"destructive\"\n            });\n            // Fallback to sample data\n            setLeaderboard(generateSampleLeaderboard());\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    const handleRefresh = ()=>{\n        setRefreshing(true);\n        fetchLeaderboard();\n    };\n    const handlePeriodChange = (period)=>{\n        setSelectedPeriod(period);\n        setLoading(true);\n        fetchLeaderboard(period);\n    };\n    const getPeriodLabel = (period)=>{\n        switch(period){\n            case \"daily\":\n                return \"اليوم\";\n            case \"weekly\":\n                return \"الأسبوع\";\n            case \"monthly\":\n                return \"الشهر\";\n            case \"all-time\":\n                return \"جميع الأوقات\";\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLeaderboard();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            allowedRoles: [\n                \"individual\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[400px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-primary/10 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-8 w-8 animate-spin text-primary\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"جاري تحميل لوحة الصدارة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"يرجى الانتظار بينما نحضر أحدث البيانات...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            allowedRoles: [\n                \"individual\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[400px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"max-w-md mx-auto bg-gradient-to-br from-red-50 to-orange-50 border-red-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-red-100 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-10 w-10 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-3 text-red-700\",\n                                    children: \"خطأ في تحميل البيانات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleRefresh,\n                                    className: \"w-full bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600\",\n                                    disabled: refreshing,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(refreshing ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"إعادة المحاولة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        allowedRoles: [\n            \"individual\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-8 w-8 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"لوحة الصدارة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 opacity-90\",\n                                            children: [\n                                                \"تصنيف أفضل المزايدين في المنصة - \",\n                                                getPeriodLabel(selectedPeriod)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleRefresh,\n                                    disabled: refreshing,\n                                    variant: \"secondary\",\n                                    className: \"bg-white/20 hover:bg-white/30 text-white border-white/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(refreshing ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5 text-white/80\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white/90 ml-3\",\n                                        children: \"الفترة الزمنية:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 space-x-reverse\",\n                                        children: [\n                                            \"daily\",\n                                            \"weekly\",\n                                            \"monthly\",\n                                            \"all-time\"\n                                        ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: selectedPeriod === period ? \"secondary\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handlePeriodChange(period),\n                                                disabled: loading,\n                                                className: selectedPeriod === period ? \"bg-white text-orange-600 hover:bg-white/90\" : \"bg-white/20 text-white border-white/30 hover:bg-white/30\",\n                                                children: getPeriodLabel(period)\n                                            }, period, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-blue-500 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-700\",\n                                                    children: leaderboard.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-600 text-sm\",\n                                                    children: \"مزايد نشط\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-green-500 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-700\",\n                                                    children: Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry)=>sum + entry.totalBids, 0) : 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-600 text-sm\",\n                                                    children: \"إجمالي المزايدات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-purple-500 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-purple-700\",\n                                                    children: Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry)=>sum + entry.points, 0) : 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-purple-600 text-sm\",\n                                                    children: \"إجمالي النقاط\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-orange-500 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-orange-700\",\n                                                    children: Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry)=>sum + entry.totalAmount, 0).toLocaleString(\"ar-SA\") : 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-orange-600 text-sm\",\n                                                    children: \"إجمالي المبالغ (ر.س)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this),\n                leaderboard.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"bg-gradient-to-br from-gray-50 to-gray-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-gray-200 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-10 w-10 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-3 text-gray-700\",\n                                    children: \"لا توجد بيانات حالياً\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6 max-w-md mx-auto\",\n                                    children: \"لم يتم العثور على أي مزايدات بعد. ابدأ بالمزايدة لتظهر في لوحة الصدارة!\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    className: \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"ابدأ المزايدة الآن\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        leaderboard.slice(0, 3).map((entry, index)=>{\n                            var _entry_user_profile;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"relative overflow-hidden \".concat(index === 0 ? \"bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-300 shadow-lg\" : index === 1 ? \"bg-gradient-to-r from-gray-50 to-slate-50 border-2 border-gray-300 shadow-md\" : \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-300 shadow-md\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 right-0 w-16 h-16 \".concat(index === 0 ? \"bg-gradient-to-br from-yellow-400 to-yellow-600\" : index === 1 ? \"bg-gradient-to-br from-gray-400 to-gray-600\" : \"bg-gradient-to-br from-amber-400 to-amber-600\", \" transform rotate-45 translate-x-6 -translate-y-6\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-2 left-2 text-white font-bold text-sm transform -rotate-45\",\n                                            children: [\n                                                \"#\",\n                                                entry.rank\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 w-16 h-16 flex items-center justify-center\",\n                                                            children: getRankIcon(entry.rank)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                    className: \"mr-4 w-12 h-12 border-2 border-white shadow-md\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                            src: entry.user.profile.avatarUrl,\n                                                                            alt: \"Avatar\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 361,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                            className: \"bg-gradient-to-br from-blue-400 to-purple-500 text-white\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-6 w-6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                lineNumber: 363,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-bold text-gray-900 truncate\",\n                                                                            children: ((_entry_user_profile = entry.user.profile) === null || _entry_user_profile === void 0 ? void 0 : _entry_user_profile.fullName) || entry.user.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 367,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-3 space-x-reverse mt-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                    className: \"\".concat(getBadgeColor(entry.badge), \" flex items-center px-3 py-1\"),\n                                                                                    children: [\n                                                                                        getBadgeIcon(entry.badge),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-1 font-medium\",\n                                                                                            children: entry.badge\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                            lineNumber: 373,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                    lineNumber: 371,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-gray-600 bg-white px-2 py-1 rounded-full\",\n                                                                                    children: [\n                                                                                        entry.points,\n                                                                                        \" نقطة\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                    lineNumber: 375,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 370,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-8 space-x-reverse text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/70 rounded-lg p-3 min-w-[80px]\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                                    children: entry.totalBids\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600 font-medium\",\n                                                                    children: \"مزايدة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/70 rounded-lg p-3 min-w-[100px]\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-green-600\",\n                                                                    children: entry.totalAmount.toLocaleString(\"ar-SA\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600 font-medium\",\n                                                                    children: \"ر.س\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/70 rounded-lg p-3 min-w-[80px]\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                                    children: entry.wonAuctions\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600 font-medium\",\n                                                                    children: \"فوز\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 397,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, entry._id, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, this);\n                        }),\n                        leaderboard.slice(3).map((entry, index)=>{\n                            var _entry_user_profile;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"hover:shadow-md transition-shadow duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 w-12 h-12 flex items-center justify-center bg-gray-100 rounded-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold text-gray-600\",\n                                                            children: [\n                                                                \"#\",\n                                                                entry.rank\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                className: \"mr-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                        src: entry.user.profile.avatarUrl,\n                                                                        alt: \"Avatar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                        className: \"bg-gradient-to-br from-gray-400 to-gray-500 text-white\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold text-gray-900 truncate\",\n                                                                        children: ((_entry_user_profile = entry.user.profile) === null || _entry_user_profile === void 0 ? void 0 : _entry_user_profile.fullName) || entry.user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2 space-x-reverse mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                className: \"\".concat(getBadgeColor(entry.badge), \" flex items-center\"),\n                                                                                children: [\n                                                                                    getBadgeIcon(entry.badge),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-1\",\n                                                                                        children: entry.badge\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                        lineNumber: 431,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    entry.points,\n                                                                                    \" نقطة\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-6 space-x-reverse text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-blue-600\",\n                                                                children: entry.totalBids\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"مزايدة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-green-600\",\n                                                                children: entry.totalAmount.toLocaleString(\"ar-SA\")\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"ر.س\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-purple-600\",\n                                                                children: entry.wonAuctions\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"فوز\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 17\n                                }, this)\n                            }, entry._id, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-500 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"كيف يتم حساب النقاط؟\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+10 نقاط لكل مزايدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+50 نقطة لكل فوز\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 text-purple-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"نقاط إضافية حسب القيمة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-4\",\n                                    children: \"يتم تحديث لوحة الصدارة كل ساعة. النقاط تُحسب بناءً على النشاط والأداء في المزايدات.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(LeaderboardPage, \"GYBBz581sotINOMDXbbv89FLbrc=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = LeaderboardPage;\nvar _c;\n$RefreshReg$(_c, \"LeaderboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/leaderboard/page.tsx\n"));

/***/ })

});