"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/leaderboard/page",{

/***/ "(app-pages-browser)/./app/user/leaderboard/page.tsx":
/*!***************************************!*\
  !*** ./app/user/leaderboard/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeaderboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/medal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst getBadgeIcon = (badge)=>{\n    switch(badge){\n        case \"Champion\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 48,\n                columnNumber: 14\n            }, undefined);\n        case \"Master\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 50,\n                columnNumber: 14\n            }, undefined);\n        case \"Expert\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 52,\n                columnNumber: 14\n            }, undefined);\n        case \"Pro\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 54,\n                columnNumber: 14\n            }, undefined);\n        case \"Active\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 56,\n                columnNumber: 14\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 58,\n                columnNumber: 14\n            }, undefined);\n    }\n};\nconst getBadgeColor = (badge)=>{\n    switch(badge){\n        case \"Champion\":\n            return \"bg-yellow-500 text-white\";\n        case \"Master\":\n            return \"bg-purple-500 text-white\";\n        case \"Expert\":\n            return \"bg-blue-500 text-white\";\n        case \"Pro\":\n            return \"bg-green-500 text-white\";\n        case \"Active\":\n            return \"bg-orange-500 text-white\";\n        default:\n            return \"bg-gray-500 text-white\";\n    }\n};\nconst getRankIcon = (rank)=>{\n    if (rank === 1) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        className: \"h-6 w-6 text-yellow-500\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 80,\n        columnNumber: 26\n    }, undefined);\n    if (rank === 2) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        className: \"h-6 w-6 text-gray-400\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 81,\n        columnNumber: 26\n    }, undefined);\n    if (rank === 3) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        className: \"h-6 w-6 text-amber-600\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 82,\n        columnNumber: 26\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-lg font-bold text-gray-600\",\n        children: [\n            \"#\",\n            rank\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 83,\n        columnNumber: 10\n    }, undefined);\n};\nfunction LeaderboardPage() {\n    _s();\n    const [leaderboard, setLeaderboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all-time\");\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const fetchLeaderboard = async function() {\n        let period = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : selectedPeriod;\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/users/leaderboard?period=\".concat(period, \"&limit=50\"));\n            if (response.data.success) {\n                setLeaderboard(response.data.data.leaderboard);\n                setError(null);\n            } else {\n                throw new Error(\"فشل في تحميل لوحة الصدارة\");\n            }\n        } catch (err) {\n            console.error(\"Leaderboard error:\", err);\n            setError(err instanceof Error ? err.message : \"حدث خطأ غير متوقع\");\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات المتصدرين\",\n                variant: \"destructive\"\n            });\n            setLeaderboard([]);\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    const handleRefresh = ()=>{\n        setRefreshing(true);\n        fetchLeaderboard();\n    };\n    const handlePeriodChange = (period)=>{\n        setSelectedPeriod(period);\n        setLoading(true);\n        fetchLeaderboard(period);\n    };\n    const getPeriodLabel = (period)=>{\n        switch(period){\n            case \"daily\":\n                return \"اليوم\";\n            case \"weekly\":\n                return \"الأسبوع\";\n            case \"monthly\":\n                return \"الشهر\";\n            case \"all-time\":\n                return \"جميع الأوقات\";\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLeaderboard();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            allowedRoles: [\n                \"individual\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[400px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-primary/10 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-8 w-8 animate-spin text-primary\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"جاري تحميل لوحة الصدارة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"يرجى الانتظار بينما نحضر أحدث البيانات...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            allowedRoles: [\n                \"individual\"\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-[400px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"max-w-md mx-auto bg-gradient-to-br from-red-50 to-orange-50 border-red-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-red-100 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-10 w-10 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-3 text-red-700\",\n                                    children: \"خطأ في تحميل البيانات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleRefresh,\n                                    className: \"w-full bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600\",\n                                    disabled: refreshing,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(refreshing ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"إعادة المحاولة\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 165,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n            lineNumber: 164,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        allowedRoles: [\n            \"individual\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-8 w-8 mr-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"لوحة الصدارة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 opacity-90\",\n                                            children: [\n                                                \"تصنيف أفضل المزايدين في المنصة - \",\n                                                getPeriodLabel(selectedPeriod)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleRefresh,\n                                    disabled: refreshing,\n                                    variant: \"secondary\",\n                                    className: \"bg-white/20 hover:bg-white/30 text-white border-white/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(refreshing ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5 text-white/80\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white/90 ml-3\",\n                                        children: \"الفترة الزمنية:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 space-x-reverse\",\n                                        children: [\n                                            \"daily\",\n                                            \"weekly\",\n                                            \"monthly\",\n                                            \"all-time\"\n                                        ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: selectedPeriod === period ? \"secondary\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handlePeriodChange(period),\n                                                disabled: loading,\n                                                className: selectedPeriod === period ? \"bg-white text-orange-600 hover:bg-white/90\" : \"bg-white/20 text-white border-white/30 hover:bg-white/30\",\n                                                children: getPeriodLabel(period)\n                                            }, period, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-blue-500 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-700\",\n                                                    children: leaderboard.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-600 text-sm\",\n                                                    children: \"مزايد نشط\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-green-500 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-700\",\n                                                    children: Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry)=>sum + entry.totalBids, 0) : 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-600 text-sm\",\n                                                    children: \"إجمالي المزايدات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-purple-500 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-purple-700\",\n                                                    children: Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry)=>sum + entry.points, 0) : 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-purple-600 text-sm\",\n                                                    children: \"إجمالي النقاط\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-orange-500 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-orange-700\",\n                                                    children: Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry)=>sum + entry.totalAmount, 0).toLocaleString(\"ar-SA\") : 0\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-orange-600 text-sm\",\n                                                    children: \"إجمالي المبالغ (ر.س)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this),\n                leaderboard.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"bg-gradient-to-br from-gray-50 to-gray-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-gray-200 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-10 w-10 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-3 text-gray-700\",\n                                    children: \"لا توجد بيانات حالياً\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6 max-w-md mx-auto\",\n                                    children: \"لم يتم العثور على أي مزايدات بعد. ابدأ بالمزايدة لتظهر في لوحة الصدارة!\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    className: \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"ابدأ المزايدة الآن\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        leaderboard.slice(0, 3).map((entry, index)=>{\n                            var _entry_user_profile;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"relative overflow-hidden \".concat(index === 0 ? \"bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-300 shadow-lg\" : index === 1 ? \"bg-gradient-to-r from-gray-50 to-slate-50 border-2 border-gray-300 shadow-md\" : \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-300 shadow-md\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 right-0 w-16 h-16 \".concat(index === 0 ? \"bg-gradient-to-br from-yellow-400 to-yellow-600\" : index === 1 ? \"bg-gradient-to-br from-gray-400 to-gray-600\" : \"bg-gradient-to-br from-amber-400 to-amber-600\", \" transform rotate-45 translate-x-6 -translate-y-6\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-2 left-2 text-white font-bold text-sm transform -rotate-45\",\n                                            children: [\n                                                \"#\",\n                                                entry.rank\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 w-16 h-16 flex items-center justify-center\",\n                                                            children: getRankIcon(entry.rank)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                    className: \"mr-4 w-12 h-12 border-2 border-white shadow-md\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                            src: entry.user.profile.avatarUrl,\n                                                                            alt: \"Avatar\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                            className: \"bg-gradient-to-br from-blue-400 to-purple-500 text-white\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"h-6 w-6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 361,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-bold text-gray-900 truncate\",\n                                                                            children: ((_entry_user_profile = entry.user.profile) === null || _entry_user_profile === void 0 ? void 0 : _entry_user_profile.fullName) || entry.user.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 366,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-3 space-x-reverse mt-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                    className: \"\".concat(getBadgeColor(entry.badge), \" flex items-center px-3 py-1\"),\n                                                                                    children: [\n                                                                                        getBadgeIcon(entry.badge),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"mr-1 font-medium\",\n                                                                                            children: entry.badge\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                            lineNumber: 372,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                    lineNumber: 370,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-gray-600 bg-white px-2 py-1 rounded-full\",\n                                                                                    children: [\n                                                                                        entry.points,\n                                                                                        \" نقطة\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                    lineNumber: 374,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-8 space-x-reverse text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/70 rounded-lg p-3 min-w-[80px]\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                                    children: entry.totalBids\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600 font-medium\",\n                                                                    children: \"مزايدة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/70 rounded-lg p-3 min-w-[100px]\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-green-600\",\n                                                                    children: entry.totalAmount.toLocaleString(\"ar-SA\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600 font-medium\",\n                                                                    children: \"ر.س\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/70 rounded-lg p-3 min-w-[80px]\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                                    children: entry.wonAuctions\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600 font-medium\",\n                                                                    children: \"فوز\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, entry._id, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, this);\n                        }),\n                        leaderboard.slice(3).map((entry, index)=>{\n                            var _entry_user_profile;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"hover:shadow-md transition-shadow duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 w-12 h-12 flex items-center justify-center bg-gray-100 rounded-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold text-gray-600\",\n                                                            children: [\n                                                                \"#\",\n                                                                entry.rank\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                className: \"mr-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                        src: entry.user.profile.avatarUrl,\n                                                                        alt: \"Avatar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                        className: \"bg-gradient-to-br from-gray-400 to-gray-500 text-white\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 420,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold text-gray-900 truncate\",\n                                                                        children: ((_entry_user_profile = entry.user.profile) === null || _entry_user_profile === void 0 ? void 0 : _entry_user_profile.fullName) || entry.user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 424,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2 space-x-reverse mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                className: \"\".concat(getBadgeColor(entry.badge), \" flex items-center\"),\n                                                                                children: [\n                                                                                    getBadgeIcon(entry.badge),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-1\",\n                                                                                        children: entry.badge\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                        lineNumber: 430,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    entry.points,\n                                                                                    \" نقطة\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-6 space-x-reverse text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-blue-600\",\n                                                                children: entry.totalBids\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"مزايدة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-green-600\",\n                                                                children: entry.totalAmount.toLocaleString(\"ar-SA\")\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"ر.س\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl font-bold text-purple-600\",\n                                                                children: entry.wonAuctions\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600\",\n                                                                children: \"فوز\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 17\n                                }, this)\n                            }, entry._id, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-500 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold text-gray-700\",\n                                            children: \"كيف يتم حساب النقاط؟\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+10 نقاط لكل مزايدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+50 نقطة لكل فوز\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 text-purple-500 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"نقاط إضافية حسب القيمة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-4\",\n                                    children: \"يتم تحديث لوحة الصدارة كل ساعة. النقاط تُحسب بناءً على النشاط والأداء في المزايدات.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n            lineNumber: 192,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(LeaderboardPage, \"GYBBz581sotINOMDXbbv89FLbrc=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = LeaderboardPage;\nvar _c;\n$RefreshReg$(_c, \"LeaderboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/leaderboard/page.tsx\n"));

/***/ })

});