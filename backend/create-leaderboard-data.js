const mongoose = require('mongoose');
const User = require('./models/User');
const Auction = require('./models/Auction');
require('dotenv').config();

async function createLeaderboardData() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/auction_platform');
    console.log('Connected to MongoDB');

    // Create some sample users for leaderboard
    const sampleUsers = [
      {
        email: '<EMAIL>',
        password: 'password123',
        role: 'individual',
        status: 'approved',
        emailVerified: true,
        profile: {
          fullName: 'أحمد علي السعيد',
          phone: '+966501234567',
          nationalId: '1234567890',
          address: 'الرياض، المملكة العربية السعودية'
        }
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        role: 'individual',
        status: 'approved',
        emailVerified: true,
        profile: {
          fullName: 'سارة محمد الأحمد',
          phone: '+966502345678',
          nationalId: '2345678901',
          address: 'جدة، المملكة العربية السعودية'
        }
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        role: 'individual',
        status: 'approved',
        emailVerified: true,
        profile: {
          fullName: 'عمر حسن المطيري',
          phone: '+966503456789',
          nationalId: '3456789012',
          address: 'الدمام، المملكة العربية السعودية'
        }
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        role: 'individual',
        status: 'approved',
        emailVerified: true,
        profile: {
          fullName: 'فاطمة عبدالله القحطاني',
          phone: '+966504567890',
          nationalId: '4567890123',
          address: 'مكة المكرمة، المملكة العربية السعودية'
        }
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        role: 'individual',
        status: 'approved',
        emailVerified: true,
        profile: {
          fullName: 'خالد إبراهيم الزهراني',
          phone: '+966505678901',
          nationalId: '5678901234',
          address: 'المدينة المنورة، المملكة العربية السعودية'
        }
      }
    ];

    // Create users if they don't exist
    for (const userData of sampleUsers) {
      const existingUser = await User.findOne({ email: userData.email });
      if (!existingUser) {
        const user = new User(userData);
        await user.save();
        console.log(`Created user: ${userData.profile.fullName}`);
      } else {
        console.log(`User already exists: ${userData.profile.fullName}`);
      }
    }

    // Note: Auctions require complex relationships and validation
    // For now, we'll just create users and let the leaderboard API handle mock data
    console.log('Users created successfully. Leaderboard will use mock data for demonstration.');

    console.log('✅ Leaderboard sample data created successfully!');
    process.exit(0);

  } catch (error) {
    console.error('❌ Error creating leaderboard data:', error);
    process.exit(1);
  }
}

createLeaderboardData();
