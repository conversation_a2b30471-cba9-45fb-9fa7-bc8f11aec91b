const mongoose = require('mongoose');
const User = require('./models/User');
require('dotenv').config();

async function checkUserStatus() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/auction_platform');
    console.log('Connected to MongoDB');
    
    const user = await User.findOne({ email: '<EMAIL>' });
    if (!user) {
      console.log('User not found');
      process.exit(1);
    }
    
    console.log('User email:', user.email);
    console.log('User status:', user.status);
    console.log('Email verified:', user.emailVerified);
    console.log('User role:', user.role);
    console.log('Submitted at:', user.submittedAt);
    console.log('Reviewed at:', user.reviewedAt);
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkUserStatus();
