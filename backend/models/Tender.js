const mongoose = require('mongoose');

const proposalSchema = new mongoose.Schema({
  proposer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  proposal: {
    type: String,
    required: true,
    maxlength: 2000
  },
  documents: [{
    name: String,
    type: String,
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    },
    verified: {
      type: Boolean,
      default: false
    }
  }],
  submittedAt: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'rejected'],
    default: 'pending'
  },
  scoring: {
    technicalScore: {
      type: Number,
      min: 0,
      max: 100
    },
    financialScore: {
      type: Number,
      min: 0,
      max: 100
    },
    experienceScore: {
      type: Number,
      min: 0,
      max: 100
    },
    overallScore: {
      type: Number,
      min: 0,
      max: 100
    },
    comments: {
      type: String,
      maxlength: 1000
    },
    recommendation: {
      type: String,
      enum: ['recommended', 'conditional', 'not_recommended']
    },
    scoredBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    scoredAt: {
      type: Date
    }
  }
});

const tenderSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    maxlength: 2000
  },
  category: {
    type: String,
    required: true,
    enum: [
      'construction',
      'it_technology',
      'consulting',
      'healthcare',
      'education',
      'transportation',
      'real_estate',
      'manufacturing',
      'energy',
      'other'
    ]
  },
  organizer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  budget: {
    type: Number,
    required: true,
    min: 1
  },
  startDate: {
    type: Date,
    required: true
  },
  deadline: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['open', 'closed', 'awarded'],
    default: 'open'
  },
  location: {
    city: String,
    region: String,
    country: {
      type: String,
      default: 'Saudi Arabia'
    }
  },
  requirements: [String],
  proposals: [proposalSchema],
  awardedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  awardedProposal: {
    type: mongoose.Schema.Types.ObjectId
  },
  rejectionReason: String,
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: Date
}, {
  timestamps: true
});

// Indexes
tenderSchema.index({ organizer: 1 });
tenderSchema.index({ status: 1 });
tenderSchema.index({ category: 1 });
tenderSchema.index({ deadline: 1 });
tenderSchema.index({ startDate: 1 });
tenderSchema.index({ 'location.city': 1 });
tenderSchema.index({ title: 'text', description: 'text' });

// Method to submit a proposal
tenderSchema.methods.submitProposal = function(proposerId, proposalAmount, proposalText, documents = []) {
  // Validation
  if (this.status !== 'open') {
    throw new Error('Tender is not open for proposals');
  }
  
  if (new Date() > this.deadline) {
    throw new Error('Tender deadline has passed');
  }
  
  if (this.organizer.toString() === proposerId.toString()) {
    throw new Error('Organizer cannot submit a proposal to their own tender');
  }

  // Add new proposal
  const newProposal = {
    proposer: proposerId,
    amount: proposalAmount,
    proposal: proposalText,
    documents: documents
  };
  
  this.proposals.push(newProposal);
  return newProposal;
};

// Method to select winning proposal
tenderSchema.methods.awardTender = function(proposalId) {
  const proposal = this.proposals.id(proposalId);
  if (!proposal) {
    throw new Error('Proposal not found');
  }

  // Update tender status and award info
  this.status = 'awarded';
  this.awardedTo = proposal.proposer;
  this.awardedProposal = proposal._id;

  // Update proposal statuses
  this.proposals.forEach(p => {
    if (p._id.toString() === proposalId.toString()) {
      p.status = 'accepted';
    } else {
      p.status = 'rejected';
    }
  });

  return this;
};

// Static method to get open tenders
tenderSchema.statics.getOpenTenders = function() {
  return this.find({
    status: 'open',
    startDate: { $lte: new Date() },
    deadline: { $gt: new Date() }
  });
};

module.exports = mongoose.model('Tender', tenderSchema);
