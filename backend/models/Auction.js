const mongoose = require('mongoose');

const bidSchema = new mongoose.Schema({
  bidder: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  isWinning: {
    type: Boolean,
    default: false
  },
  isAutomatic: {
    type: Boolean,
    default: false
  }
});

const auctionSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    maxlength: 2000
  },
  category: {
    type: String,
    required: true,
    enum: [
      'electronics',
      'vehicles',
      'real_estate',
      'art_collectibles',
      'machinery',
      'furniture',
      'jewelry',
      'books_media',
      'clothing',
      'sports',
      'other'
    ]
  },
  seller: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  auctioneer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  startingPrice: {
    type: Number,
    required: true,
    min: 1
  },
  currentPrice: {
    type: Number,
    required: true
  },
  reservePrice: {
    type: Number,
    default: 0
  },
  buyNowPrice: {
    type: Number,
    default: null
  },
  bidIncrement: {
    type: Number,
    default: 10,
    min: 1
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['draft', 'pending_approval', 'active', 'ended', 'cancelled', 'sold'],
    default: 'draft'
  },
  auctionType: {
    type: String,
    enum: ['standard', 'reserve', 'buy_now', 'dutch'],
    default: 'standard'
  },
  images: [{
    url: String,
    caption: String,
    isMain: {
      type: Boolean,
      default: false
    }
  }],
  condition: {
    type: String,
    enum: ['new', 'like_new', 'excellent', 'good', 'fair', 'poor'],
    required: true
  },
  location: {
    city: String,
    region: String,
    country: {
      type: String,
      default: 'Saudi Arabia'
    }
  },
  shipping: {
    available: {
      type: Boolean,
      default: false
    },
    cost: {
      type: Number,
      default: 0
    },
    methods: [String]
  },
  bids: [bidSchema],
  watchers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  autoExtend: {
    enabled: {
      type: Boolean,
      default: true
    },
    timeMinutes: {
      type: Number,
      default: 5
    },
    maxExtensions: {
      type: Number,
      default: 3
    },
    extensionCount: {
      type: Number,
      default: 0
    },
    lastExtensionTime: Date
  },
  winner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  winningBid: {
    type: mongoose.Schema.Types.ObjectId
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'refunded'],
    default: 'pending'
  },
  deliveryStatus: {
    type: String,
    enum: ['pending', 'shipped', 'delivered', 'pickup_ready'],
    default: 'pending'
  },
  fees: {
    sellerFee: {
      type: Number,
      default: 0
    },
    buyerFee: {
      type: Number,
      default: 0
    }
  },
  featured: {
    type: Boolean,
    default: false
  },
  views: {
    type: Number,
    default: 0
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: Date,
  rejectionReason: String
}, {
  timestamps: true
});

// Indexes
auctionSchema.index({ seller: 1 });
auctionSchema.index({ status: 1 });
auctionSchema.index({ category: 1 });
auctionSchema.index({ endDate: 1 });
auctionSchema.index({ startDate: 1 });
auctionSchema.index({ featured: 1 });
auctionSchema.index({ 'location.city': 1 });
auctionSchema.index({ title: 'text', description: 'text' });

// Virtual for time remaining
auctionSchema.virtual('timeRemaining').get(function() {
  if (this.status !== 'active') return 0;
  const now = new Date();
  const remaining = this.endDate.getTime() - now.getTime();
  return Math.max(0, remaining);
});

// Virtual for total bids
auctionSchema.virtual('totalBids').get(function() {
  return this.bids.length;
});

// Virtual for leading bidder
auctionSchema.virtual('leadingBidder').get(function() {
  if (this.bids.length === 0) return null;
  const winningBid = this.bids.find(bid => bid.isWinning);
  return winningBid ? winningBid.bidder : null;
});

// Method to place a bid
auctionSchema.methods.placeBid = function(bidderId, amount, isAutomatic = false) {
  // Validation
  if (this.status !== 'active') {
    throw new Error('Auction is not active');
  }

  const now = new Date();

  if (now < this.startDate) {
    throw new Error('Auction has not started yet');
  }

  if (now > this.endDate) {
    throw new Error('Auction has ended');
  }
  
  if (amount <= this.currentPrice) {
    throw new Error(`Bid must be higher than current price`);
  }
  
  if (amount < this.currentPrice + this.bidIncrement) {
    throw new Error(`Bid must be at least ${this.currentPrice + this.bidIncrement}`);
  }
  
  if (this.seller.toString() === bidderId.toString()) {
    throw new Error('Seller cannot bid on their own auction');
  }
  
  // Mark all previous bids as not winning
  this.bids.forEach(bid => {
    bid.isWinning = false;
  });
  
// Add new bid
const newBid = {
  bidder: bidderId,
  amount: amount,
  isWinning: true,
  isAutomatic: isAutomatic
};

this.bids.push(newBid);
this.currentPrice = amount;

// Auto-extend if enabled and bid placed in last few minutes
if (this.autoExtend.enabled) {
  const timeRemaining = this.endDate.getTime() - Date.now();
  const extendThreshold = this.autoExtend.timeMinutes * 60 * 1000;
  
  if (timeRemaining < extendThreshold && this.autoExtend.extensionCount < this.autoExtend.maxExtensions) {
    this.endDate = new Date(Date.now() + extendThreshold);
    this.autoExtend.extensionCount += 1;
    this.autoExtend.lastExtensionTime = new Date();
  }
}

return newBid;
};

// Method to end auction
auctionSchema.methods.endAuction = function() {
  if (this.status !== 'active') {
    throw new Error('Auction is not active');
  }
  
  this.status = 'ended';
  
  const winningBid = this.bids.find(bid => bid.isWinning);
  if (winningBid && winningBid.amount >= this.reservePrice) {
    this.winner = winningBid.bidder;
    this.winningBid = winningBid._id;
    this.status = 'sold';
  }
  
  return this;
};

// Static method to get active auctions
auctionSchema.statics.getActive = function() {
  return this.find({
    status: 'active',
    startDate: { $lte: new Date() },
    endDate: { $gt: new Date() }
  });
};

// Static method to get ending soon auctions
auctionSchema.statics.getEndingSoon = function(hours = 1) {
  const cutoff = new Date(Date.now() + (hours * 60 * 60 * 1000));
  return this.find({
    status: 'active',
    endDate: { $lte: cutoff, $gt: new Date() }
  });
};

module.exports = mongoose.model('Auction', auctionSchema);
