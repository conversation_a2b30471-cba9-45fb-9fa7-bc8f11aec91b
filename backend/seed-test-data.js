require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('./models/User');
const Auction = require('./models/Auction');
const Tender = require('./models/Tender');

/**
 * Comprehensive Test Data Seeder
 * Creates realistic test data for all user types and scenarios
 */

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/auction_platform');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const clearExistingData = async () => {
  console.log('🧹 Clearing existing test data...');
  await User.deleteMany({});
  await Auction.deleteMany({});
  await Tender.deleteMany({});
  console.log('✅ Existing data cleared');
};

const createTestUsers = async () => {
  console.log('👥 Creating test users...');
  
  const hashedPassword = await bcrypt.hash('password123', 10);
  
  const users = [
    // Admin Users
    {
      name: 'مدير النظام',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      status: 'approved',
      phone: '+966501234567',
      nationalId: '1234567890'
    },
    {
      name: 'مدير عام',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'super_admin',
      status: 'approved',
      phone: '+966501234568',
      nationalId: '1234567891'
    },
    
    // Individual Users
    {
      name: 'أحمد محمد علي',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'individual',
      status: 'approved',
      phone: '+966501234569',
      nationalId: '1234567892',
      dateOfBirth: new Date('1990-01-15'),
      address: 'الرياض، المملكة العربية السعودية'
    },
    {
      name: 'فاطمة أحمد السالم',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'individual',
      status: 'approved',
      phone: '+966501234570',
      nationalId: '1234567893',
      dateOfBirth: new Date('1985-05-20'),
      address: 'جدة، المملكة العربية السعودية'
    },
    {
      name: 'محمد عبدالله الخالد',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'individual',
      status: 'pending',
      phone: '+966501234571',
      nationalId: '1234567894',
      dateOfBirth: new Date('1992-08-10'),
      address: 'الدمام، المملكة العربية السعودية'
    },
    
    // Company Users
    {
      name: 'شركة التقنية المتقدمة',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'company',
      status: 'approved',
      phone: '+966112345678',
      commercialRegister: 'CR1234567890',
      companyType: 'technology',
      establishedYear: 2015,
      address: 'الرياض، المملكة العربية السعودية',
      website: 'https://techcompany.com'
    },
    {
      name: 'مؤسسة البناء والتشييد',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'company',
      status: 'approved',
      phone: '+966112345679',
      commercialRegister: 'CR1234567891',
      companyType: 'construction',
      establishedYear: 2010,
      address: 'جدة، المملكة العربية السعودية',
      website: 'https://construction.com'
    },
    {
      name: 'شركة الخدمات اللوجستية',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'company',
      status: 'pending',
      phone: '+966112345680',
      commercialRegister: 'CR1234567892',
      companyType: 'logistics',
      establishedYear: 2018,
      address: 'الدمام، المملكة العربية السعودية'
    },
    
    // Government Users
    {
      name: 'وزارة التجارة والاستثمار',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'government',
      status: 'approved',
      phone: '+966114567890',
      department: 'وزارة التجارة والاستثمار',
      position: 'مدير المشتريات',
      governmentId: 'GOV001',
      address: 'الرياض، المملكة العربية السعودية'
    },
    {
      name: 'وزارة الصحة',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'government',
      status: 'approved',
      phone: '+966114567891',
      department: 'وزارة الصحة',
      position: 'مدير المشتريات الطبية',
      governmentId: 'GOV002',
      address: 'الرياض، المملكة العربية السعودية'
    }
  ];
  
  const createdUsers = await User.insertMany(users);
  console.log(`✅ Created ${createdUsers.length} test users`);
  return createdUsers;
};

const createTestAuctions = async (users) => {
  console.log('🏷️ Creating test auctions...');
  
  const companies = users.filter(user => user.role === 'company' && user.status === 'approved');
  
  const auctions = [
    {
      title: 'جهاز كمبيوتر محمول عالي الأداء',
      description: 'جهاز كمبيوتر محمول للألعاب والتصميم مع مواصفات عالية',
      startPrice: 5000,
      currentPrice: 5000,
      category: 'electronics',
      condition: 'new',
      seller: companies[0]._id,
      startTime: new Date(),
      endTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      status: 'active',
      images: ['https://example.com/laptop1.jpg'],
      specifications: {
        brand: 'Dell',
        model: 'XPS 15',
        processor: 'Intel i7',
        ram: '16GB',
        storage: '512GB SSD'
      }
    },
    {
      title: 'سيارة مرسيدس C200 موديل 2020',
      description: 'سيارة مرسيدس C200 في حالة ممتازة، قليلة الاستخدام',
      startPrice: 150000,
      currentPrice: 165000,
      category: 'vehicles',
      condition: 'used',
      seller: companies[1]._id,
      startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // Started 2 days ago
      endTime: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
      status: 'active',
      images: ['https://example.com/mercedes1.jpg'],
      specifications: {
        brand: 'Mercedes',
        model: 'C200',
        year: 2020,
        mileage: '25000 km',
        color: 'أسود'
      },
      bids: [
        {
          bidder: users.find(u => u.email === '<EMAIL>')._id,
          amount: 160000,
          timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
        },
        {
          bidder: users.find(u => u.email === '<EMAIL>')._id,
          amount: 165000,
          timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000)
        }
      ]
    }
  ];
  
  const createdAuctions = await Auction.insertMany(auctions);
  console.log(`✅ Created ${createdAuctions.length} test auctions`);
  return createdAuctions;
};

const createTestTenders = async (users) => {
  console.log('📋 Creating test tenders...');

  const governments = users.filter(user => user.role === 'government' && user.status === 'approved');
  const companies = users.filter(user => user.role === 'company' && user.status === 'approved');

  const tenders = [
    {
      title: 'توريد أجهزة كمبيوتر للمدارس الحكومية',
      description: 'مناقصة لتوريد 500 جهاز كمبيوتر للمدارس الحكومية في منطقة الرياض',
      category: 'technology',
      budget: 2500000,
      department: 'وزارة التعليم',
      location: 'الرياض',
      submissionDeadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      openingDate: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000), // 35 days from now
      status: 'open',
      organizer: governments[0]._id,
      requirements: [
        'شهادة ISO 9001',
        'خبرة لا تقل عن 5 سنوات',
        'ضمان لمدة 3 سنوات'
      ],
      documents: ['tender_specs.pdf', 'terms_conditions.pdf'],
      applications: [
        {
          applicant: companies[0]._id,
          submissionDate: new Date(),
          proposedPrice: 2400000,
          documents: ['company_profile.pdf', 'technical_proposal.pdf'],
          status: 'submitted'
        }
      ]
    },
    {
      title: 'مشروع إنشاء مستشفى عام',
      description: 'مناقصة لإنشاء مستشفى عام بسعة 200 سرير في مدينة جدة',
      category: 'construction',
      budget: 50000000,
      department: 'وزارة الصحة',
      location: 'جدة',
      submissionDeadline: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
      openingDate: new Date(Date.now() + 50 * 24 * 60 * 60 * 1000), // 50 days from now
      status: 'open',
      organizer: governments[1]._id,
      requirements: [
        'ترخيص المقاولات من الدرجة الأولى',
        'خبرة في مشاريع المستشفيات',
        'شهادة الجودة والسلامة'
      ],
      documents: ['hospital_plans.pdf', 'specifications.pdf']
    }
  ];

  const createdTenders = await Tender.insertMany(tenders);
  console.log(`✅ Created ${createdTenders.length} test tenders`);
  return createdTenders;
};

const seedTestData = async () => {
  try {
    console.log('🌱 Starting test data seeding...\n');

    await connectDB();
    await clearExistingData();

    const users = await createTestUsers();
    const auctions = await createTestAuctions(users);
    const tenders = await createTestTenders(users);

    console.log('\n🎉 Test data seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   👥 Users: ${users.length}`);
    console.log(`   🏷️ Auctions: ${auctions.length}`);
    console.log(`   📋 Tenders: ${tenders.length}`);

    console.log('\n🔑 Test Accounts:');
    console.log('   Admin: <EMAIL> / password123');
    console.log('   Individual: <EMAIL> / password123');
    console.log('   Company: <EMAIL> / password123');
    console.log('   Government: <EMAIL> / password123');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding test data:', error);
    process.exit(1);
  }
};

// Run the seeder
if (require.main === module) {
  seedTestData();
}

module.exports = { seedTestData };
