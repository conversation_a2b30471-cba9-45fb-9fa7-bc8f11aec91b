require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('./models/User');
const Auction = require('./models/Auction');
const Tender = require('./models/Tender');

/**
 * Comprehensive Test Data Seeder
 * Creates realistic test data for all user types and scenarios
 */

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/auction_platform');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const clearExistingData = async () => {
  console.log('🧹 Clearing existing test data...');
  await User.deleteMany({});
  await Auction.deleteMany({});
  await Tender.deleteMany({});
  console.log('✅ Existing data cleared');
};

const createTestUsers = async () => {
  console.log('👥 Creating comprehensive test users...');

  const hashedPassword = await bcrypt.hash('password123', 10);

  const users = [
    // ========== ADMIN USERS ==========
    {
      name: 'مدير النظام الرئيسي',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      status: 'approved',
      phone: '+966501234567',
      nationalId: '1234567890',
      profileImage: 'https://via.placeholder.com/150/0066cc/ffffff?text=Admin',
      lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
    },
    {
      name: 'المدير العام للمنصة',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'super_admin',
      status: 'approved',
      phone: '+966501234568',
      nationalId: '1234567891',
      profileImage: 'https://via.placeholder.com/150/cc0066/ffffff?text=Super',
      lastLogin: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000) // 60 days ago
    },
    {
      name: 'مدير الدعم الفني',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      status: 'approved',
      phone: '+966501234569',
      nationalId: '1234567892',
      department: 'الدعم الفني',
      position: 'مدير الدعم',
      lastLogin: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000) // 45 days ago
    },

    // ========== INDIVIDUAL USERS ==========
    {
      name: 'أحمد محمد علي الشهري',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'individual',
      status: 'approved',
      phone: '+966501234570',
      nationalId: '1234567893',
      dateOfBirth: new Date('1990-01-15'),
      address: 'حي الملك فهد، الرياض 12345، المملكة العربية السعودية',
      profileImage: 'https://via.placeholder.com/150/009900/ffffff?text=أحمد',
      occupation: 'مهندس برمجيات',
      education: 'بكالوريوس علوم الحاسب',
      lastLogin: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
      createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
      emailVerified: true,
      phoneVerified: true
    },
    {
      name: 'فاطمة أحمد السالم القحطاني',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'individual',
      status: 'approved',
      phone: '+966501234571',
      nationalId: '1234567894',
      dateOfBirth: new Date('1985-05-20'),
      address: 'حي الروضة، جدة 21442، المملكة العربية السعودية',
      profileImage: 'https://via.placeholder.com/150/cc6600/ffffff?text=فاطمة',
      occupation: 'طبيبة أسنان',
      education: 'دكتوراه في طب الأسنان',
      lastLogin: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
      createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000), // 20 days ago
      emailVerified: true,
      phoneVerified: true
    },
    {
      name: 'محمد عبدالله الخالد العتيبي',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'individual',
      status: 'documents_submitted',
      phone: '+966501234572',
      nationalId: '1234567895',
      dateOfBirth: new Date('1992-08-10'),
      address: 'حي الفيصلية، الدمام 31441، المملكة العربية السعودية',
      profileImage: 'https://via.placeholder.com/150/666666/ffffff?text=محمد',
      occupation: 'محاسب',
      education: 'بكالوريوس المحاسبة',
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      emailVerified: true,
      phoneVerified: false
    },
    {
      name: 'سارة عبدالرحمن المطيري',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'individual',
      status: 'approved',
      phone: '+966501234573',
      nationalId: '1234567896',
      dateOfBirth: new Date('1988-12-03'),
      address: 'حي النزهة، الرياض 11564، المملكة العربية السعودية',
      profileImage: 'https://via.placeholder.com/150/9900cc/ffffff?text=سارة',
      occupation: 'مصممة جرافيك',
      education: 'بكالوريوس التصميم الجرافيكي',
      lastLogin: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
      emailVerified: true,
      phoneVerified: true
    },
    {
      name: 'خالد سعد الغامدي',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'individual',
      status: 'blocked',
      phone: '+966501234574',
      nationalId: '1234567897',
      dateOfBirth: new Date('1995-03-22'),
      address: 'حي الصفا، جدة 21524، المملكة العربية السعودية',
      occupation: 'طالب جامعي',
      education: 'بكالوريوس إدارة الأعمال (طالب)',
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      emailVerified: false,
      phoneVerified: false,
      blockReason: 'مخالفة شروط الاستخدام'
    },

    // ========== COMPANY USERS ==========
    {
      name: 'شركة التقنية المتقدمة للحلول الذكية',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'company',
      status: 'approved',
      phone: '+966112345678',
      commercialRegister: 'CR1234567890',
      companyType: 'technology',
      establishedYear: 2015,
      address: 'مجمع الملك عبدالعزيز للتقنية، الرياض 11442، المملكة العربية السعودية',
      website: 'https://techcompany.sa',
      profileImage: 'https://via.placeholder.com/150/0066ff/ffffff?text=Tech',
      description: 'شركة رائدة في مجال تطوير الحلول التقنية والذكية للقطاعين الحكومي والخاص',
      employeeCount: 150,
      annualRevenue: 25000000,
      certifications: ['ISO 9001:2015', 'ISO 27001:2013', 'CITC License'],
      lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000), // 25 days ago
      emailVerified: true,
      phoneVerified: true
    },
    {
      name: 'مؤسسة البناء والتشييد الحديث',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'company',
      status: 'approved',
      phone: '+966112345679',
      commercialRegister: 'CR1234567891',
      companyType: 'construction',
      establishedYear: 2010,
      address: 'المنطقة الصناعية الثانية، جدة 21442، المملكة العربية السعودية',
      website: 'https://construction.sa',
      profileImage: 'https://via.placeholder.com/150/ff6600/ffffff?text=Build',
      description: 'مؤسسة متخصصة في مشاريع البناء والتشييد والمقاولات العامة',
      employeeCount: 300,
      annualRevenue: 75000000,
      certifications: ['ترخيص المقاولات الدرجة الأولى', 'شهادة الجودة والسلامة', 'ISO 45001'],
      lastLogin: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
      createdAt: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000), // 40 days ago
      emailVerified: true,
      phoneVerified: true
    },
    {
      name: 'شركة الخدمات اللوجستية المتكاملة',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'company',
      status: 'documents_submitted',
      phone: '+966112345680',
      commercialRegister: 'CR1234567892',
      companyType: 'logistics',
      establishedYear: 2018,
      address: 'المنطقة الصناعية الأولى، الدمام 31441، المملكة العربية السعودية',
      website: 'https://logistics.sa',
      profileImage: 'https://via.placeholder.com/150/666666/ffffff?text=Log',
      description: 'شركة متخصصة في الخدمات اللوجستية والنقل والتوزيع',
      employeeCount: 80,
      annualRevenue: 15000000,
      certifications: ['ترخيص النقل التجاري', 'شهادة الجودة'],
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      emailVerified: true,
      phoneVerified: false
    },
    {
      name: 'شركة الطاقة المتجددة والبيئة',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'company',
      status: 'approved',
      phone: '+966112345681',
      commercialRegister: 'CR1234567893',
      companyType: 'energy',
      establishedYear: 2020,
      address: 'مدينة الملك عبدالله الاقتصادية، رابغ 23955، المملكة العربية السعودية',
      website: 'https://greenenergy.sa',
      profileImage: 'https://via.placeholder.com/150/00cc66/ffffff?text=Green',
      description: 'شركة رائدة في مجال الطاقة المتجددة والحلول البيئية المستدامة',
      employeeCount: 120,
      annualRevenue: 30000000,
      certifications: ['ISO 14001:2015', 'LEED Certification', 'رخصة الطاقة المتجددة'],
      lastLogin: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
      createdAt: new Date(Date.now() - 18 * 24 * 60 * 60 * 1000), // 18 days ago
      emailVerified: true,
      phoneVerified: true
    },
    {
      name: 'مؤسسة الخدمات الطبية المتخصصة',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'company',
      status: 'blocked',
      phone: '+966112345682',
      commercialRegister: 'CR1234567894',
      companyType: 'healthcare',
      establishedYear: 2012,
      address: 'حي الملك فيصل، الرياض 11564، المملكة العربية السعودية',
      description: 'مؤسسة متخصصة في توريد المعدات والخدمات الطبية',
      employeeCount: 50,
      annualRevenue: 8000000,
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      emailVerified: false,
      phoneVerified: false,
      blockReason: 'عدم استكمال الوثائق المطلوبة'
    },

    // ========== GOVERNMENT USERS ==========
    {
      name: 'أحمد بن سعد المالكي',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'government',
      status: 'approved',
      phone: '+966114567890',
      department: 'وزارة التجارة والاستثمار',
      position: 'مدير عام المشتريات الحكومية',
      governmentId: 'GOV001',
      address: 'وزارة التجارة والاستثمار، الرياض 11148، المملكة العربية السعودية',
      profileImage: 'https://via.placeholder.com/150/003366/ffffff?text=وزارة',
      employeeId: 'EMP001',
      securityClearance: 'سري',
      lastLogin: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      createdAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000), // 35 days ago
      emailVerified: true,
      phoneVerified: true
    },
    {
      name: 'فاطمة بنت عبدالله الزهراني',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'government',
      status: 'approved',
      phone: '+966114567891',
      department: 'وزارة الصحة',
      position: 'مديرة المشتريات الطبية والمعدات',
      governmentId: 'GOV002',
      address: 'وزارة الصحة، الرياض 11176، المملكة العربية السعودية',
      profileImage: 'https://via.placeholder.com/150/cc0033/ffffff?text=صحة',
      employeeId: 'EMP002',
      securityClearance: 'محدود',
      lastLogin: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
      createdAt: new Date(Date.now() - 50 * 24 * 60 * 60 * 1000), // 50 days ago
      emailVerified: true,
      phoneVerified: true
    },
    {
      name: 'محمد بن عبدالرحمن القحطاني',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'government',
      status: 'approved',
      phone: '+966114567892',
      department: 'وزارة التعليم',
      position: 'مدير المشتريات التعليمية',
      governmentId: 'GOV003',
      address: 'وزارة التعليم، الرياض 11148، المملكة العربية السعودية',
      profileImage: 'https://via.placeholder.com/150/009933/ffffff?text=تعليم',
      employeeId: 'EMP003',
      securityClearance: 'عام',
      lastLogin: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
      createdAt: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000), // 28 days ago
      emailVerified: true,
      phoneVerified: true
    },
    {
      name: 'سارة بنت خالد العتيبي',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'government',
      status: 'documents_submitted',
      phone: '+966114567893',
      department: 'وزارة النقل والخدمات اللوجستية',
      position: 'مديرة المشاريع والمناقصات',
      governmentId: 'GOV004',
      address: 'وزارة النقل، الرياض 11148، المملكة العربية السعودية',
      employeeId: 'EMP004',
      securityClearance: 'محدود',
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      emailVerified: true,
      phoneVerified: false
    },
    {
      name: 'عبدالله بن أحمد الشهري',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'government',
      status: 'approved',
      phone: '+966114567894',
      department: 'وزارة الدفاع',
      position: 'مدير المشتريات العسكرية',
      governmentId: 'GOV005',
      address: 'وزارة الدفاع، الرياض 11165، المملكة العربية السعودية',
      profileImage: 'https://via.placeholder.com/150/660000/ffffff?text=دفاع',
      employeeId: 'EMP005',
      securityClearance: 'سري للغاية',
      lastLogin: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
      createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 60 days ago
      emailVerified: true,
      phoneVerified: true
    }
  ];
  
  const createdUsers = await User.insertMany(users);
  console.log(`✅ Created ${createdUsers.length} test users`);
  return createdUsers;
};

const createTestAuctions = async (users) => {
  console.log('🏷️ Creating test auctions...');
  
  const companies = users.filter(user => user.role === 'company' && user.status === 'approved');
  
  const auctions = [
    {
      title: 'جهاز كمبيوتر محمول عالي الأداء',
      description: 'جهاز كمبيوتر محمول للألعاب والتصميم مع مواصفات عالية',
      startingPrice: 5000,
      currentPrice: 5000,
      category: 'electronics',
      condition: 'new',
      seller: companies[0]._id,
      auctioneer: companies[0]._id,
      startDate: new Date(),
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      status: 'active',
      images: [{ url: 'https://example.com/laptop1.jpg', caption: 'جهاز كمبيوتر محمول', isMain: true }],
      specifications: {
        brand: 'Dell',
        model: 'XPS 15',
        processor: 'Intel i7',
        ram: '16GB',
        storage: '512GB SSD'
      }
    },
    {
      title: 'سيارة مرسيدس C200 موديل 2020',
      description: 'سيارة مرسيدس C200 في حالة ممتازة، قليلة الاستخدام',
      startingPrice: 150000,
      currentPrice: 165000,
      category: 'vehicles',
      condition: 'excellent',
      seller: companies[1]._id,
      auctioneer: companies[1]._id,
      startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // Started 2 days ago
      endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
      status: 'active',
      images: [{ url: 'https://example.com/mercedes1.jpg', caption: 'سيارة مرسيدس C200', isMain: true }],
      specifications: {
        brand: 'Mercedes',
        model: 'C200',
        year: 2020,
        mileage: '25000 km',
        color: 'أسود'
      },
      bids: [
        {
          bidder: users.find(u => u.email === '<EMAIL>')._id,
          amount: 160000,
          timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
        },
        {
          bidder: users.find(u => u.email === '<EMAIL>')._id,
          amount: 165000,
          timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000)
        }
      ]
    }
  ];
  
  const createdAuctions = await Auction.insertMany(auctions);
  console.log(`✅ Created ${createdAuctions.length} test auctions`);
  return createdAuctions;
};

const createTestTenders = async (users) => {
  console.log('📋 Creating test tenders...');

  const governments = users.filter(user => user.role === 'government' && user.status === 'approved');
  const companies = users.filter(user => user.role === 'company' && user.status === 'approved');

  const tenders = [
    {
      title: 'توريد أجهزة كمبيوتر للمدارس الحكومية',
      description: 'مناقصة لتوريد 500 جهاز كمبيوتر للمدارس الحكومية في منطقة الرياض',
      category: 'it_technology',
      budget: 2500000,
      startDate: new Date(),
      deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      status: 'open',
      organizer: governments[0]._id,
      location: {
        city: 'الرياض',
        region: 'منطقة الرياض',
        country: 'Saudi Arabia'
      },
      requirements: [
        'شهادة ISO 9001',
        'خبرة لا تقل عن 5 سنوات',
        'ضمان لمدة 3 سنوات'
      ],
      documents: ['tender_specs.pdf', 'terms_conditions.pdf'],
      applications: [
        {
          applicant: companies[0]._id,
          submissionDate: new Date(),
          proposedPrice: 2400000,
          documents: ['company_profile.pdf', 'technical_proposal.pdf'],
          status: 'submitted'
        }
      ]
    },
    {
      title: 'مشروع إنشاء مستشفى عام',
      description: 'مناقصة لإنشاء مستشفى عام بسعة 200 سرير في مدينة جدة',
      category: 'construction',
      budget: 50000000,
      startDate: new Date(),
      deadline: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
      status: 'open',
      organizer: governments[1]._id,
      location: {
        city: 'جدة',
        region: 'منطقة مكة المكرمة',
        country: 'Saudi Arabia'
      },
      requirements: [
        'ترخيص المقاولات من الدرجة الأولى',
        'خبرة في مشاريع المستشفيات',
        'شهادة الجودة والسلامة'
      ],
      documents: ['hospital_plans.pdf', 'specifications.pdf']
    }
  ];

  const createdTenders = await Tender.insertMany(tenders);
  console.log(`✅ Created ${createdTenders.length} test tenders`);
  return createdTenders;
};

const seedTestData = async () => {
  try {
    console.log('🌱 Starting test data seeding...\n');

    await connectDB();
    await clearExistingData();

    const users = await createTestUsers();
    const auctions = await createTestAuctions(users);
    const tenders = await createTestTenders(users);

    console.log('\n🎉 Test data seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   👥 Users: ${users.length}`);
    console.log(`   🏷️ Auctions: ${auctions.length}`);
    console.log(`   📋 Tenders: ${tenders.length}`);

    console.log('\n🔑 Test Accounts:');
    console.log('   Admin: <EMAIL> / password123');
    console.log('   Individual: <EMAIL> / password123');
    console.log('   Company: <EMAIL> / password123');
    console.log('   Government: <EMAIL> / password123');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding test data:', error);
    process.exit(1);
  }
};

// Run the seeder
if (require.main === module) {
  seedTestData();
}

module.exports = { seedTestData };
