const mongoose = require('mongoose');
const User = require('./models/User');
require('dotenv').config();

async function updateUserStatus() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/auction_platform');
    console.log('Connected to MongoDB');

    // Get email from command line argument or use default
    const email = process.argv[2] || '<EMAIL>';
    
    // Find the user
    const user = await User.findOne({ email: email });
    if (!user) {
      console.log(`User with email ${email} not found`);
      process.exit(1);
    }

    console.log('Found user:', user.email, 'Current status:', user.status);

    // Update user to approved status (simulate document upload and approval)
    user.status = 'approved';
    user.submittedAt = new Date();
    user.reviewedAt = new Date();

    await user.save();
    console.log('User status updated successfully!');
    console.log('New status:', user.status);

    process.exit(0);
  } catch (error) {
    console.error('Error updating user:', error);
    process.exit(1);
  }
}

updateUserStatus();
