{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:08:31:831"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:08:31:831"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:10:00:100"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:10:34:1034"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:10:34:1034"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:10:43:1043"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:10:43:1043"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:10:55:1055"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:10:55:1055"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:12:00:120"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:12:59:1259"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:12:59:1259"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:13:00:130"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:13:17:1317"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:13:17:1317"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:14:00:140"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:14:06:146"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:14:06:146"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:14:36:1436"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:14:36:1436"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:16:00:160"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 14:16:35:1635","url":"/api/user/dashboard/stats","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/profile\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"PUT","timestamp":"2025-07-11 14:16:36:1636","url":"/api/user/profile","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/company/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:16:36:1636","url":"/api/company/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/dashboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:16:37:1637","url":"/api/government/dashboard","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:18:00:180"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:18:09:189"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:18:09:189"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:18:43:1843"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:18:43:1843"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:19:00:190"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:19:18:1918"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:19:18:1918"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:20:00:200"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 14:20:14:2014","url":"/api/user/dashboard/stats","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/profile\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"PUT","timestamp":"2025-07-11 14:20:14:2014","url":"/api/user/profile","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/company/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:20:15:2015","url":"/api/company/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/dashboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:20:15:2015","url":"/api/government/dashboard","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/users/6870f3660040fd0e29176f8c/approve\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"PUT","timestamp":"2025-07-11 14:20:16:2016","url":"/api/admin/users/6870f3660040fd0e29176f8c/approve","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:28:00:280"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/company/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:28:33:2833","url":"/api/company/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/dashboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:28:33:2833","url":"/api/government/dashboard","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:31:00:310"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:31:47:3147"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:31:47:3147"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:32:00:320"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:32:09:329"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:32:09:329"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:32:49:3249"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:32:49:3249"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:33:00:330"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:33:04:334"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:33:04:334"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:33:20:3320"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:33:20:3320"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:33:31:3331"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:33:31:3331"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:33:41:3341"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:33:41:3341"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:40:00:400"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:40:41:4041"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:40:41:4041"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:46:00:460"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:48:02:482"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:48:02:482"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:52:00:520"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:52:00:520"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:53:00:530"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:53:39:5339"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:53:39:5339"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:54:00:540"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:54:12:5412"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:54:12:5412"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:54:29:5429"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:54:29:5429"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 15:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:39:01:391"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:43:00:430"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:43:48:4348"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:43:48:4348"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:45:00:450"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:45:14:4514"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:45:14:4514"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:46:00:460"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:46:19:4619"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:46:19:4619"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:47:00:470"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:47:16:4716"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:47:16:4716"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/tenders/687107e023168d085301922c/apply\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"POST","timestamp":"2025-07-11 15:47:28:4728","url":"/api/tenders/687107e023168d085301922c/apply","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:53:00:530"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871093b23168d085301924c/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 15:53:15:5315","url":"/api/government/tenders/6871093b23168d085301924c/applications","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:57:00:570"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:57:24:5724"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:57:24:5724"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:57:40:5740"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:57:40:5740"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:58:00:580"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:58:02:582"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:58:02:582"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 16:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:00:00:00"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:00:22:022"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:00:22:022"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:02:00:20"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/68710b4f07a04ce831926d6d/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:02:07:27","url":"/api/government/tenders/68710b4f07a04ce831926d6d/applications","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:05:00:50"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/68710c1e07a04ce831926ea9/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:05:34:534","url":"/api/government/tenders/68710c1e07a04ce831926ea9/applications","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:06:00:60"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:07:24:724"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:07:24:724"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:20:00:200"}
