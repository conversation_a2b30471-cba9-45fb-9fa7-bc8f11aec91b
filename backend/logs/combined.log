{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 00:27:09:279"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 00:27:09:279"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 00:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 00:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 00:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 00:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 00:32:00:320"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 00:35:05:355"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 00:35:05:355"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 00:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 00:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 00:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 00:39:00:390"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 01:24:14:2414"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 01:24:14:2414"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 01:37:00:370"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 02:20:43:2043"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 02:20:43:2043"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 02:28:14:2814"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 02:28:14:2814"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 02:37:14:3714"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 02:37:14:3714"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 02:38:00:380"}
{"level":"info","message":"Found 1 auctions that should end","timestamp":"2025-07-04 02:38:01:381"}
{"level":"info","message":"Auction 68670b07044d1cce535d22a5 ended with status: completed","timestamp":"2025-07-04 02:38:01:381"}
{"level":"error","message":"Error processing auction 68670b07044d1cce535d22a5: Cannot read properties of undefined (reading 'toString')","stack":"TypeError: Cannot read properties of undefined (reading 'toString')\n    at Task._execution (/Users/<USER>/Desktop/brid1/backend/server.js:181:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-04 02:38:01:381"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 02:39:00:390"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 02:42:56:4256"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 02:42:56:4256"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 02:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 02:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 02:45:00:450"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 02:48:16:4816"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 02:48:16:4816"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 02:52:29:5229"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 02:52:29:5229"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 02:53:00:530"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 02:58:30:5830"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 02:58:30:5830"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 02:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 03:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:00:00:00"}
{"level":"info","message":"Found 1 expired pending accounts","timestamp":"2025-07-04 03:00:00:00"}
{"level":"info","message":"Deleted 1 expired accounts","timestamp":"2025-07-04 03:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:12:00:120"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/auth/login\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at router.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"GET","timestamp":"2025-07-04 03:12:11:1211","url":"/api/auth/login","userAgent":"curl/8.7.1"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:24:00:240"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/auth/verify-email\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at router.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"POST","timestamp":"2025-07-04 03:24:30:2430","url":"/api/auth/verify-email","userAgent":"curl/8.7.1"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/auth/\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at router.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"GET","timestamp":"2025-07-04 03:24:37:2437","url":"/api/auth/","userAgent":"curl/8.7.1"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:25:00:250"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 03:26:00:260"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 03:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:35:00:350"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 03:35:38:3538"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 03:35:38:3538"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 03:40:00:400"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 10:19:03:193"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 10:19:03:193"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 10:45:53:4553"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 10:45:53:4553"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 10:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 10:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 10:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 10:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 10:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 10:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 10:52:00:520"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 10:53:14:5314"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 10:53:14:5314"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/api/auctions/leaderboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-04 10:53:31:5331","url":"/api/api/auctions/leaderboard","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/api/auctions/leaderboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-04 10:53:32:5332","url":"/api/api/auctions/leaderboard","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/api/auctions/leaderboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-04 10:53:33:5333","url":"/api/api/auctions/leaderboard","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 10:57:24:5724"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 10:57:24:5724"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/api/auctions/leaderboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-04 10:57:32:5732","url":"/api/api/auctions/leaderboard","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/api/auctions/leaderboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-04 10:57:34:5734","url":"/api/api/auctions/leaderboard","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 10:58:31:5831"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 10:58:31:5831"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 10:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 11:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:31:00:310"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 11:34:38:3438"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 11:34:38:3438"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:47:00:470"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 11:48:39:4839"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 11:48:39:4839"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:49:00:490"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 11:54:42:5442"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 11:54:42:5442"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 11:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 12:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 12:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 13:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:29:00:290"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 13:31:10:3110"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 13:31:10:3110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 13:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 14:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 14:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 15:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:41:01:411"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 15:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 16:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 16:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 17:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 17:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 18:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:35:00:350"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/reports/financial\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","method":"GET","timestamp":"2025-07-04 18:35:13:3513","url":"/api/admin/reports/financial","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/reports/financial\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","method":"GET","timestamp":"2025-07-04 18:35:13:3513","url":"/api/admin/reports/financial","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:44:00:440"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-04 18:46:58:4658"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-04 18:46:58:4658"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 18:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 19:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 19:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 20:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 20:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 21:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 21:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 22:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 22:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-04 23:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-04 23:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-05 00:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:30:00:300"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-05 00:30:48:3048"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-05 00:30:48:3048"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:33:00:330"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-05 00:33:31:3331"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-05 00:33:31:3331"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-05 00:33:48:3348"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-05 00:33:48:3348"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 00:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-05 01:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 01:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-05 02:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 02:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 02:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 02:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 02:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 02:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 02:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 02:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 02:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 02:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 02:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 02:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-05 02:11:00:110"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-10 14:19:02:192"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-10 14:19:02:192"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:20:00:200"}
{"level":"info","message":"Found 2 auctions that should end","timestamp":"2025-07-10 14:20:00:200"}
{"level":"info","message":"Auction 6867920fb96ab8ab6929c111 ended with status: ended","timestamp":"2025-07-10 14:20:00:200"}
{"level":"error","message":"Error processing auction 6867920fb96ab8ab6929c111: Cannot read properties of undefined (reading 'toString')","stack":"TypeError: Cannot read properties of undefined (reading 'toString')\n    at Task._execution (/Users/<USER>/Desktop/brid1/backend/server.js:183:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-10 14:20:00:200"}
{"level":"info","message":"Auction 6867920fb96ab8ab6929c109 ended with status: ended","timestamp":"2025-07-10 14:20:00:200"}
{"level":"error","message":"Error processing auction 6867920fb96ab8ab6929c109: Cannot read properties of undefined (reading 'toString')","stack":"TypeError: Cannot read properties of undefined (reading 'toString')\n    at Task._execution (/Users/<USER>/Desktop/brid1/backend/server.js:183:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-10 14:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:21:00:210"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-10 14:22:55:2255"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-10 14:22:55:2255"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:26:00:260"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-10 14:26:46:2646"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-10 14:26:46:2646"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:27:00:270"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-10 14:30:04:304"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-10 14:30:04:304"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:34:00:340"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-10 14:38:12:3812"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-10 14:38:12:3812"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:41:00:410"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-10 14:42:57:4257"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-10 14:42:57:4257"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 14:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-10 15:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:33:00:330"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/users/686fa20457d8b62ecd6168ed/activate\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"POST","timestamp":"2025-07-10 15:33:44:3344","url":"/api/admin/users/686fa20457d8b62ecd6168ed/activate","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/users/686fa20457d8b62ecd6168ed/activate\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"POST","timestamp":"2025-07-10 15:33:46:3346","url":"/api/admin/users/686fa20457d8b62ecd6168ed/activate","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/users/6867862cc07883a1548b4453/deactivate\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"POST","timestamp":"2025-07-10 15:33:47:3347","url":"/api/admin/users/6867862cc07883a1548b4453/deactivate","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:34:00:340"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/auctions/1\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-10 15:34:55:3455","url":"/api/admin/auctions/1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/auctions/1\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-10 15:34:55:3455","url":"/api/admin/auctions/1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:36:00:360"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-10 15:37:14:3714"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-10 15:37:14:3714"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:40:00:400"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/pending-accounts/686fa20457d8b62ecd6168ed/approve\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"POST","timestamp":"2025-07-10 15:40:41:4041","url":"/api/admin/pending-accounts/686fa20457d8b62ecd6168ed/approve","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:43:00:430"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/users/686fa20457d8b62ecd6168ed/activate\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"POST","timestamp":"2025-07-10 15:43:14:4314","url":"/api/admin/users/686fa20457d8b62ecd6168ed/activate","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:45:00:450"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/users/686fa20457d8b62ecd6168ed/activate\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"POST","timestamp":"2025-07-10 15:45:06:456","url":"/api/admin/users/686fa20457d8b62ecd6168ed/activate","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-10 15:46:08:468"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-10 15:46:08:468"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:47:00:470"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-10 15:47:37:4737"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-10 15:47:37:4737"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:50:00:500"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/auctions/1\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-10 15:50:36:5036","url":"/api/admin/auctions/1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/auctions/1\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-10 15:50:36:5036","url":"/api/admin/auctions/1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 15:59:00:590"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-10 15:59:11:5911"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-10 15:59:11:5911"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-10 16:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:10:00:100"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/auctions/6867920fb96ab8ab6929c11a/watch\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"DELETE","timestamp":"2025-07-10 16:10:57:1057","url":"/api/auctions/6867920fb96ab8ab6929c11a/watch","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 16:58:00:580"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-10 16:59:00:590"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-10 16:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-10 17:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-10 17:42:00:420"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 11:58:29:5829"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 11:58:29:5829"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 11:59:00:590"}
{"level":"info","message":"Found 1 auctions that should end","timestamp":"2025-07-11 11:59:00:590"}
{"level":"info","message":"Auction 6867920fb96ab8ab6929c103 ended with status: ended","timestamp":"2025-07-11 11:59:00:590"}
{"level":"error","message":"Error processing auction 6867920fb96ab8ab6929c103: Cannot read properties of undefined (reading 'toString')","stack":"TypeError: Cannot read properties of undefined (reading 'toString')\n    at Task._execution (/Users/<USER>/Desktop/brid1/backend/server.js:189:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-11 11:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 12:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:00:00:00"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 12:01:04:14"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 12:01:04:14"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:24:00:240"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:24:41:2441","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:24:41:2441","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:25:01:251"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 12:25:43:2543"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 12:25:43:2543"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:26:00:260"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 12:26:53:2653"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 12:26:53:2653"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:27:00:270"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:27:57:2757","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:27:57:2757","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:37:00:370"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:37:12:3712","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:37:12:3712","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:37:31:3731","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:37:31:3731","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:37:54:3754","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:37:54:3754","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:40:00:400"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 12:40:36:4036"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 12:40:36:4036"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:40:38:4038","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:40:38:4038","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:41:00:410"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:41:06:416","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:41:06:416","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 12:41:10:4110"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 12:41:10:4110"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:41:23:4123","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:41:23:4123","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:41:26:4126","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 12:41:26:4126","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:42:00:420"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 12:42:06:426"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 12:42:06:426"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:43:00:430"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 12:43:35:4335"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 12:43:35:4335"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 12:44:00:440"}
