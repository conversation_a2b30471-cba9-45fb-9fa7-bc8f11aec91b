require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');

/**
 * User Management Utility for Testing
 * Provides functions to manage test users during testing
 */

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/auction_platform');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// List all users with their status
const listAllUsers = async () => {
  try {
    const users = await User.find({}, {
      name: 1,
      email: 1,
      role: 1,
      status: 1,
      createdAt: 1,
      lastLogin: 1
    }).sort({ createdAt: -1 });

    console.log('\n👥 All Users in Database:');
    console.log('=' .repeat(80));
    
    const roleGroups = {
      admin: [],
      super_admin: [],
      individual: [],
      company: [],
      government: []
    };

    users.forEach(user => {
      roleGroups[user.role] = roleGroups[user.role] || [];
      roleGroups[user.role].push(user);
    });

    Object.keys(roleGroups).forEach(role => {
      if (roleGroups[role].length > 0) {
        console.log(`\n🔹 ${role.toUpperCase()} USERS (${roleGroups[role].length}):`);
        roleGroups[role].forEach(user => {
          const statusIcon = user.status === 'approved' ? '✅' : 
                           user.status === 'pending' ? '⏳' : 
                           user.status === 'blocked' ? '🚫' : '❓';
          const lastLogin = user.lastLogin ? 
            `(Last: ${user.lastLogin.toLocaleDateString()})` : '(Never)';
          
          console.log(`   ${statusIcon} ${user.name} - ${user.email} ${lastLogin}`);
        });
      }
    });

    console.log(`\n📊 Total Users: ${users.length}`);
    return users;
  } catch (error) {
    console.error('❌ Error listing users:', error);
  }
};

// Approve pending users
const approvePendingUsers = async () => {
  try {
    const pendingUsers = await User.find({ status: 'pending' });
    
    if (pendingUsers.length === 0) {
      console.log('ℹ️ No pending users found');
      return;
    }

    console.log(`\n⏳ Found ${pendingUsers.length} pending users:`);
    pendingUsers.forEach(user => {
      console.log(`   - ${user.name} (${user.email}) - ${user.role}`);
    });

    const result = await User.updateMany(
      { status: 'pending' },
      { 
        status: 'approved',
        approvedAt: new Date(),
        approvedBy: 'system'
      }
    );

    console.log(`✅ Approved ${result.modifiedCount} users`);
    return result;
  } catch (error) {
    console.error('❌ Error approving users:', error);
  }
};

// Block a user by email
const blockUser = async (email, reason = 'Administrative action') => {
  try {
    const user = await User.findOne({ email });
    
    if (!user) {
      console.log(`❌ User not found: ${email}`);
      return;
    }

    user.status = 'blocked';
    user.blockReason = reason;
    user.blockedAt = new Date();
    await user.save();

    console.log(`🚫 Blocked user: ${user.name} (${email})`);
    console.log(`   Reason: ${reason}`);
    return user;
  } catch (error) {
    console.error('❌ Error blocking user:', error);
  }
};

// Unblock a user by email
const unblockUser = async (email) => {
  try {
    const user = await User.findOne({ email });
    
    if (!user) {
      console.log(`❌ User not found: ${email}`);
      return;
    }

    user.status = 'approved';
    user.blockReason = undefined;
    user.blockedAt = undefined;
    user.unblockedAt = new Date();
    await user.save();

    console.log(`✅ Unblocked user: ${user.name} (${email})`);
    return user;
  } catch (error) {
    console.error('❌ Error unblocking user:', error);
  }
};

// Get user statistics
const getUserStats = async () => {
  try {
    const stats = await User.aggregate([
      {
        $group: {
          _id: { role: '$role', status: '$status' },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: '$_id.role',
          statuses: {
            $push: {
              status: '$_id.status',
              count: '$count'
            }
          },
          total: { $sum: '$count' }
        }
      }
    ]);

    console.log('\n📊 User Statistics:');
    console.log('=' .repeat(50));
    
    let totalUsers = 0;
    stats.forEach(roleStat => {
      console.log(`\n🔹 ${roleStat._id.toUpperCase()}: ${roleStat.total} users`);
      roleStat.statuses.forEach(statusStat => {
        const icon = statusStat.status === 'approved' ? '✅' : 
                    statusStat.status === 'pending' ? '⏳' : 
                    statusStat.status === 'blocked' ? '🚫' : '❓';
        console.log(`   ${icon} ${statusStat.status}: ${statusStat.count}`);
      });
      totalUsers += roleStat.total;
    });

    console.log(`\n📈 Total Users: ${totalUsers}`);
    return stats;
  } catch (error) {
    console.error('❌ Error getting user stats:', error);
  }
};

// Create a test user
const createTestUser = async (userData) => {
  try {
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash(userData.password || 'password123', 10);
    
    const user = new User({
      ...userData,
      password: hashedPassword,
      createdAt: new Date(),
      emailVerified: true
    });

    await user.save();
    console.log(`✅ Created test user: ${user.name} (${user.email})`);
    return user;
  } catch (error) {
    console.error('❌ Error creating test user:', error);
  }
};

// Command line interface
const runCommand = async () => {
  await connectDB();
  
  const command = process.argv[2];
  const param = process.argv[3];
  const param2 = process.argv[4];

  switch (command) {
    case 'list':
      await listAllUsers();
      break;
    case 'stats':
      await getUserStats();
      break;
    case 'approve':
      await approvePendingUsers();
      break;
    case 'block':
      if (!param) {
        console.log('❌ Please provide email: node user-management-utility.<NAME_EMAIL> [reason]');
        break;
      }
      await blockUser(param, param2);
      break;
    case 'unblock':
      if (!param) {
        console.log('❌ Please provide email: node user-management-utility.<NAME_EMAIL>');
        break;
      }
      await unblockUser(param);
      break;
    default:
      console.log('📋 Available commands:');
      console.log('  list     - List all users');
      console.log('  stats    - Show user statistics');
      console.log('  approve  - Approve all pending users');
      console.log('  block    - Block a user by email');
      console.log('  unblock  - Unblock a user by email');
      console.log('\nExamples:');
      console.log('  node user-management-utility.js list');
      console.log('  node user-management-utility.js approve');
      console.log('  node user-management-utility.<NAME_EMAIL> "Violation of terms"');
  }

  process.exit(0);
};

// Run if called directly
if (require.main === module) {
  runCommand().catch(error => {
    console.error('❌ Command failed:', error);
    process.exit(1);
  });
}

module.exports = {
  listAllUsers,
  approvePendingUsers,
  blockUser,
  unblockUser,
  getUserStats,
  createTestUser
};
