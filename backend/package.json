{"name": "auction-tender-backend", "version": "1.0.0", "description": "Backend API for Auction and Tender Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node seed-test-data.js", "setup": "node ../setup-testing-environment.js", "reset-db": "node -e \"require('./seed-test-data.js').seedTestData()\"", "create-admin": "node create-admin.js", "users": "node user-management-utility.js", "users:list": "node user-management-utility.js list", "users:stats": "node user-management-utility.js stats", "users:approve": "node user-management-utility.js approve", "test:workflows": "cd .. && node workflow-testing-suite.js", "test:individual": "cd .. && node workflow-testing-suite.js individual", "test:company": "cd .. && node workflow-testing-suite.js company", "test:government": "cd .. && node workflow-testing-suite.js government", "test:admin-workflows": "cd .. && node workflow-testing-suite.js admin", "test:registration": "cd .. && node workflow-testing-suite.js registration", "test:errors": "cd .. && node workflow-testing-suite.js errors", "test:auctions": "cd .. && node auction-system-testing.js", "test:auction-creation": "cd .. && node auction-system-testing.js creation", "test:auction-bidding": "cd .. && node auction-system-testing.js bidding", "test:auction-management": "cd .. && node auction-system-testing.js management", "test:auction-completion": "cd .. && node auction-system-testing.js completion", "test:tenders": "cd .. && node tender-system-testing.js", "test:tender-creation": "cd .. && node tender-system-testing.js creation", "test:tender-applications": "cd .. && node tender-system-testing.js applications", "test:tender-review": "cd .. && node tender-system-testing.js review", "test:tender-award": "cd .. && node tender-system-testing.js award", "test:tender-post-award": "cd .. && node tender-system-testing.js post-award", "test:admin": "cd .. && node admin-functionality-testing.js", "test:admin-users": "cd .. && node admin-functionality-testing.js users", "test:admin-emails": "cd .. && node admin-functionality-testing.js emails", "test:admin-support": "cd .. && node admin-functionality-testing.js support", "test:admin-monitoring": "cd .. && node admin-functionality-testing.js monitoring", "test:admin-moderation": "cd .. && node admin-functionality-testing.js moderation", "test:admin-security": "cd .. && node admin-functionality-testing.js security"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "colors": "^1.4.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "winston": "^3.17.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "babel-jest": "^29.7.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "keywords": ["auction", "tender", "nodejs", "express", "mongodb"], "author": "Your Name", "license": "MIT"}