{"name": "auction-tender-backend", "version": "1.0.0", "description": "Backend API for Auction and Tender Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node seed-test-data.js", "setup": "node ../setup-testing-environment.js", "reset-db": "node -e \"require('./seed-test-data.js').seedTestData()\"", "create-admin": "node create-admin.js", "users": "node user-management-utility.js", "users:list": "node user-management-utility.js list", "users:stats": "node user-management-utility.js stats", "users:approve": "node user-management-utility.js approve", "test:workflows": "node ../workflow-testing-suite.js", "test:individual": "node ../workflow-testing-suite.js individual", "test:company": "node ../workflow-testing-suite.js company", "test:government": "node ../workflow-testing-suite.js government", "test:admin": "node ../workflow-testing-suite.js admin", "test:registration": "node ../workflow-testing-suite.js registration", "test:errors": "node ../workflow-testing-suite.js errors", "test:auctions": "node ../auction-system-testing.js", "test:auction-creation": "node ../auction-system-testing.js creation", "test:auction-bidding": "node ../auction-system-testing.js bidding", "test:auction-management": "node ../auction-system-testing.js management", "test:auction-completion": "node ../auction-system-testing.js completion"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "winston": "^3.17.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "babel-jest": "^29.7.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "keywords": ["auction", "tender", "nodejs", "express", "mongodb"], "author": "Your Name", "license": "MIT"}