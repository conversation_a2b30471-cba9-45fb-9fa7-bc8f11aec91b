const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const mongoose = require('mongoose');
const http = require('http');
const socketIo = require('socket.io');
const cron = require('node-cron');
const db = require('./db');
const logger = require('./utils/logger');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const auctionRoutes = require('./routes/auctions');
const tenderRoutes = require('./routes/tenders');
const adminRoutes = require('./routes/admin');
const favoriteRoutes = require('./routes/favorites');
const activityRoutes = require('./routes/activity');
const messageRoutes = require('./routes/messages');
const templateRoutes = require('./routes/templates');
const paymentRoutes = require('./routes/payments');
const companyRoutes = require('./routes/company');
const governmentRoutes = require('./routes/government');
const searchRoutes = require('./routes/search');
const supportRoutes = require('./routes/support');
const statsRoutes = require('./routes/stats');

// Import middleware
const { errorHandler } = require('./middleware/errorHandler');
const { notFound } = require('./middleware/notFound');
const { urlFix } = require('./middleware/urlFix');

// Import socket handlers
const { initializeSocketHandlers } = require('./utils/socketHandlers');

// Import models for cron jobs
const User = require('./models/User');
const Auction = require('./models/Auction');

// Initialize Express app
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.CLIENT_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(urlFix); // Fix double /api prefix issue
app.use(helmet());
app.use(compression());
app.use(morgan('combined'));
app.use(cors({
  origin: process.env.CLIENT_URL || "http://localhost:3000",
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Initialize enhanced socket handlers
initializeSocketHandlers(io);

// Make io accessible to routes
app.set('io', io);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/auctions', auctionRoutes);
app.use('/api/tenders', tenderRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/company', companyRoutes);
app.use('/api/government', governmentRoutes);
app.use('/api/search', searchRoutes);
app.use('/api/favorites', favoriteRoutes);
app.use('/api/activity', activityRoutes);
app.use('/api/messages', messageRoutes);
app.use('/api/templates', templateRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/support', supportRoutes);
app.use('/api/stats', statsRoutes);

// Health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    // Check database connection
    const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';

    // Get basic stats
    const userCount = await User.countDocuments();
    const auctionCount = await Auction.countDocuments();

    res.status(200).json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: dbStatus,
      stats: {
        users: userCount,
        auctions: auctionCount
      },
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Cron jobs
// Check for account activation timeouts (runs every hour)
cron.schedule('0 * * * *', async () => {
  logger.info('Running account activation timeout check...');
  try {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    // Find users with pending_email_verification status created more than 24 hours ago
    const expiredUsers = await User.find({
      status: 'pending_email_verification',
      createdAt: { $lt: twentyFourHoursAgo }
    });
    
    if (expiredUsers.length > 0) {
      logger.info(`Found ${expiredUsers.length} expired pending accounts`);
      
      // Update users to expired status (or delete them since 'expired' is not in enum)
      const updateResult = await User.deleteMany({
        status: 'pending_email_verification',
        createdAt: { $lt: twentyFourHoursAgo }
      });
      
      logger.info(`Deleted ${updateResult.deletedCount} expired accounts`);
      
      // Send real-time notifications to expired users if they're connected
      expiredUsers.forEach(user => {
        io.to(`user_${user._id}`).emit('account_expired', {
          message: 'Your account activation period has expired. Please register again.',
          timestamp: new Date().toISOString()
        });
      });
    }
  } catch (error) {
    logger.error('Error in account activation timeout check:', error);
  }
});

// Check for auction endings (runs every minute)
cron.schedule('* * * * *', async () => {
  logger.info('Checking for auction endings...');
  try {
    const now = new Date();
    
    // Find active auctions that should have ended
    const endedAuctions = await Auction.find({
      status: 'active',
      endDate: { $lt: now }
    }).populate('bids.bidder', 'username email');
    
    if (endedAuctions.length > 0) {
      logger.info(`Found ${endedAuctions.length} auctions that should end`);
      
      for (const auction of endedAuctions) {
        try {
          let newStatus = 'ended';
          let winner = null;
          
          // Check if there are any bids
          if (auction.bids && auction.bids.length > 0) {
            // Sort bids by amount (highest first) and get the winner
            const sortedBids = auction.bids.sort((a, b) => b.amount - a.amount);
            const highestBid = sortedBids[0];
            
            if (highestBid.amount >= auction.reservePrice) {
              newStatus = 'completed';
              winner = highestBid.bidder._id;
            }
          }
          
          // Update auction status
          await Auction.findByIdAndUpdate(auction._id, {
            status: newStatus,
            winner: winner,
            updatedAt: new Date()
          });
          
          logger.info(`Auction ${auction._id} ended with status: ${newStatus}`);
          
          // Notify all participants
          const participants = new Set();
          
          // Add auction creator
          participants.add(auction.createdBy.toString());
          
          // Add all bidders
          auction.bids.forEach(bid => {
            participants.add(bid.bidder._id.toString());
          });
          
          // Send notifications to all participants
          participants.forEach(userId => {
            io.to(`user_${userId}`).emit('auction_ended', {
              auctionId: auction._id,
              title: auction.title,
              status: newStatus,
              winner: winner,
              finalAmount: auction.bids.length > 0 ? Math.max(...auction.bids.map(b => b.amount)) : null,
              message: winner ? 
                `Auction "${auction.title}" has ended. Winner: ${auction.bids.find(b => b.bidder._id.toString() === winner.toString())?.bidder.username}` :
                `Auction "${auction.title}" has ended with no winner.`,
              timestamp: new Date().toISOString()
            });
          });
          
          // Broadcast to auction room
          io.to(`auction_${auction._id}`).emit('auction_status_changed', {
            auctionId: auction._id,
            status: newStatus,
            winner: winner,
            timestamp: new Date().toISOString()
          });
          
        } catch (auctionError) {
          logger.error(`Error processing auction ${auction._id}:`, auctionError);
        }
      }
    }
  } catch (error) {
    logger.error('Error in auction ending check:', error);
  }
});

// Export the app for testing
module.exports = { app, server };

// Only start server if this file is run directly
if (require.main === module) {
  const PORT = process.env.PORT || 5000;
  
  // Database connection
  db.connect();
  
  server.listen(PORT, () => {
    logger.info(`Server running on port ${PORT}`);
    logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
  });
}
