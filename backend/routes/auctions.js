const express = require('express');
const { body, validationResult } = require('express-validator');
const Auction = require('../models/Auction');
const User = require('../models/User');
const { authenticate, requireApproved, requireRole } = require('../middleware/auth');
const { uploadToCloudinary } = require('../utils/cloudinary');
const upload = require('../middleware/upload');

const router = express.Router();

// @route   GET /api/auctions
// @desc    Get all active auctions with filters
// @access  Public
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;
    const { category, search, sortBy, minPrice, maxPrice, condition, location, auctionType, shipping, autoExtend, timeRemaining, minBidCount, maxBidCount, reservePrice } = req.query;

    // Build filter object
    const filter = { status: 'active' };
    
    if (category) filter.category = category;
    if (condition) filter.condition = condition;
    if (location) filter['location.city'] = { $regex: location, $options: 'i' };
    
    if (minPrice || maxPrice) {
      filter.currentPrice = {};
      if (minPrice) filter.currentPrice.$gte = parseInt(minPrice);
      if (maxPrice) filter.currentPrice.$lte = parseInt(maxPrice);
    }

    if (auctionType) filter.auctionType = auctionType;
    if (shipping) filter['shipping.available'] = shipping === 'true';
    if (autoExtend) filter.autoExtend = autoExtend === 'true';

    if (timeRemaining) {
      const currentDate = new Date();
      const targetDate = new Date(currentDate.getTime() + parseInt(timeRemaining) * 60 * 60 * 1000);
      filter.endDate = { $lte: targetDate };
    }

    if (minBidCount || maxBidCount) {
      filter.totalBids = {};
      if (minBidCount) filter.totalBids.$gte = parseInt(minBidCount);
      if (maxBidCount) filter.totalBids.$lte = parseInt(maxBidCount);
    }

    if (reservePrice) filter.reservePrice = { $lte: parseFloat(reservePrice) };

    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    let sort = { createdAt: -1 };
    switch (sortBy) {
      case 'price_asc':
        sort = { currentPrice: 1 };
        break;
      case 'price_desc':
        sort = { currentPrice: -1 };
        break;
      case 'ending_soon':
        sort = { endDate: 1 };
        break;
      case 'newest':
        sort = { createdAt: -1 };
        break;
      case 'popular':
        sort = { views: -1 };
        break;
    }

    const auctions = await Auction.find(filter)
      .populate('seller', 'profile.fullName profile.companyName')
      .select('-bids.bidder')
      .sort(sort)
      .skip(skip)
      .limit(limit);

    const total = await Auction.countDocuments(filter);

    // Update view counts
    const auctionIds = auctions.map(auction => auction._id);
    await Auction.updateMany(
      { _id: { $in: auctionIds } },
      { $inc: { views: 1 } }
    );

    res.json({
      success: true,
      data: {
        auctions,
        pagination: {
          current: page,
          pages: Math.ceil(total / limit),
          total
        }
      }
    });
  } catch (error) {
    console.error('Get auctions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching auctions'
    });
  }
});

// @route   GET /api/auctions/featured
// @desc    Get featured auctions
// @access  Public
router.get('/featured', async (req, res) => {
  try {
    const auctions = await Auction.find({ 
      status: 'active', 
      featured: true 
    })
    .populate('seller', 'profile.fullName profile.companyName')
    .select('-bids.bidder')
    .sort({ createdAt: -1 })
    .limit(6);

    res.json({
      success: true,
      data: { auctions }
    });
  } catch (error) {
    console.error('Get featured auctions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching featured auctions'
    });
  }
});

// @route   GET /api/auctions/ending-soon
// @desc    Get auctions ending soon
// @access  Public
router.get('/ending-soon', async (req, res) => {
  try {
    const auctions = await Auction.getEndingSoon(24) // 24 hours
      .populate('seller', 'profile.fullName profile.companyName')
      .select('-bids.bidder')
      .limit(10);

    res.json({
      success: true,
      data: { auctions }
    });
  } catch (error) {
    console.error('Get ending soon auctions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching ending soon auctions'
    });
  }
});

// @route   GET /api/auctions/leaderboard
// @desc    Get bidding battle leaderboards
// @access  Public
router.get('/leaderboard', async (req, res) => {
  try {
    const { period = 'weekly', limit = 10 } = req.query;
    
    // Calculate date range based on period
    const now = new Date();
    let startDate;
    
    switch (period) {
      case 'daily':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'weekly':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'monthly':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'all-time':
        startDate = new Date(0); // Beginning of time
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // MongoDB aggregation pipeline to get top bidders
    const leaderboard = await Auction.aggregate([
      {
        $match: {
          status: { $in: ['active', 'ended'] },
          'bids.timestamp': { $gte: startDate }
        }
      },
      {
        $unwind: '$bids'
      },
      {
        $match: {
          'bids.timestamp': { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$bids.bidder',
          totalBids: { $sum: 1 },
          totalAmountBid: { $sum: '$bids.amount' },
          averageBidAmount: { $avg: '$bids.amount' },
          highestBid: { $max: '$bids.amount' },
          auctionsParticipated: { $addToSet: '$_id' },
          lastBidDate: { $max: '$bids.timestamp' }
        }
      },
      {
        $addFields: {
          auctionsCount: { $size: '$auctionsParticipated' },
          points: {
            $add: [
              { $multiply: ['$totalBids', 10] }, // 10 points per bid
              { $multiply: ['$auctionsCount', 50] }, // 50 points per auction participated
              { $divide: ['$totalAmountBid', 100] } // 1 point per $100 bid
            ]
          }
        }
      },
      {
        $sort: { points: -1 }
      },
      {
        $limit: parseInt(limit)
      }
    ]);

    // Populate user information
    const populatedLeaderboard = await User.populate(leaderboard, {
      path: '_id',
      select: 'profile.fullName profile.companyName profile.avatar userType'
    });

    // Format the leaderboard with ranks and badges
    const formattedLeaderboard = populatedLeaderboard.map((entry, index) => {
      let badge = '';
      let badgeColor = '';
      
      // Assign badges based on ranking and performance
      if (index === 0) {
        badge = '👑 Bidding Champion';
        badgeColor = 'gold';
      } else if (index === 1) {
        badge = '🥈 Bid Master';
        badgeColor = 'silver';
      } else if (index === 2) {
        badge = '🥉 Bid Expert';
        badgeColor = 'bronze';
      } else if (entry.totalBids >= 50) {
        badge = '🔥 Bid Warrior';
        badgeColor = 'red';
      } else if (entry.totalBids >= 25) {
        badge = '⚡ Bid Hunter';
        badgeColor = 'orange';
      } else if (entry.totalBids >= 10) {
        badge = '🎯 Bid Enthusiast';
        badgeColor = 'blue';
      } else {
        badge = '🌟 Rising Bidder';
        badgeColor = 'green';
      }

      return {
        rank: index + 1,
        user: {
          id: entry._id._id,
          name: entry._id.profile.fullName || entry._id.profile.companyName,
          avatar: entry._id.profile.avatar,
          userType: entry._id.userType
        },
        stats: {
          totalBids: entry.totalBids,
          totalAmountBid: Math.round(entry.totalAmountBid),
          averageBidAmount: Math.round(entry.averageBidAmount),
          highestBid: entry.highestBid,
          auctionsParticipated: entry.auctionsCount,
          points: Math.round(entry.points),
          lastBidDate: entry.lastBidDate
        },
        badge: {
          title: badge,
          color: badgeColor
        }
      };
    });

    // Get total statistics for the period
    const totalStats = await Auction.aggregate([
      {
        $match: {
          status: { $in: ['active', 'ended'] },
          'bids.timestamp': { $gte: startDate }
        }
      },
      {
        $unwind: '$bids'
      },
      {
        $match: {
          'bids.timestamp': { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          totalBids: { $sum: 1 },
          totalAmount: { $sum: '$bids.amount' },
          uniqueBidders: { $addToSet: '$bids.bidder' },
          uniqueAuctions: { $addToSet: '$_id' }
        }
      },
      {
        $addFields: {
          uniqueBiddersCount: { $size: '$uniqueBidders' },
          uniqueAuctionsCount: { $size: '$uniqueAuctions' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        leaderboard: formattedLeaderboard,
        period,
        totalStats: totalStats[0] || {
          totalBids: 0,
          totalAmount: 0,
          uniqueBiddersCount: 0,
          uniqueAuctionsCount: 0
        },
        lastUpdated: new Date()
      }
    });
  } catch (error) {
    console.error('Get leaderboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching leaderboard'
    });
  }
});


// @route   POST /api/auctions
// @desc    Create new auction
// @access  Private (Government/Company only)
router.post('/',
  authenticate,
  requireApproved,
  requireRole('government', 'company'),
  upload.array('images', 10),
  [
    body('title').trim().isLength({ min: 3, max: 200 }),
    body('description').trim().isLength({ min: 10, max: 2000 }),
    body('category').isIn(['electronics', 'vehicles', 'real_estate', 'art_collectibles', 'machinery', 'furniture', 'jewelry', 'books_media', 'clothing', 'sports', 'other']),
    body('startingPrice').isFloat({ min: 1 }),
    body('condition').isIn(['new', 'like_new', 'excellent', 'good', 'fair', 'poor']),
    body('startDate').isISO8601(),
    body('endDate').isISO8601()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const {
        title,
        description,
        category,
        startingPrice,
        reservePrice,
        buyNowPrice,
        bidIncrement,
        startDate,
        endDate,
        condition,
        location,
        shipping,
        auctionType
      } = req.body;

      // Validate dates
      const start = new Date(startDate);
      const end = new Date(endDate);
      const now = new Date();

      // Allow start dates within 2 minutes of now (for testing purposes)
      const twoMinutesAgo = new Date(now.getTime() - 2 * 60 * 1000);
      if (start < twoMinutesAgo) {
        return res.status(400).json({
          success: false,
          message: 'Start date cannot be more than 2 minutes in the past'
        });
      }

      if (end <= start) {
        return res.status(400).json({
          success: false,
          message: 'End date must be after start date'
        });
      }

      // Upload images to cloudinary
      const images = [];
      if (req.files && req.files.length > 0) {
        for (let i = 0; i < req.files.length; i++) {
          const file = req.files[i];
          const uploadResult = await uploadToCloudinary(file.buffer, {
            folder: `auction-images/${req.user._id}`,
            resource_type: 'image'
          });
          
          images.push({
            url: uploadResult.secure_url,
            caption: `Image ${i + 1}`,
            isMain: i === 0
          });
        }
      }

      // Create auction
      const auction = new Auction({
        title,
        description,
        category,
        seller: req.user._id,
        auctioneer: req.user._id,
        startingPrice: parseFloat(startingPrice),
        currentPrice: parseFloat(startingPrice),
        reservePrice: reservePrice ? parseFloat(reservePrice) : 0,
        buyNowPrice: buyNowPrice ? parseFloat(buyNowPrice) : null,
        bidIncrement: bidIncrement ? parseFloat(bidIncrement) : 10,
        startDate: start,
        endDate: end,
        condition,
        location: location ? JSON.parse(location) : {},
        shipping: shipping ? JSON.parse(shipping) : { available: false },
        auctionType: auctionType || 'standard',
        images,
        status: 'active'
      });

      await auction.save();

      const populatedAuction = await Auction.findById(auction._id)
        .populate('seller', 'profile.fullName profile.companyName');

      res.status(201).json({
        success: true,
        message: 'Auction created successfully and submitted for approval',
        data: { auction: populatedAuction }
      });
    } catch (error) {
      console.error('Create auction error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while creating auction'
      });
    }
  }
);

// @route   POST /api/auctions/:id/bid
// @desc    Place a bid on auction
// @access  Private (Individual/Company)
router.post('/:id/bid',
  authenticate,
  requireApproved,
  requireRole('individual', 'company'),
  [
    body('bidAmount').isFloat({ min: 1 })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { bidAmount: amount } = req.body;
      const auction = await Auction.findById(req.params.id);

      if (!auction) {
        return res.status(404).json({
          success: false,
          message: 'Auction not found'
        });
      }

      try {
        console.log('Attempting to place bid:', {
          auctionId: auction._id,
          userId: req.user._id,
          amount: parseFloat(amount),
          auctionStatus: auction.status,
          auctionStartDate: auction.startDate,
          auctionEndDate: auction.endDate,
          currentTime: new Date()
        });
        const newBid = auction.placeBid(req.user._id, parseFloat(amount));
        await auction.save();

        // Emit real-time update
        const io = req.app.get('io');
        io.to(`auction-${auction._id}`).emit('new-bid', {
          auctionId: auction._id,
          bid: newBid,
          currentPrice: auction.currentPrice,
          totalBids: auction.bids.length
        });

        res.json({
          success: true,
          message: 'Bid placed successfully',
          data: {
            bid: newBid,
            auction: {
              id: auction._id,
              currentPrice: auction.currentPrice,
              totalBids: auction.bids.length,
              bids: auction.bids
            }
          }
        });
      } catch (bidError) {
        console.log('Bid placement error:', bidError.message);
        return res.status(400).json({
          success: false,
          message: bidError.message
        });
      }
    } catch (error) {
      console.error('Place bid error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while placing bid'
      });
    }
  }
);

// @route   POST /api/auctions/:id/watch
// @desc    Add auction to watchlist
// @access  Private
router.post('/:id/watch',
  authenticate,
  requireApproved,
  async (req, res) => {
    try {
      const auction = await Auction.findById(req.params.id);

      if (!auction) {
        return res.status(404).json({
          success: false,
          message: 'Auction not found'
        });
      }

      const isWatching = auction.watchers.includes(req.user._id);

      if (isWatching) {
        auction.watchers.pull(req.user._id);
      } else {
        auction.watchers.push(req.user._id);
      }

      await auction.save();

      res.json({
        success: true,
        message: isWatching ? 'Auction removed from watchlist' : 'Auction added to watchlist',
        data: {
          isWatching: !isWatching,
          watchersCount: auction.watchers.length
        }
      });
    } catch (error) {
      console.error('Watch auction error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while updating watchlist'
      });
    }
  }
);

// @route   GET /api/auctions/my-auctions
// @desc    Get user's auctions
// @access  Private
router.get('/my-auctions',
  authenticate,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;
      const { status } = req.query;

      const filter = { seller: req.user._id };
      if (status) filter.status = status;

      const auctions = await Auction.find(filter)
        .populate('auctioneer', 'profile.fullName profile.companyName')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Auction.countDocuments(filter);

      res.json({
        success: true,
        data: {
          auctions,
          pagination: {
            current: page,
            pages: Math.ceil(total / limit),
            total
          }
        }
      });
    } catch (error) {
      console.error('Get my auctions error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching user auctions'
      });
    }
  }
);

// @route   GET /api/auctions/my-bids
// @desc    Get user's bids
// @access  Private
router.get('/my-bids',
  authenticate,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      const auctions = await Auction.find({
        'bids.bidder': req.user._id
      })
      .populate('seller', 'profile.fullName profile.companyName')
      .sort({ 'bids.timestamp': -1 })
      .skip(skip)
      .limit(limit);

      // Filter to show only user's bids and auction info
      const userBids = auctions.map(auction => {
        const userBid = auction.bids
          .filter(bid => bid.bidder.toString() === req.user._id.toString())
          .sort((a, b) => b.timestamp - a.timestamp)[0];

        return {
          auction: {
            id: auction._id,
            title: auction.title,
            currentPrice: auction.currentPrice,
            status: auction.status,
            endDate: auction.endDate,
            images: auction.images
          },
          bid: userBid,
          isWinning: userBid.isWinning
        };
      });

      const total = await Auction.countDocuments({
        'bids.bidder': req.user._id
      });

      res.json({
        success: true,
        data: {
          bids: userBids,
          pagination: {
            current: page,
            pages: Math.ceil(total / limit),
            total
          }
        }
      });
    } catch (error) {
      console.error('Get my bids error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching user bids'
      });
    }
  }
);

// @route   GET /api/auctions/watchlist
// @desc    Get user's watchlist
// @access  Private
router.get('/watchlist',
  authenticate,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      const auctions = await Auction.find({
        watchers: req.user._id
      })
      .populate('seller', 'profile.fullName profile.companyName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

      const total = await Auction.countDocuments({
        watchers: req.user._id
      });

      res.json({
        success: true,
        data: {
          auctions,
          pagination: {
            current: page,
            pages: Math.ceil(total / limit),
            total
          }
        }
      });
    } catch (error) {
      console.error('Get watchlist error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching watchlist'
      });
    }
  }
);
// @route   PUT /api/auctions/:id
// @desc    Update auction (before approval)
// @access  Private (Auction owner)
router.put('/:id',
  authenticate,
  requireApproved,
  async (req, res) => {
    try {
      const auction = await Auction.findById(req.params.id);

      if (!auction) {
        return res.status(404).json({
          success: false,
          message: 'Auction not found'
        });
      }

      if (auction.seller.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to update this auction'
        });
      }

      if (auction.status !== 'draft' && auction.status !== 'pending_approval' && auction.bids.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Cannot update auction with existing bids'
        });
      }

      // Update allowed fields
      const allowedUpdates = ['title', 'description', 'startingPrice', 'reservePrice', 'buyNowPrice', 'bidIncrement', 'condition', 'location', 'shipping'];
      allowedUpdates.forEach(field => {
        if (req.body[field] !== undefined) {
          auction[field] = req.body[field];
        }
      });

      await auction.save();

      res.json({
        success: true,
        message: 'Auction updated successfully',
        data: { auction }
      });
    } catch (error) {
      console.error('Update auction error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while updating auction'
      });
    }
  }
);

// @route   DELETE /api/auctions/:id
// @desc    Cancel/Delete auction
// @access  Private (Auction owner or Admin)
router.delete('/:id',
  authenticate,
  requireApproved,
  async (req, res) => {
    try {
      const auction = await Auction.findById(req.params.id);

      if (!auction) {
        return res.status(404).json({
          success: false,
          message: 'Auction not found'
        });
      }

      // Allow admin to delete any auction
      const isOwner = auction.seller.toString() === req.user._id.toString();
      const isAdmin = req.user.role === 'admin';
      
      if (!isOwner && !isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to delete this auction'
        });
      }

      // Only check for bids if user is not admin
      if (!isAdmin && auction.status === 'active' && auction.bids.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete auction with existing bids'
        });
      }

      await Auction.findByIdAndDelete(req.params.id);

      res.json({
        success: true,
        message: 'Auction deleted successfully'
      });
    } catch (error) {
      console.error('Delete auction error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while deleting auction'
      });
    }
  }
);

// @route   GET /api/auctions/:id
// @desc    Get auction details
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const auction = await Auction.findById(req.params.id)
      .populate('seller', 'profile.fullName profile.companyName')
      .populate('auctioneer', 'profile.fullName profile.companyName')
      .populate('bids.bidder', 'profile.fullName');

    if (!auction) {
      return res.status(404).json({
        success: false,
        message: 'Auction not found'
      });
    }

    // Update view count
    auction.views += 1;
    await auction.save();

    res.json({
      success: true,
      data: {
        auction: {
          ...auction.toObject(),
          auctioneer: {
            fullName: auction.auctioneer.profile.fullName || auction.auctioneer.profile.companyName
          }
        }
      }
    });
  } catch (error) {
    console.error('Get auction details error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching auction details'
    });
  }
});

module.exports = router;
