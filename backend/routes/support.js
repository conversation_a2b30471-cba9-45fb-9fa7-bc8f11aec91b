const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticate, requireRole } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// @route   GET /api/support/tickets
// @desc    Get user's support tickets
// @access  Private
router.get('/tickets', async (req, res) => {
  try {
    const userId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status;
    
    // Build filter
    const filter = { userId };
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    // For now, return empty array since we don't have a Ticket model yet
    // TODO: Implement proper ticket model and database queries
    const tickets = [];
    const total = 0;
    
    res.json({
      success: true,
      data: {
        tickets,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get support tickets error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching support tickets'
    });
  }
});

// @route   POST /api/support/tickets
// @desc    Create new support ticket
// @access  Private
router.post('/tickets', [
  body('category').isIn(['technical', 'account', 'billing', 'general']).withMessage('Invalid category'),
  body('subject').trim().isLength({ min: 5, max: 200 }).withMessage('Subject must be between 5 and 200 characters'),
  body('description').trim().isLength({ min: 10, max: 2000 }).withMessage('Description must be between 10 and 2000 characters'),
  body('priority').optional().isIn(['low', 'medium', 'high']).withMessage('Invalid priority')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const { category, subject, description, priority = 'medium' } = req.body;
    const userId = req.user._id;
    
    // TODO: Implement proper ticket creation with database
    // For now, return a mock response
    const newTicket = {
      id: `TICKET-${Date.now()}`,
      category,
      subject,
      description,
      priority,
      status: 'open',
      userId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    res.status(201).json({
      success: true,
      message: 'Support ticket created successfully',
      data: { ticket: newTicket }
    });
  } catch (error) {
    console.error('Create support ticket error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while creating support ticket'
    });
  }
});

// @route   GET /api/support/tickets/:id
// @desc    Get support ticket details
// @access  Private
router.get('/tickets/:id', async (req, res) => {
  try {
    const ticketId = req.params.id;
    const userId = req.user._id;
    
    // TODO: Implement proper ticket retrieval
    // For now, return empty response
    res.status(404).json({
      success: false,
      message: 'Ticket not found'
    });
  } catch (error) {
    console.error('Get support ticket error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching support ticket'
    });
  }
});

// @route   PUT /api/support/tickets/:id
// @desc    Update support ticket
// @access  Private
router.put('/tickets/:id', [
  body('subject').optional().trim().isLength({ min: 5, max: 200 }),
  body('description').optional().trim().isLength({ min: 10, max: 2000 }),
  body('priority').optional().isIn(['low', 'medium', 'high'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    
    const ticketId = req.params.id;
    const userId = req.user._id;
    const { subject, description, priority } = req.body;

    // Find the ticket
    const ticket = await Ticket.findById(ticketId);

    if (!ticket) {
      return res.status(404).json({
        success: false,
        message: 'Ticket not found'
      });
    }

    // Check if user owns the ticket or is admin
    if (ticket.user.toString() !== userId.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only update your own tickets.'
      });
    }

    // Update ticket fields
    if (subject) ticket.subject = subject;
    if (description) ticket.description = description;
    if (priority) ticket.priority = priority;

    ticket.updatedAt = new Date();

    await ticket.save();

    res.json({
      success: true,
      message: 'Ticket updated successfully',
      data: { ticket }
    });
  } catch (error) {
    console.error('Update support ticket error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating support ticket'
    });
  }
});

// @route   DELETE /api/support/tickets/:id
// @desc    Delete support ticket
// @access  Private
router.delete('/tickets/:id', async (req, res) => {
  try {
    const ticketId = req.params.id;
    const userId = req.user._id;
    
    // TODO: Implement proper ticket deletion
    res.status(404).json({
      success: false,
      message: 'Ticket not found'
    });
  } catch (error) {
    console.error('Delete support ticket error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting support ticket'
    });
  }
});

// Admin routes for managing support tickets
// @route   GET /api/support/admin/tickets
// @desc    Get all support tickets (Admin only)
// @access  Private (Admin)
router.get('/admin/tickets', requireRole('admin', 'super_admin'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    const status = req.query.status;
    const priority = req.query.priority;
    
    // Build filter
    const filter = {};
    if (status && status !== 'all') {
      filter.status = status;
    }
    if (priority && priority !== 'all') {
      filter.priority = priority;
    }
    
    // TODO: Implement proper admin ticket retrieval
    const tickets = [];
    const total = 0;
    
    res.json({
      success: true,
      data: {
        tickets,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Admin get support tickets error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching support tickets'
    });
  }
});

module.exports = router;
