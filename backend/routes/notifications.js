const express = require('express');
const { authenticate, requireApproved } = require('../middleware/auth');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// @route   GET /api/notifications
// @desc    Get notifications (general endpoint that routes based on user role)
// @access  Private
router.get('/', requireApproved, async (req, res) => {
  try {
    const userId = req.user._id;
    const userRole = req.user.role;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Generate role-specific sample notifications
    let sampleNotifications = [];
    
    if (userRole === 'company') {
      sampleNotifications = [
        {
          _id: '1',
          type: 'new_bid',
          title: 'مزايدة جديدة',
          message: 'تم تقديم مزايدة جديدة على مزاد السيارة BMW',
          createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          read: false
        },
        {
          _id: '2',
          type: 'auction_ending',
          title: 'انتهاء مزاد قريباً',
          message: 'مزاد الأثاث المكتبي ينتهي خلال ساعة واحدة',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          read: false
        },
        {
          _id: '3',
          type: 'payment_received',
          title: 'تم استلام الدفعة',
          message: 'تم استلام دفعة مزاد الإلكترونيات بنجاح',
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          read: true
        }
      ];
    } else if (userRole === 'government') {
      sampleNotifications = [
        {
          _id: '1',
          type: 'new_application',
          title: 'طلب جديد',
          message: 'تم تقديم طلب جديد على مناقصة بناء المستشفى',
          createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          read: false
        },
        {
          _id: '2',
          type: 'tender_deadline',
          title: 'اقتراب موعد الإغلاق',
          message: 'مناقصة تطوير البرمجيات تنتهي خلال 24 ساعة',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          read: false
        },
        {
          _id: '3',
          type: 'application_approved',
          title: 'تم الموافقة على طلب',
          message: 'تم الموافقة على طلب شركة البناء العربية',
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          read: true
        }
      ];
    } else {
      // Individual user notifications
      sampleNotifications = [
        {
          _id: '1',
          type: 'auction_won',
          title: 'مبروك! فزت بالمزاد',
          message: 'لقد فزت بمزاد جهاز كمبيوتر محمول Dell',
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          read: false
        },
        {
          _id: '2',
          type: 'auction_ending',
          title: 'تذكير: المزاد ينتهي قريباً',
          message: 'مزاد السيارة BMW ينتهي خلال ساعة واحدة',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          read: false
        },
        {
          _id: '3',
          type: 'new_auction',
          title: 'مزاد جديد قد يهمك',
          message: 'تم إضافة مزاد جديد في فئة الإلكترونيات',
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          read: true
        },
        {
          _id: '4',
          type: 'bid_outbid',
          title: 'تم تجاوز مزايدتك',
          message: 'تم تجاوز مزايدتك في مزاد الأثاث المكتبي',
          createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
          read: true
        }
      ];
    }

    const notifications = sampleNotifications.slice(skip, skip + limit);
    const total = sampleNotifications.length;

    res.json({
      success: true,
      data: {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('General notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching notifications'
    });
  }
});

// @route   POST /api/notifications/:id/read
// @desc    Mark notification as read
// @access  Private
router.post('/:id/read', requireApproved, async (req, res) => {
  try {
    // In a real app, you'd update the notification in the database
    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  } catch (error) {
    console.error('Mark notification read error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while marking notification as read'
    });
  }
});

// @route   POST /api/notifications/mark-all-read
// @desc    Mark all notifications as read
// @access  Private
router.post('/mark-all-read', requireApproved, async (req, res) => {
  try {
    // In a real app, you'd update all notifications for the user in the database
    res.json({
      success: true,
      message: 'All notifications marked as read'
    });
  } catch (error) {
    console.error('Mark all notifications read error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while marking all notifications as read'
    });
  }
});

module.exports = router;
