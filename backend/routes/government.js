const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Tender = require('../models/Tender');
const { authenticate, requireRole, authorize } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// @route   GET /api/government/dashboard
// @desc    Get government dashboard overview
// @access  Private (Government users)
router.get('/dashboard', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;

    // Get basic dashboard data
    const [
      totalTenders,
      activeTenders,
      completedTenders,
      totalApplications
    ] = await Promise.all([
      Tender.countDocuments({ organizer: governmentId }),
      Tender.countDocuments({ organizer: governmentId, status: 'open' }),
      Tender.countDocuments({ organizer: governmentId, status: 'closed' }),
      Tender.aggregate([
        { $match: { organizer: governmentId } },
        { $project: {
          applicationCount: {
            $cond: {
              if: { $isArray: '$proposals' },
              then: { $size: '$proposals' },
              else: 0
            }
          }
        }},
        { $group: { _id: null, total: { $sum: '$applicationCount' } } }
      ])
    ]);

    const totalApps = totalApplications.length > 0 ? totalApplications[0].total : 0;

    res.json({
      success: true,
      data: {
        overview: {
          totalTenders,
          activeTenders,
          completedTenders,
          totalApplications: totalApps
        }
      }
    });
  } catch (error) {
    console.error('Government dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching government dashboard'
    });
  }
});

// @route   GET /api/government/dashboard/stats
// @desc    Get government dashboard statistics
// @access  Private (Government users)
router.get('/dashboard/stats', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;
    
    // Get government's tenders
    const governmentTenders = await Tender.find({ organizer: governmentId });
    
    // Calculate stats
    const activeTenders = governmentTenders.filter(tender => tender.status === 'active').length;
    const completedTenders = governmentTenders.filter(tender => tender.status === 'completed').length;
    
    // Calculate total applications across all tenders
    const totalApplications = governmentTenders.reduce((total, tender) => {
      return total + (tender.applications ? tender.applications.length : 0);
    }, 0);
    
    // Calculate approved applications
    const approvedApplications = governmentTenders.reduce((total, tender) => {
      if (tender.applications) {
        return total + tender.applications.filter(app => app.status === 'approved').length;
      }
      return total;
    }, 0);
    
    // Calculate total budget
    const totalBudget = governmentTenders.reduce((total, tender) => total + tender.budget, 0);
    
    // Calculate pending reviews
    const pendingReviews = governmentTenders.reduce((total, tender) => {
      if (tender.applications) {
        return total + tender.applications.filter(app => app.status === 'review').length;
      }
      return total;
    }, 0);
    
    // Calculate total projects (completed tenders)
    const totalProjects = completedTenders;
    
    // Calculate average tender value
    const averageTenderValue = governmentTenders.length > 0 ? totalBudget / governmentTenders.length : 0;
    
    // Calculate success rate (completed vs total)
    const successRate = governmentTenders.length > 0 ? Math.round((completedTenders / governmentTenders.length) * 100) : 0;
    
    res.json({
      success: true,
      data: {
        activeTenders,
        completedTenders,
        totalApplications,
        approvedApplications,
        totalBudget,
        pendingReviews,
        totalProjects,
        averageTenderValue,
        successRate
      }
    });
  } catch (error) {
    console.error('Government dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching government dashboard stats'
    });
  }
});

// @route   GET /api/government/tenders
// @desc    Get government's tenders with pagination
// @access  Private (Government users)
router.get('/tenders', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const sort = req.query.sort || '-createdAt';
    
    const tenders = await Tender.find({ organizer: governmentId })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('proposals.proposer', 'profile.companyName profile.fullName email');
    
    const total = await Tender.countDocuments({ organizer: governmentId });
    
    res.json({
      success: true,
      data: {
        tenders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Government tenders error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching government tenders'
    });
  }
});

// @route   GET /api/government/applications/recent
// @desc    Get recent applications on government's tenders
// @access  Private (Government users)
router.get('/applications/recent', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;
    const limit = parseInt(req.query.limit) || 10;
    
    // Get government's tenders with recent applications
    const tenders = await Tender.find({
      organizer: governmentId,
      'proposals.0': { $exists: true } // Only tenders with proposals
    })
    .populate('proposals.proposer', 'profile.companyName profile.fullName email')
    .sort({ 'proposals.submittedAt': -1 })
    .limit(limit);
    
    // Extract recent applications with tender context
    let recentApplications = [];
    tenders.forEach(tender => {
      if (tender.applications) {
        tender.applications.forEach(application => {
          recentApplications.push({
            _id: application._id,
            applicant: application.applicant,
            submittedAt: application.submittedAt,
            status: application.status,
            tender: {
              _id: tender._id,
              title: tender.title
            }
          });
        });
      }
    });
    
    // Sort by submission date and limit
    recentApplications.sort((a, b) => new Date(b.submittedAt) - new Date(a.submittedAt));
    recentApplications = recentApplications.slice(0, limit);
    
    res.json({
      success: true,
      data: {
        applications: recentApplications
      }
    });
  } catch (error) {
    console.error('Government recent applications error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching recent applications'
    });
  }
});

// @route   GET /api/government/notifications
// @desc    Get government notifications
// @access  Private (Government users)
router.get('/notifications', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    
    // For now, return sample notifications
    // In a real app, you'd have a Notification model
    const sampleNotifications = [
      {
        _id: '1',
        type: 'new_application',
        title: 'طلب جديد',
        message: 'تم تقديم طلب جديد على مناقصة بناء المستشفى',
        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        read: false
      },
      {
        _id: '2',
        type: 'tender_deadline',
        title: 'اقتراب موعد الإغلاق',
        message: 'مناقصة تطوير البرمجيات تنتهي خلال 24 ساعة',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        read: false
      },
      {
        _id: '3',
        type: 'application_approved',
        title: 'تم الموافقة على طلب',
        message: 'تم الموافقة على طلب شركة البناء العربية',
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        read: true
      }
    ];
    
    const notifications = sampleNotifications.slice((page - 1) * limit, page * limit);
    const total = sampleNotifications.length;
    
    res.json({
      success: true,
      data: {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Government notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching notifications'
    });
  }
});

// @route   GET /api/government/analytics/charts
// @desc    Get government analytics data for charts
// @access  Private (Government users)
router.get('/analytics/charts', requireRole('government'), async (req, res) => {
  try {
    const governmentId = req.user._id;

    // Get current date and 6 months ago
    const now = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(now.getMonth() - 6);

    // Get government's tenders in the last 6 months
    const governmentTenders = await Tender.find({
      organizer: governmentId,
      createdAt: { $gte: sixMonthsAgo }
    });

    // Generate budget over time data (monthly)
    const budgetOverTime = [];
    const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date();
      monthDate.setMonth(now.getMonth() - i);
      const monthStart = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1);
      const monthEnd = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0);

      const monthTenders = governmentTenders.filter(tender => {
        const tenderDate = new Date(tender.createdAt);
        return tenderDate >= monthStart && tenderDate <= monthEnd;
      });

      const budget = monthTenders.reduce((total, tender) => total + tender.budget, 0);
      const tenders = monthTenders.length;

      budgetOverTime.push({
        month: monthNames[monthDate.getMonth()],
        budget,
        tenders
      });
    }

    // Generate tender performance data
    const statusCounts = {
      completed: governmentTenders.filter(t => t.status === 'completed').length,
      active: governmentTenders.filter(t => t.status === 'active').length,
      review: governmentTenders.filter(t => t.status === 'review').length,
      cancelled: governmentTenders.filter(t => t.status === 'cancelled').length
    };

    const tenderPerformance = [
      { status: 'مكتملة', count: statusCounts.completed, color: '#10B981' },
      { status: 'نشطة', count: statusCounts.active, color: '#059669' },
      { status: 'قيد المراجعة', count: statusCounts.review, color: '#F59E0B' },
      { status: 'ملغية', count: statusCounts.cancelled, color: '#EF4444' }
    ].filter(item => item.count > 0);

    // Generate category distribution
    const categoryDistribution = {};
    governmentTenders.forEach(tender => {
      const category = tender.category || 'أخرى';
      categoryDistribution[category] = (categoryDistribution[category] || 0) + 1;
    });

    const categoryColors = ['#059669', '#7C3AED', '#10B981', '#F59E0B', '#EF4444', '#06B6D4'];
    const categoryDistributionArray = Object.entries(categoryDistribution).map(([name, value], index) => ({
      name: getCategoryNameInArabic(name),
      value: Math.round((value / governmentTenders.length) * 100),
      color: categoryColors[index % categoryColors.length]
    }));

    // Generate application trends data (weekly)
    const applicationTrends = [];
    const dayNames = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];

    for (let i = 0; i < 7; i++) {
      const dayDate = new Date();
      dayDate.setDate(dayDate.getDate() - (6 - i));
      const dayStart = new Date(dayDate.getFullYear(), dayDate.getMonth(), dayDate.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

      let applications = 0;
      let approved = 0;

      governmentTenders.forEach(tender => {
        if (tender.applications) {
          tender.applications.forEach(application => {
            const appDate = new Date(application.submittedAt);
            if (appDate >= dayStart && appDate < dayEnd) {
              applications++;
              if (application.status === 'approved') {
                approved++;
              }
            }
          });
        }
      });

      applicationTrends.push({
        day: dayNames[dayDate.getDay()],
        applications,
        approved
      });
    }

    res.json({
      success: true,
      data: {
        budgetOverTime,
        tenderPerformance,
        categoryDistribution: categoryDistributionArray,
        applicationTrends
      }
    });
  } catch (error) {
    console.error('Government analytics charts error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching analytics data'
    });
  }
});

// Helper function to translate category names to Arabic
function getCategoryNameInArabic(category) {
  const translations = {
    'construction': 'إنشاءات',
    'it_technology': 'تقنية المعلومات',
    'consulting': 'استشارات',
    'healthcare': 'رعاية صحية',
    'education': 'تعليم',
    'transportation': 'نقل ومواصلات',
    'real_estate': 'عقارات',
    'manufacturing': 'تصنيع',
    'energy': 'طاقة',
    'other': 'أخرى'
  };
  return translations[category] || category;
}

module.exports = router;
