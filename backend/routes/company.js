const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Auction = require('../models/Auction');
const Tender = require('../models/Tender');
const { authenticate, requireRole, authorize } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// @route   GET /api/company/dashboard/stats
// @desc    Get company dashboard statistics
// @access  Private (Company users)
router.get('/dashboard/stats', requireRole('company'), async (req, res) => {
  try {
    const companyId = req.user._id;

    // Get company's auctions
    const companyAuctions = await Auction.find({ seller: companyId });

    // Calculate stats
    const activeAuctions = companyAuctions.filter(auction => auction.status === 'active').length;
    const completedAuctions = companyAuctions.filter(auction => auction.status === 'ended').length;
    const totalRevenue = companyAuctions
      .filter(auction => auction.status === 'ended' && auction.winner)
      .reduce((sum, auction) => sum + auction.currentPrice, 0);

    // Get total bids on company auctions
    const totalBids = companyAuctions.reduce((sum, auction) => sum + auction.bids.length, 0);

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentAuctions = companyAuctions.filter(auction =>
      new Date(auction.createdAt) >= thirtyDaysAgo
    ).length;

    res.json({
      success: true,
      data: {
        activeAuctions,
        completedAuctions,
        totalRevenue,
        totalBids,
        recentAuctions,
        totalAuctions: companyAuctions.length
      }
    });
  } catch (error) {
    console.error('Company dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching dashboard stats'
    });
  }
});

// @route   GET /api/company/dashboard/stats
// @desc    Get company dashboard statistics
// @access  Private (Company users)
router.get('/dashboard/stats', requireRole('company'), async (req, res) => {
  try {
    const companyId = req.user._id;
    
    // Get company's auctions
    const companyAuctions = await Auction.find({ organizer: companyId });
    
    // Calculate stats
    const myAuctions = companyAuctions.length;
    const completedAuctions = companyAuctions.filter(auction => auction.status === 'completed').length;
    const activeBids = companyAuctions.reduce((total, auction) => total + auction.bids.length, 0);
    
    // Calculate total revenue from completed auctions
    const totalRevenue = companyAuctions
      .filter(auction => auction.status === 'completed')
      .reduce((total, auction) => total + (auction.finalPrice || auction.currentBid), 0);
    
    // Get unique bidders count
    const allBidders = new Set();
    companyAuctions.forEach(auction => {
      auction.bids.forEach(bid => {
        allBidders.add(bid.bidder.toString());
      });
    });
    const totalBidders = allBidders.size;
    
    // Calculate average auction value
    const averageAuctionValue = completedAuctions > 0 ? totalRevenue / completedAuctions : 0;
    
    // Calculate success rate (completed vs total)
    const successRate = myAuctions > 0 ? Math.round((completedAuctions / myAuctions) * 100) : 0;
    
    // Get tenders count (if company also participates in tenders)
    const myTenders = await Tender.countDocuments({ organizer: companyId });
    
    // Pending payments (auctions completed but payment pending)
    const pendingPayments = companyAuctions.filter(auction => 
      auction.status === 'completed' && !auction.paymentCompleted
    ).length;
    
    res.json({
      success: true,
      data: {
        myAuctions,
        myTenders,
        activeBids,
        completedAuctions,
        totalRevenue,
        pendingPayments,
        totalBidders,
        averageAuctionValue,
        successRate
      }
    });
  } catch (error) {
    console.error('Company dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching company dashboard stats'
    });
  }
});

// @route   GET /api/company/auctions
// @desc    Get company's auctions with pagination
// @access  Private (Company users)
router.get('/auctions', requireRole('company'), async (req, res) => {
  try {
    const companyId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const sort = req.query.sort || '-createdAt';
    
    const auctions = await Auction.find({ organizer: companyId })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('winner', 'profile.fullName email');
    
    const total = await Auction.countDocuments({ organizer: companyId });
    
    res.json({
      success: true,
      data: {
        auctions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Company auctions error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching company auctions'
    });
  }
});

// @route   GET /api/company/bids
// @desc    Get all bids on company's auctions
// @access  Private (Company users)
router.get('/bids', requireRole('company'), async (req, res) => {
  try {
    const companyId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get company's auctions with bids
    const auctions = await Auction.find({
      seller: companyId,
      'bids.0': { $exists: true } // Only auctions with bids
    })
    .populate('bids.bidder', 'profile.fullName profile.companyName email')
    .sort({ 'bids.timestamp': -1 });

    // Extract all bids with auction context
    let allBids = [];
    auctions.forEach(auction => {
      auction.bids.forEach(bid => {
        allBids.push({
          _id: bid._id,
          auctionTitle: auction.title,
          auctionId: auction._id,
          bidderName: bid.bidder.profile.fullName || bid.bidder.profile.companyName,
          bidderEmail: bid.bidder.email,
          bidAmount: bid.amount,
          bidDate: bid.timestamp,
          status: auction.status === 'active' ? (bid.amount === auction.currentPrice ? 'leading' : 'outbid') : 'completed',
          isLeading: bid.amount === auction.currentPrice,
          auction: {
            _id: auction._id,
            title: auction.title,
            status: auction.status,
            currentPrice: auction.currentPrice,
            endDate: auction.endDate
          }
        });
      });
    });

    // Sort by timestamp and paginate
    allBids.sort((a, b) => new Date(b.bidDate) - new Date(a.bidDate));
    const paginatedBids = allBids.slice(skip, skip + limit);
    const total = allBids.length;

    res.json({
      success: true,
      data: {
        bids: paginatedBids,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Company bids error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching company bids'
    });
  }
});

// @route   GET /api/company/bids/recent
// @desc    Get recent bids on company's auctions
// @access  Private (Company users)
router.get('/bids/recent', requireRole('company'), async (req, res) => {
  try {
    const companyId = req.user._id;
    const limit = parseInt(req.query.limit) || 10;

    // Get company's auctions with recent bids
    const auctions = await Auction.find({
      seller: companyId,
      'bids.0': { $exists: true } // Only auctions with bids
    })
    .populate('bids.bidder', 'profile.fullName email')
    .sort({ 'bids.timestamp': -1 })
    .limit(limit);
    
    // Extract recent bids with auction context
    let recentBids = [];
    auctions.forEach(auction => {
      auction.bids.forEach(bid => {
        recentBids.push({
          _id: bid._id,
          bidder: bid.bidder,
          amount: bid.amount,
          timestamp: bid.timestamp,
          auction: {
            _id: auction._id,
            title: auction.title
          }
        });
      });
    });
    
    // Sort by timestamp and limit
    recentBids.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    recentBids = recentBids.slice(0, limit);
    
    res.json({
      success: true,
      data: {
        bids: recentBids
      }
    });
  } catch (error) {
    console.error('Company recent bids error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching recent bids'
    });
  }
});

// @route   GET /api/company/notifications
// @desc    Get company notifications
// @access  Private (Company users)
router.get('/notifications', requireRole('company'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    
    // For now, return sample notifications
    // In a real app, you'd have a Notification model
    const sampleNotifications = [
      {
        _id: '1',
        type: 'new_bid',
        title: 'مزايدة جديدة',
        message: 'تم تقديم مزايدة جديدة على مزاد السيارة BMW',
        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        read: false
      },
      {
        _id: '2',
        type: 'auction_ending',
        title: 'انتهاء مزاد قريباً',
        message: 'مزاد الأثاث المكتبي ينتهي خلال ساعة واحدة',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        read: false
      },
      {
        _id: '3',
        type: 'payment_received',
        title: 'تم استلام الدفعة',
        message: 'تم استلام دفعة مزاد الإلكترونيات بنجاح',
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        read: true
      }
    ];
    
    const notifications = sampleNotifications.slice((page - 1) * limit, page * limit);
    const total = sampleNotifications.length;
    
    res.json({
      success: true,
      data: {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Company notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching notifications'
    });
  }
});

// @route   GET /api/company/analytics/charts
// @desc    Get company analytics data for charts
// @access  Private (Company users)
router.get('/analytics/charts', requireRole('company'), async (req, res) => {
  try {
    const companyId = req.user._id;

    // Get current date and 6 months ago
    const now = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(now.getMonth() - 6);

    // Get company's auctions in the last 6 months
    const companyAuctions = await Auction.find({
      organizer: companyId,
      createdAt: { $gte: sixMonthsAgo }
    });

    // Generate revenue over time data (monthly)
    const revenueOverTime = [];
    const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date();
      monthDate.setMonth(now.getMonth() - i);
      const monthStart = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1);
      const monthEnd = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0);

      const monthAuctions = companyAuctions.filter(auction => {
        const auctionDate = new Date(auction.createdAt);
        return auctionDate >= monthStart && auctionDate <= monthEnd;
      });

      const revenue = monthAuctions
        .filter(auction => auction.status === 'completed')
        .reduce((total, auction) => total + (auction.finalPrice || auction.currentBid), 0);

      const auctions = monthAuctions.length;

      revenueOverTime.push({
        month: monthNames[monthDate.getMonth()],
        revenue,
        auctions
      });
    }

    // Generate auction performance data
    const statusCounts = {
      completed: companyAuctions.filter(a => a.status === 'completed').length,
      active: companyAuctions.filter(a => a.status === 'active').length,
      ended: companyAuctions.filter(a => a.status === 'ended').length,
      cancelled: companyAuctions.filter(a => a.status === 'cancelled').length
    };

    const auctionPerformance = [
      { status: 'مكتملة', count: statusCounts.completed, color: '#10B981' },
      { status: 'نشطة', count: statusCounts.active, color: '#3B82F6' },
      { status: 'منتهية', count: statusCounts.ended, color: '#F59E0B' },
      { status: 'ملغية', count: statusCounts.cancelled, color: '#EF4444' }
    ].filter(item => item.count > 0);

    // Generate category distribution
    const categoryDistribution = {};
    companyAuctions.forEach(auction => {
      const category = auction.category || 'أخرى';
      categoryDistribution[category] = (categoryDistribution[category] || 0) + 1;
    });

    const categoryColors = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444', '#06B6D4'];
    const categoryDistributionArray = Object.entries(categoryDistribution).map(([name, value], index) => ({
      name: getCategoryNameInArabic(name),
      value: Math.round((value / companyAuctions.length) * 100),
      color: categoryColors[index % categoryColors.length]
    }));

    // Generate bidder activity data (weekly)
    const bidderActivity = [];
    const dayNames = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];

    for (let i = 0; i < 7; i++) {
      const dayDate = new Date();
      dayDate.setDate(dayDate.getDate() - (6 - i));
      const dayStart = new Date(dayDate.getFullYear(), dayDate.getMonth(), dayDate.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

      const dayAuctions = companyAuctions.filter(auction => {
        return auction.bids.some(bid => {
          const bidDate = new Date(bid.timestamp);
          return bidDate >= dayStart && bidDate < dayEnd;
        });
      });

      const bidders = new Set();
      let bids = 0;

      dayAuctions.forEach(auction => {
        auction.bids.forEach(bid => {
          const bidDate = new Date(bid.timestamp);
          if (bidDate >= dayStart && bidDate < dayEnd) {
            bidders.add(bid.bidder.toString());
            bids++;
          }
        });
      });

      bidderActivity.push({
        day: dayNames[dayDate.getDay()],
        bidders: bidders.size,
        bids
      });
    }

    res.json({
      success: true,
      data: {
        revenueOverTime,
        auctionPerformance,
        categoryDistribution: categoryDistributionArray,
        bidderActivity
      }
    });
  } catch (error) {
    console.error('Company analytics charts error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching analytics data'
    });
  }
});

// Helper function to translate category names to Arabic
function getCategoryNameInArabic(category) {
  const translations = {
    'electronics': 'إلكترونيات',
    'vehicles': 'سيارات',
    'real_estate': 'عقارات',
    'art_collectibles': 'فنون ومقتنيات',
    'machinery': 'معدات',
    'furniture': 'أثاث',
    'jewelry': 'مجوهرات',
    'books_media': 'كتب ووسائط',
    'sports_recreation': 'رياضة وترفيه',
    'fashion_accessories': 'أزياء وإكسسوارات',
    'home_garden': 'منزل وحديقة',
    'business_industrial': 'أعمال وصناعة',
    'other': 'أخرى'
  };
  return translations[category] || category;
}

// @route   POST /api/company/bids/:bidId/accept
// @desc    Accept a bid on company's auction
// @access  Private (Company users)
router.post('/bids/:bidId/accept', requireRole('company'), async (req, res) => {
  try {
    const bidId = req.params.bidId;
    const companyId = req.user._id;

    // Find the auction with this bid
    const auction = await Auction.findOne({
      'bids._id': bidId,
      seller: companyId
    });

    if (!auction) {
      return res.status(404).json({
        success: false,
        message: 'Bid not found or you do not have permission to accept it'
      });
    }

    // Find the specific bid
    const bid = auction.bids.id(bidId);
    if (!bid) {
      return res.status(404).json({
        success: false,
        message: 'Bid not found'
      });
    }

    // Update bid status to accepted
    bid.status = 'accepted';

    // Set auction winner if this is the highest bid
    if (bid.amount >= auction.currentPrice) {
      auction.winner = bid.bidder;
      auction.status = 'ended';
      auction.finalPrice = bid.amount;
    }

    await auction.save();

    res.json({
      success: true,
      message: 'Bid accepted successfully',
      data: { bid, auction }
    });
  } catch (error) {
    console.error('Error accepting bid:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while accepting bid'
    });
  }
});

// @route   POST /api/company/bids/:bidId/reject
// @desc    Reject a bid on company's auction
// @access  Private (Company users)
router.post('/bids/:bidId/reject', requireRole('company'), async (req, res) => {
  try {
    const bidId = req.params.bidId;
    const companyId = req.user._id;

    // Find the auction with this bid
    const auction = await Auction.findOne({
      'bids._id': bidId,
      seller: companyId
    });

    if (!auction) {
      return res.status(404).json({
        success: false,
        message: 'Bid not found or you do not have permission to reject it'
      });
    }

    // Find the specific bid
    const bid = auction.bids.id(bidId);
    if (!bid) {
      return res.status(404).json({
        success: false,
        message: 'Bid not found'
      });
    }

    // Update bid status to rejected
    bid.status = 'rejected';
    await auction.save();

    res.json({
      success: true,
      message: 'Bid rejected successfully',
      data: { bid }
    });
  } catch (error) {
    console.error('Error rejecting bid:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while rejecting bid'
    });
  }
});

module.exports = router;
