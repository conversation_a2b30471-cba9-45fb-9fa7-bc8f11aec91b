const express = require('express');
const { body, validationResult } = require('express-validator');
const Tender = require('../models/Tender');
const User = require('../models/User');
const { authenticate, requireApproved, requireRole } = require('../middleware/auth');
const { uploadToCloudinary } = require('../utils/cloudinary');
const upload = require('../middleware/upload');

const router = express.Router();

// @route   GET /api/tenders
// @desc    Get all open tenders with advanced filters
// @access  Public
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;
    
    const { 
      category, 
      search, 
      sortBy, 
      minBudget, 
      maxBudget, 
      location,
      deadlineWithin,
      proposalCount,
      organizerType,
      featured,
      publishedAfter,
      publishedBefore,
      status
    } = req.query;

    // Build filter object with advanced filters
    const filter = {};
    
    // Status filter - default to open if not specified
    if (status) {
      filter.status = status;
    } else {
      filter.status = 'open';
    }
    
    // Category filter
    if (category) filter.category = category;
    
    // Location filter (city, state, or country)
    if (location) {
      filter.$or = [
        { 'location.city': { $regex: location, $options: 'i' } },
        { 'location.state': { $regex: location, $options: 'i' } },
        { 'location.country': { $regex: location, $options: 'i' } }
      ];
    }
    
    // Budget range filter
    if (minBudget || maxBudget) {
      filter.budget = {};
      if (minBudget) filter.budget.$gte = parseInt(minBudget);
      if (maxBudget) filter.budget.$lte = parseInt(maxBudget);
    }

    // Deadline proximity filter
    if (deadlineWithin) {
      const now = new Date();
      const daysFromNow = parseInt(deadlineWithin);
      const futureDate = new Date(now.getTime() + (daysFromNow * 24 * 60 * 60 * 1000));
      filter.deadline = { $gte: now, $lte: futureDate };
    }

    // Publication date filters
    if (publishedAfter || publishedBefore) {
      filter.createdAt = {};
      if (publishedAfter) filter.createdAt.$gte = new Date(publishedAfter);
      if (publishedBefore) filter.createdAt.$lte = new Date(publishedBefore);
    }

    // Featured filter
    if (featured === 'true') {
      filter.featured = true;
    }

    // Advanced search across multiple fields
    if (search) {
      const searchRegex = { $regex: search, $options: 'i' };
      filter.$or = [
        { title: searchRegex },
        { description: searchRegex },
        { 'requirements': { $elemMatch: { $regex: search, $options: 'i' } } }
      ];
    }

    // Build aggregation pipeline for proposal count and organizer type filters
    let aggregationPipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'users',
          localField: 'organizer',
          foreignField: '_id',
          as: 'organizerInfo'
        }
      },
      {
        $addFields: {
          proposalCount: { $size: '$proposals' },
          organizerType: { $arrayElemAt: ['$organizerInfo.role', 0] }
        }
      }
    ];

    // Organizer type filter
    if (organizerType) {
      aggregationPipeline.push({
        $match: { organizerType: organizerType }
      });
    }

    // Proposal count filter
    if (proposalCount) {
      const [operator, value] = proposalCount.split(':');
      const countValue = parseInt(value);
      
      switch (operator) {
        case 'gt':
          aggregationPipeline.push({ $match: { proposalCount: { $gt: countValue } } });
          break;
        case 'lt':
          aggregationPipeline.push({ $match: { proposalCount: { $lt: countValue } } });
          break;
        case 'eq':
          aggregationPipeline.push({ $match: { proposalCount: countValue } });
          break;
        case 'gte':
          aggregationPipeline.push({ $match: { proposalCount: { $gte: countValue } } });
          break;
        case 'lte':
          aggregationPipeline.push({ $match: { proposalCount: { $lte: countValue } } });
          break;
      }
    }

    // Build sort object with enhanced options
    let sort = { createdAt: -1 };
    switch (sortBy) {
      case 'budget_asc':
        sort = { budget: 1 };
        break;
      case 'budget_desc':
        sort = { budget: -1 };
        break;
      case 'deadline_soon':
        sort = { deadline: 1 };
        break;
      case 'deadline_far':
        sort = { deadline: -1 };
        break;
      case 'newest':
        sort = { createdAt: -1 };
        break;
      case 'oldest':
        sort = { createdAt: 1 };
        break;
      case 'proposals_most':
        sort = { proposalCount: -1 };
        break;
      case 'proposals_least':
        sort = { proposalCount: 1 };
        break;
      case 'title_asc':
        sort = { title: 1 };
        break;
      case 'title_desc':
        sort = { title: -1 };
        break;
      case 'featured_first':
        sort = { featured: -1, createdAt: -1 };
        break;
    }

    // Add sorting to aggregation pipeline
    aggregationPipeline.push({ $sort: sort });
    
    // Add pagination
    aggregationPipeline.push({ $skip: skip });
    aggregationPipeline.push({ $limit: limit });

    // Add population and field selection
    aggregationPipeline.push({
      $lookup: {
        from: 'users',
        localField: 'organizer',
        foreignField: '_id',
        as: 'organizer',
        pipeline: [
          {
            $project: {
              'profile.fullName': 1,
              'profile.companyName': 1,
              'profile.governmentEntity': 1,
              'role': 1
            }
          }
        ]
      }
    });

    aggregationPipeline.push({
      $addFields: {
        organizer: { $arrayElemAt: ['$organizer', 0] }
      }
    });

    // Remove sensitive proposal documents
    aggregationPipeline.push({
      $project: {
        'proposals.documents': 0
      }
    });

    const tenders = await Tender.aggregate(aggregationPipeline);

    // Get total count for pagination
    const totalPipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'users',
          localField: 'organizer',
          foreignField: '_id',
          as: 'organizerInfo'
        }
      },
      {
        $addFields: {
          proposalCount: { $size: '$proposals' },
          organizerType: { $arrayElemAt: ['$organizerInfo.role', 0] }
        }
      }
    ];

    if (organizerType) {
      totalPipeline.push({ $match: { organizerType: organizerType } });
    }

    if (proposalCount) {
      const [operator, value] = proposalCount.split(':');
      const countValue = parseInt(value);
      
      switch (operator) {
        case 'gt':
          totalPipeline.push({ $match: { proposalCount: { $gt: countValue } } });
          break;
        case 'lt':
          totalPipeline.push({ $match: { proposalCount: { $lt: countValue } } });
          break;
        case 'eq':
          totalPipeline.push({ $match: { proposalCount: countValue } });
          break;
        case 'gte':
          totalPipeline.push({ $match: { proposalCount: { $gte: countValue } } });
          break;
        case 'lte':
          totalPipeline.push({ $match: { proposalCount: { $lte: countValue } } });
          break;
      }
    }

    totalPipeline.push({ $count: 'total' });
    const totalResult = await Tender.aggregate(totalPipeline);
    const total = totalResult[0]?.total || 0;

    res.json({
      success: true,
      data: {
        tenders,
        pagination: {
          current: page,
          pages: Math.ceil(total / limit),
          total
        },
        filters: {
          appliedFilters: {
            category,
            location,
            minBudget,
            maxBudget,
            deadlineWithin,
            proposalCount,
            organizerType,
            featured,
            publishedAfter,
            publishedBefore,
            status: status || 'open'
          },
          totalResults: total
        }
      }
    });
  } catch (error) {
    console.error('Get tenders error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching tenders'
    });
  }
});

// @route   GET /api/tenders/categories
// @desc    Get tender categories with counts
// @access  Public
router.get('/categories', async (req, res) => {
  try {
    const categories = await Tender.aggregate([
      { $match: { status: 'open' } },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    res.json({
      success: true,
      data: { categories }
    });
  } catch (error) {
    console.error('Get tender categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching categories'
    });
  }
});

// @route   GET /api/tenders/:id
// @desc    Get single tender details
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const tender = await Tender.findById(req.params.id)
      .populate('organizer', 'profile.fullName profile.companyName profile.governmentEntity profile.phone email')
      .populate('proposals.proposer', 'profile.fullName profile.companyName')
      .populate('awardedTo', 'profile.fullName profile.companyName');

    if (!tender) {
      return res.status(404).json({
        success: false,
        message: 'Tender not found'
      });
    }

    res.json({
      success: true,
      data: { tender }
    });
  } catch (error) {
    console.error('Get tender details error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching tender details'
    });
  }
});

// @route   POST /api/tenders
// @desc    Create new tender
// @access  Private (Company/Government only)
router.post('/',
  authenticate,
  requireApproved,
  requireRole('company', 'government'),
  [
    body('title').trim().isLength({ min: 3, max: 200 }),
    body('description').trim().isLength({ min: 10, max: 2000 }),
    body('category').isIn(['construction', 'it_technology', 'consulting', 'healthcare', 'education', 'transportation', 'real_estate', 'manufacturing', 'energy', 'other']),
    body('budget').isFloat({ min: 1 }),
    body('startDate').isISO8601(),
    body('deadline').isISO8601(),
    body('requirements').isArray({ min: 1 })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const {
        title,
        description,
        category,
        budget,
        startDate,
        deadline,
        location,
        requirements
      } = req.body;

      // Validate dates
      const start = new Date(startDate);
      const end = new Date(deadline);
      const now = new Date();

      if (start < now) {
        return res.status(400).json({
          success: false,
          message: 'Start date cannot be in the past'
        });
      }

      if (end <= start) {
        return res.status(400).json({
          success: false,
          message: 'Deadline must be after start date'
        });
      }

      // Create tender
      const tender = new Tender({
        title,
        description,
        category,
        organizer: req.user._id,
        budget: parseFloat(budget),
        startDate: start,
        deadline: end,
        location: location ? (typeof location === 'string' ? JSON.parse(location) : location) : {},
        requirements: Array.isArray(requirements) ? requirements : [requirements]
      });

      await tender.save();

      const populatedTender = await Tender.findById(tender._id)
        .populate('organizer', 'profile.fullName profile.companyName profile.governmentEntity');

      res.status(201).json({
        success: true,
        message: 'Tender created successfully',
        data: { tender: populatedTender }
      });
    } catch (error) {
      console.error('Create tender error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while creating tender'
      });
    }
  }
);

// @route   POST /api/tenders/:id/proposal
// @desc    Submit proposal for tender
// @access  Private (Company only)
router.post('/:id/proposal',
  authenticate,
  requireApproved,
  requireRole('company'),
  upload.array('documents', 10),
  [
    body('amount').isFloat({ min: 1 }),
    body('proposal').trim().isLength({ min: 10, max: 2000 })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { amount, proposal } = req.body;
      const tender = await Tender.findById(req.params.id);

      if (!tender) {
        return res.status(404).json({
          success: false,
          message: 'Tender not found'
        });
      }

      // Check if user already submitted a proposal
      const existingProposal = tender.proposals.find(
        p => p.proposer.toString() === req.user._id.toString()
      );

      if (existingProposal) {
        return res.status(400).json({
          success: false,
          message: 'You have already submitted a proposal for this tender'
        });
      }

      // Upload documents to cloudinary
      const documents = [];
      if (req.files && req.files.length > 0) {
        for (const file of req.files) {
          const uploadResult = await uploadToCloudinary(file.buffer, {
            folder: `tender-proposals/${req.user._id}`,
            resource_type: 'auto'
          });
          
          documents.push({
            name: file.originalname,
            type: file.mimetype,
            url: uploadResult.secure_url
          });
        }
      }

      try {
        const newProposal = tender.submitProposal(
          req.user._id,
          parseFloat(amount),
          proposal,
          documents
        );

        await tender.save();

        res.status(201).json({
          success: true,
          message: 'Proposal submitted successfully',
          data: {
            proposal: {
              id: newProposal._id,
              amount: newProposal.amount,
              proposal: newProposal.proposal,
              documentsCount: newProposal.documents.length,
              submittedAt: newProposal.submittedAt
            }
          }
        });
      } catch (proposalError) {
        return res.status(400).json({
          success: false,
          message: proposalError.message
        });
      }
    } catch (error) {
      console.error('Submit proposal error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while submitting proposal'
      });
    }
  }
);

// @route   POST /api/tenders/:id/award/:proposalId
// @desc    Award tender to winning proposal
// @access  Private (Tender organizer only)
router.post('/:id/award/:proposalId',
  authenticate,
  requireApproved,
  async (req, res) => {
    try {
      const tender = await Tender.findById(req.params.id);

      if (!tender) {
        return res.status(404).json({
          success: false,
          message: 'Tender not found'
        });
      }

      if (tender.organizer.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to award this tender'
        });
      }

      if (tender.status !== 'open') {
        return res.status(400).json({
          success: false,
          message: 'Tender is not open for awarding'
        });
      }

      try {
        tender.awardTender(req.params.proposalId);
        await tender.save();

        const populatedTender = await Tender.findById(tender._id)
          .populate('awardedTo', 'profile.fullName profile.companyName');

        res.json({
          success: true,
          message: 'Tender awarded successfully',
          data: { tender: populatedTender }
        });
      } catch (awardError) {
        return res.status(400).json({
          success: false,
          message: awardError.message
        });
      }
    } catch (error) {
      console.error('Award tender error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while awarding tender'
      });
    }
  }
);

// @route   GET /api/tenders/user/my-tenders
// @desc    Get user's tenders
// @access  Private
router.get('/user/my-tenders',
  authenticate,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;
      const { status } = req.query;

      const filter = { organizer: req.user._id };
      if (status) filter.status = status;

      const tenders = await Tender.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Tender.countDocuments(filter);

      res.json({
        success: true,
        data: {
          tenders,
          pagination: {
            current: page,
            pages: Math.ceil(total / limit),
            total
          }
        }
      });
    } catch (error) {
      console.error('Get my tenders error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching user tenders'
      });
    }
  }
);

// @route   GET /api/tenders/user/my-proposals
// @desc    Get user's proposals
// @access  Private
router.get('/user/my-proposals',
  authenticate,
  async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      const tenders = await Tender.find({
        'proposals.proposer': req.user._id
      })
      .populate('organizer', 'profile.fullName profile.companyName profile.governmentEntity')
      .sort({ 'proposals.submittedAt': -1 })
      .skip(skip)
      .limit(limit);

      // Filter to show only user's proposals and tender info
      const userProposals = tenders.map(tender => {
        const userProposal = tender.proposals.find(
          p => p.proposer.toString() === req.user._id.toString()
        );

        return {
          tender: {
            id: tender._id,
            title: tender.title,
            budget: tender.budget,
            status: tender.status,
            deadline: tender.deadline,
            organizer: tender.organizer
          },
          proposal: userProposal,
          isAwarded: tender.awardedTo && tender.awardedTo.toString() === req.user._id.toString()
        };
      });

      const total = await Tender.countDocuments({
        'proposals.proposer': req.user._id
      });

      res.json({
        success: true,
        data: {
          proposals: userProposals,
          pagination: {
            current: page,
            pages: Math.ceil(total / limit),
            total
          }
        }
      });
    } catch (error) {
      console.error('Get my proposals error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while fetching user proposals'
      });
    }
  }
);

// @route   PUT /api/tenders/:id
// @desc    Update tender (before deadline)
// @access  Private (Tender organizer)
router.put('/:id',
  authenticate,
  requireApproved,
  async (req, res) => {
    try {
      const tender = await Tender.findById(req.params.id);

      if (!tender) {
        return res.status(404).json({
          success: false,
          message: 'Tender not found'
        });
      }

      if (tender.organizer.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to update this tender'
        });
      }

      if (tender.status !== 'open') {
        return res.status(400).json({
          success: false,
          message: 'Cannot update tender that is closed or awarded'
        });
      }

      if (tender.proposals.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Cannot update tender that has received proposals'
        });
      }

      // Update allowed fields
      const allowedUpdates = ['title', 'description', 'budget', 'deadline', 'location', 'requirements'];
      allowedUpdates.forEach(field => {
        if (req.body[field] !== undefined) {
          tender[field] = req.body[field];
        }
      });

      await tender.save();

      res.json({
        success: true,
        message: 'Tender updated successfully',
        data: { tender }
      });
    } catch (error) {
      console.error('Update tender error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while updating tender'
      });
    }
  }
);

// @route   POST /api/tenders/:id/close
// @desc    Close tender (stop accepting proposals)
// @access  Private (Tender organizer)
router.post('/:id/close',
  authenticate,
  requireApproved,
  async (req, res) => {
    try {
      const tender = await Tender.findById(req.params.id);

      if (!tender) {
        return res.status(404).json({
          success: false,
          message: 'Tender not found'
        });
      }

      if (tender.organizer.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to close this tender'
        });
      }

      if (tender.status !== 'open') {
        return res.status(400).json({
          success: false,
          message: 'Tender is not open'
        });
      }

      tender.status = 'closed';
      await tender.save();

      res.json({
        success: true,
        message: 'Tender closed successfully'
      });
    } catch (error) {
      console.error('Close tender error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while closing tender'
      });
    }
  }
);

// @route   DELETE /api/tenders/:id
// @desc    Delete tender (if no proposals)
// @access  Private (Tender organizer)
router.delete('/:id',
  authenticate,
  requireApproved,
  async (req, res) => {
    try {
      const tender = await Tender.findById(req.params.id);

      if (!tender) {
        return res.status(404).json({
          success: false,
          message: 'Tender not found'
        });
      }

      if (tender.organizer.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to delete this tender'
        });
      }

      if (tender.proposals.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete tender that has received proposals'
        });
      }

      await Tender.findByIdAndDelete(req.params.id);

      res.json({
        success: true,
        message: 'Tender deleted successfully'
      });
    } catch (error) {
      console.error('Delete tender error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while deleting tender'
      });
    }
  }
);

module.exports = router;
