const express = require('express');
const User = require('../models/User');
const Auction = require('../models/Auction');
const Tender = require('../models/Tender');

const router = express.Router();

// @route   GET /api/stats/public
// @desc    Get public platform statistics
// @access  Public
router.get('/public', async (req, res) => {
  try {
    // Get total users count
    const totalUsers = await User.countDocuments({ 
      status: 'approved',
      role: { $in: ['individual', 'company'] }
    });
    
    // Get total auctions count
    const totalAuctions = await Auction.countDocuments();
    
    // Get total companies count
    const totalCompanies = await User.countDocuments({ 
      role: 'company',
      status: 'approved'
    });
    
    // Calculate total transaction volume (completed auctions)
    const completedAuctions = await Auction.find({ 
      status: 'ended',
      winner: { $exists: true }
    });
    
    const totalVolume = completedAuctions.reduce((sum, auction) => {
      return sum + (auction.currentPrice || 0);
    }, 0);
    
    // Convert to millions for display
    const volumeInMillions = Math.round(totalVolume / 1000000);
    
    res.json({
      success: true,
      data: {
        stats: {
          users: totalUsers,
          auctions: totalAuctions,
          companies: totalCompanies,
          volume: volumeInMillions
        }
      }
    });
  } catch (error) {
    console.error('Public stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching public statistics'
    });
  }
});

// @route   GET /api/stats/platform
// @desc    Get detailed platform statistics (Admin only)
// @access  Private (Admin)
router.get('/platform', async (req, res) => {
  try {
    // This would require authentication middleware
    // For now, return basic stats
    
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    // User statistics
    const totalUsers = await User.countDocuments({ role: { $in: ['individual', 'company'] } });
    const activeUsers = await User.countDocuments({ 
      role: { $in: ['individual', 'company'] },
      status: 'approved'
    });
    const newUsersThisMonth = await User.countDocuments({
      role: { $in: ['individual', 'company'] },
      createdAt: { $gte: thirtyDaysAgo }
    });
    
    // Auction statistics
    const totalAuctions = await Auction.countDocuments();
    const activeAuctions = await Auction.countDocuments({ status: 'active' });
    const completedAuctions = await Auction.countDocuments({ status: 'ended' });
    const newAuctionsThisWeek = await Auction.countDocuments({
      createdAt: { $gte: sevenDaysAgo }
    });
    
    // Tender statistics
    const totalTenders = await Tender.countDocuments();
    const activeTenders = await Tender.countDocuments({ status: 'active' });
    const completedTenders = await Tender.countDocuments({ status: 'closed' });
    
    // Revenue statistics
    const completedAuctionsWithWinner = await Auction.find({ 
      status: 'ended',
      winner: { $exists: true }
    });
    
    const totalRevenue = completedAuctionsWithWinner.reduce((sum, auction) => {
      return sum + (auction.currentPrice || 0);
    }, 0);
    
    const revenueThisMonth = completedAuctionsWithWinner
      .filter(auction => new Date(auction.endDate) >= thirtyDaysAgo)
      .reduce((sum, auction) => sum + (auction.currentPrice || 0), 0);
    
    res.json({
      success: true,
      data: {
        users: {
          total: totalUsers,
          active: activeUsers,
          newThisMonth: newUsersThisMonth
        },
        auctions: {
          total: totalAuctions,
          active: activeAuctions,
          completed: completedAuctions,
          newThisWeek: newAuctionsThisWeek
        },
        tenders: {
          total: totalTenders,
          active: activeTenders,
          completed: completedTenders
        },
        revenue: {
          total: totalRevenue,
          thisMonth: revenueThisMonth
        }
      }
    });
  } catch (error) {
    console.error('Platform stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching platform statistics'
    });
  }
});

// @route   GET /api/stats/charts/auctions
// @desc    Get auction statistics for charts
// @access  Private (Admin)
router.get('/charts/auctions', async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 30;
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    
    // Get auction creation data by day
    const auctionsByDay = await Auction.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);
    
    res.json({
      success: true,
      data: {
        auctionsByDay
      }
    });
  } catch (error) {
    console.error('Auction charts error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching auction chart data'
    });
  }
});

// @route   GET /api/stats/charts/revenue
// @desc    Get revenue statistics for charts
// @access  Private (Admin)
router.get('/charts/revenue', async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 30;
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    
    // Get revenue data by day
    const revenueByDay = await Auction.aggregate([
      {
        $match: {
          status: 'ended',
          winner: { $exists: true },
          endDate: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$endDate" }
          },
          revenue: { $sum: "$currentPrice" },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);
    
    res.json({
      success: true,
      data: {
        revenueByDay
      }
    });
  } catch (error) {
    console.error('Revenue charts error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching revenue chart data'
    });
  }
});

module.exports = router;
