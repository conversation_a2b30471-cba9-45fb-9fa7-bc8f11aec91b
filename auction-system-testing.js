#!/usr/bin/env node

/**
 * Comprehensive Auction System End-to-End Testing
 * Tests complete auction lifecycle from creation to winner determination
 */

const axios = require('axios');
const colors = require('colors');

const API_BASE = 'http://localhost:5000/api';

// Test results tracking
const auctionTestResults = {
  passed: 0,
  failed: 0,
  total: 0,
  phases: {}
};

const log = {
  success: (msg) => console.log('✅'.green + ' ' + msg),
  error: (msg) => console.log('❌'.red + ' ' + msg),
  info: (msg) => console.log('ℹ️'.blue + ' ' + msg),
  warning: (msg) => console.log('⚠️'.yellow + ' ' + msg),
  phase: (msg) => console.log('🔄'.cyan + ' ' + msg.bold)
};

const runTest = async (testName, testFn, phase = 'general') => {
  auctionTestResults.total++;
  if (!auctionTestResults.phases[phase]) {
    auctionTestResults.phases[phase] = { passed: 0, failed: 0, total: 0 };
  }
  auctionTestResults.phases[phase].total++;

  try {
    const result = await testFn();
    auctionTestResults.passed++;
    auctionTestResults.phases[phase].passed++;
    log.success(testName);
    return result;
  } catch (error) {
    auctionTestResults.failed++;
    auctionTestResults.phases[phase].failed++;
    log.error(`${testName}: ${error.message}`);
    throw error;
  }
};

// Authentication helper
const authenticateUser = async (email, password) => {
  const response = await axios.post(`${API_BASE}/auth/login`, {
    email,
    password
  });
  
  if (!response.data.success) {
    throw new Error(response.data.message);
  }
  
  return response.data.data.accessToken;
};

// Phase 1: Auction Creation Testing
const testAuctionCreation = async () => {
  log.phase('Phase 1: Testing Auction Creation');
  
  let companyToken, auctionId;

  // Test 1: Company Authentication
  companyToken = await runTest('Company Authentication for Auction Creation', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    if (!token) throw new Error('No token received');
    return token;
  }, 'creation');

  // Test 2: Create Basic Auction
  auctionId = await runTest('Create Basic Auction', async () => {
    const auctionData = {
      title: 'Test Auction - High-Performance Laptop',
      description: 'Dell XPS 15 with Intel i7, 16GB RAM, 512GB SSD. Excellent condition, barely used.',
      startingPrice: 8000,
      startDate: new Date(Date.now() - 60 * 1000).toISOString(), // 1 minute ago (auction is active)
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      category: 'electronics',
      condition: 'excellent',
      specifications: {
        brand: 'Dell',
        model: 'XPS 15',
        processor: 'Intel i7-11800H',
        ram: '16GB DDR4',
        storage: '512GB NVMe SSD',
        graphics: 'NVIDIA GTX 1650'
      },
      images: ['https://via.placeholder.com/600x400/0066cc/ffffff?text=Dell+XPS+15']
    };

    const response = await axios.post(`${API_BASE}/auctions`, auctionData, {
      headers: { 'Authorization': `Bearer ${companyToken}` }
    });

    if (!response.data.success) {
      throw new Error(response.data.message);
    }

    return response.data.data.auction._id;
  }, 'creation');

  // Test 3: Verify Auction Details
  await runTest('Verify Created Auction Details', async () => {
    const response = await axios.get(`${API_BASE}/auctions/${auctionId}`);
    
    if (!response.data.success) {
      throw new Error('Failed to fetch auction details');
    }

    const auction = response.data.data.auction;
    if (auction.title !== 'Test Auction - High-Performance Laptop') {
      throw new Error('Auction title mismatch');
    }
    if (auction.startingPrice !== 8000) {
      throw new Error('Starting price mismatch');
    }
    if (auction.status !== 'active') {
      throw new Error('Auction should be active');
    }
  }, 'creation');

  // Test 4: Create Auction with Invalid Data
  await runTest('Handle Invalid Auction Creation', async () => {
    try {
      await axios.post(`${API_BASE}/auctions`, {
        title: '', // Invalid empty title
        startingPrice: -100, // Invalid negative price
        startDate: new Date(Date.now() + 60 * 1000).toISOString(), // 1 minute from now
        endDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Past date
        category: 'electronics',
        condition: 'good'
      }, {
        headers: { 'Authorization': `Bearer ${companyToken}` }
      });
      throw new Error('Should have failed with invalid data');
    } catch (error) {
      if (error.response && error.response.status >= 400) {
        // Expected error response
        return;
      }
      throw new Error('Should have returned validation error');
    }
  }, 'creation');

  // Test 5: Unauthorized Auction Creation
  await runTest('Prevent Unauthorized Auction Creation', async () => {
    try {
      await axios.post(`${API_BASE}/auctions`, {
        title: 'Unauthorized Auction',
        startingPrice: 1000,
        startDate: new Date(Date.now() + 60 * 1000).toISOString(), // 1 minute from now
        endDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        category: 'electronics',
        condition: 'good'
      });
      throw new Error('Should have failed without authentication');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Expected 401 Unauthorized
        return;
      }
      throw new Error('Should have returned 401 Unauthorized');
    }
  }, 'creation');

  return { companyToken, auctionId };
};

// Phase 2: Bidding Process Testing
const testBiddingProcess = async (auctionId) => {
  log.phase('Phase 2: Testing Bidding Process');
  
  let user1Token, user2Token, user3Token;

  // Test 1: Multiple User Authentication
  user1Token = await runTest('User 1 Authentication (Ahmed)', async () => {
    return await authenticateUser('<EMAIL>', 'password123');
  }, 'bidding');

  user2Token = await runTest('User 2 Authentication (Fatima)', async () => {
    return await authenticateUser('<EMAIL>', 'password123');
  }, 'bidding');

  user3Token = await runTest('User 3 Authentication (Sara)', async () => {
    return await authenticateUser('<EMAIL>', 'password123');
  }, 'bidding');

  // Test 2: First Bid Placement
  await runTest('Place First Bid (Ahmed - 8500 SAR)', async () => {
    const response = await axios.post(`${API_BASE}/auctions/${auctionId}/bid`, {
      bidAmount: 8500
    }, {
      headers: { 'Authorization': `Bearer ${user1Token}` }
    });

    if (!response.data.success) {
      throw new Error(response.data.message);
    }
  }, 'bidding');

  // Test 3: Higher Bid Placement
  await runTest('Place Higher Bid (Fatima - 9000 SAR)', async () => {
    const response = await axios.post(`${API_BASE}/auctions/${auctionId}/bid`, {
      bidAmount: 9000
    }, {
      headers: { 'Authorization': `Bearer ${user2Token}` }
    });

    if (!response.data.success) {
      throw new Error(response.data.message);
    }
  }, 'bidding');

  // Test 4: Invalid Lower Bid
  await runTest('Reject Lower Bid (Sara - 8800 SAR)', async () => {
    try {
      await axios.post(`${API_BASE}/auctions/${auctionId}/bid`, {
        bidAmount: 8800 // Lower than current highest bid
      }, {
        headers: { 'Authorization': `Bearer ${user3Token}` }
      });
      throw new Error('Should have rejected lower bid');
    } catch (error) {
      if (error.response && error.response.status >= 400) {
        // Expected error for lower bid
        return;
      }
      throw new Error('Should have rejected lower bid amount');
    }
  }, 'bidding');

  // Test 5: Competitive Bidding
  await runTest('Competitive Bidding (Ahmed - 9500 SAR)', async () => {
    const response = await axios.post(`${API_BASE}/auctions/${auctionId}/bid`, {
      bidAmount: 9500
    }, {
      headers: { 'Authorization': `Bearer ${user1Token}` }
    });

    if (!response.data.success) {
      throw new Error(response.data.message);
    }
  }, 'bidding');

  await runTest('Final Competitive Bid (Fatima - 10000 SAR)', async () => {
    const response = await axios.post(`${API_BASE}/auctions/${auctionId}/bid`, {
      bidAmount: 10000
    }, {
      headers: { 'Authorization': `Bearer ${user2Token}` }
    });

    if (!response.data.success) {
      throw new Error(response.data.message);
    }
  }, 'bidding');

  // Test 6: Verify Auction Current Price Update
  await runTest('Verify Current Price Updates', async () => {
    const response = await axios.get(`${API_BASE}/auctions/${auctionId}`);
    
    if (!response.data.success) {
      throw new Error('Failed to fetch auction details');
    }

    const auction = response.data.data.auction;
    if (auction.currentPrice !== 10000) {
      throw new Error(`Expected current price 10000, got ${auction.currentPrice}`);
    }
    if (auction.bids.length < 4) {
      throw new Error(`Expected at least 4 bids, got ${auction.bids.length}`);
    }
  }, 'bidding');

  // Test 7: Invalid Bid Scenarios
  await runTest('Handle Invalid Bid Amount', async () => {
    try {
      await axios.post(`${API_BASE}/auctions/${auctionId}/bid`, {
        bidAmount: -100 // Negative amount
      }, {
        headers: { 'Authorization': `Bearer ${user1Token}` }
      });
      throw new Error('Should have rejected negative bid');
    } catch (error) {
      if (error.response && error.response.status >= 400) {
        return;
      }
      throw new Error('Should have rejected negative bid amount');
    }
  }, 'bidding');

  return { user1Token, user2Token, user3Token };
};

// Phase 3: Bid Management Testing
const testBidManagement = async (auctionId, companyToken) => {
  log.phase('Phase 3: Testing Bid Management');

  // Test 1: Company Views Bids
  await runTest('Company Views All Bids on Auction', async () => {
    const response = await axios.get(`${API_BASE}/company/bids`, {
      headers: { 'Authorization': `Bearer ${companyToken}` }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch company bids');
    }

    const bids = response.data.data.bids;
    const auctionBids = bids.filter(bid => bid.auction._id === auctionId);

    if (auctionBids.length === 0) {
      throw new Error('No bids found for the test auction');
    }
  }, 'management');

  // Test 2: Get Specific Auction Bids
  let highestBid;
  await runTest('Get Auction Bid Details', async () => {
    const response = await axios.get(`${API_BASE}/auctions/${auctionId}`);

    if (!response.data.success) {
      throw new Error('Failed to fetch auction details');
    }

    const auction = response.data.data.auction;
    if (auction.bids.length === 0) {
      throw new Error('No bids found on auction');
    }

    // Find highest bid
    highestBid = auction.bids.reduce((highest, current) =>
      current.amount > highest.amount ? current : highest
    );

    if (!highestBid) {
      throw new Error('Could not determine highest bid');
    }
  }, 'management');

  // Test 3: Accept Highest Bid
  await runTest('Company Accepts Highest Bid', async () => {
    const response = await axios.post(`${API_BASE}/company/bids/${highestBid._id}/accept`, {}, {
      headers: { 'Authorization': `Bearer ${companyToken}` }
    });

    if (!response.data.success) {
      throw new Error(response.data.message);
    }
  }, 'management');

  // Test 4: Verify Bid Acceptance
  await runTest('Verify Bid Acceptance Status', async () => {
    const response = await axios.get(`${API_BASE}/auctions/${auctionId}`);

    if (!response.data.success) {
      throw new Error('Failed to fetch auction details');
    }

    const auction = response.data.data.auction;
    const acceptedBid = auction.bids.find(bid => bid._id === highestBid._id);

    if (!acceptedBid || acceptedBid.status !== 'accepted') {
      throw new Error('Bid was not properly accepted');
    }
  }, 'management');

  // Test 5: Prevent Double Acceptance
  await runTest('Prevent Double Bid Acceptance', async () => {
    try {
      await axios.post(`${API_BASE}/company/bids/${highestBid._id}/accept`, {}, {
        headers: { 'Authorization': `Bearer ${companyToken}` }
      });
      // If this succeeds, it might be okay (idempotent), but let's check the response
    } catch (error) {
      // Expected to fail or handle gracefully
      if (error.response && error.response.status >= 400) {
        return; // Expected error
      }
    }
    // If no error, verify the bid is still properly accepted
    const response = await axios.get(`${API_BASE}/auctions/${auctionId}`);
    const auction = response.data.data.auction;
    const acceptedBid = auction.bids.find(bid => bid._id === highestBid._id);
    if (acceptedBid.status !== 'accepted') {
      throw new Error('Bid acceptance status corrupted');
    }
  }, 'management');

  return { highestBid };
};

// Phase 4: Auction Completion Testing
const testAuctionCompletion = async (auctionId, highestBid) => {
  log.phase('Phase 4: Testing Auction Completion');

  // Test 1: Verify Auction Status After Bid Acceptance
  await runTest('Verify Auction Status After Acceptance', async () => {
    const response = await axios.get(`${API_BASE}/auctions/${auctionId}`);

    if (!response.data.success) {
      throw new Error('Failed to fetch auction details');
    }

    const auction = response.data.data.auction;

    // Check if auction is marked as ended or has a winner
    if (auction.winner) {
      const winnerId = auction.winner._id || auction.winner;
      const bidderId = highestBid.bidder._id || highestBid.bidder;
      if (winnerId.toString() !== bidderId.toString()) {
        throw new Error(`Wrong winner assigned to auction. Expected: ${bidderId}, Got: ${winnerId}`);
      }
    }

    if (auction.finalPrice && auction.finalPrice !== highestBid.amount) {
      throw new Error('Final price does not match accepted bid amount');
    }
  }, 'completion');

  // Test 2: Winner Notification Status
  await runTest('Verify Winner Determination', async () => {
    const response = await axios.get(`${API_BASE}/auctions/${auctionId}`);

    if (!response.data.success) {
      throw new Error('Failed to fetch auction details');
    }

    const auction = response.data.data.auction;

    if (auction.winner) {
      log.info(`Auction winner: ${auction.winner}`);
      log.info(`Final price: ${auction.finalPrice} SAR`);
    } else {
      log.warning('Auction winner not yet determined');
    }
  }, 'completion');

  // Test 3: Bid History Integrity
  await runTest('Verify Bid History Integrity', async () => {
    const response = await axios.get(`${API_BASE}/auctions/${auctionId}`);

    if (!response.data.success) {
      throw new Error('Failed to fetch auction details');
    }

    const auction = response.data.data.auction;

    // Verify bids are in chronological order
    for (let i = 1; i < auction.bids.length; i++) {
      const prevBid = auction.bids[i - 1];
      const currentBid = auction.bids[i];

      if (new Date(currentBid.timestamp) < new Date(prevBid.timestamp)) {
        throw new Error('Bids are not in chronological order');
      }
    }

    // Verify bid amounts are increasing (except for rejected bids)
    const acceptedBids = auction.bids.filter(bid => bid.status !== 'rejected');
    for (let i = 1; i < acceptedBids.length; i++) {
      if (acceptedBids[i].amount <= acceptedBids[i - 1].amount) {
        throw new Error('Bid amounts are not properly increasing');
      }
    }
  }, 'completion');
};

// Phase 5: User Experience Testing
const testUserExperience = async (auctionId, userTokens) => {
  log.phase('Phase 5: Testing User Experience');

  // Test 1: Winner Can View Their Won Auction
  await runTest('Winner Views Won Auction in Profile', async () => {
    const response = await axios.get(`${API_BASE}/users/bids`, {
      headers: { 'Authorization': `Bearer ${userTokens.user2Token}` } // Fatima won with 10000 SAR
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch user bids');
    }

    const userBids = response.data.data.bids;
    console.log('User bids response:', JSON.stringify(userBids, null, 2));
    console.log('Looking for auction ID:', auctionId);

    const winningBid = userBids.find(bid =>
      bid.auction._id === auctionId && bid.status === 'accepted'
    );

    if (!winningBid) {
      console.log('Available bid statuses:', userBids.map(bid => ({ auctionId: bid.auction._id, status: bid.status })));
      throw new Error('Winning bid not found in user profile');
    }
  }, 'user_experience');

  // Test 2: Losing Bidders See Correct Status
  await runTest('Losing Bidders See Correct Status', async () => {
    const response = await axios.get(`${API_BASE}/users/bids`, {
      headers: { 'Authorization': `Bearer ${userTokens.user1Token}` } // Ahmed lost
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch user bids');
    }

    const userBids = response.data.data.bids;
    const losingBids = userBids.filter(bid =>
      bid.auction._id === auctionId && bid.status !== 'accepted'
    );

    if (losingBids.length === 0) {
      throw new Error('Losing bids not found in user profile');
    }
  }, 'user_experience');

  // Test 3: Public Auction View Shows Correct Information
  await runTest('Public Auction View Shows Winner', async () => {
    const response = await axios.get(`${API_BASE}/auctions/${auctionId}`);

    if (!response.data.success) {
      throw new Error('Failed to fetch auction details');
    }

    const auction = response.data.data.auction;

    // Verify public information is correct
    if (auction.currentPrice !== 10000) {
      throw new Error('Current price not updated correctly');
    }

    if (auction.bids.length < 4) {
      throw new Error('Not all bids are visible');
    }
  }, 'user_experience');
};

// Main auction system testing function
const runCompleteAuctionSystemTest = async () => {
  console.log('🏷️ Starting Complete Auction System End-to-End Testing\n'.bold.cyan);

  try {
    // Check system health
    log.info('Checking system health...');
    const healthResponse = await axios.get(`${API_BASE}/health`);
    if (healthResponse.data.status !== 'OK') {
      throw new Error('System health check failed');
    }
    log.success('System is healthy and ready for auction testing');

    // Execute all phases
    const { companyToken, auctionId } = await testAuctionCreation();
    const userTokens = await testBiddingProcess(auctionId);
    const { highestBid } = await testBidManagement(auctionId, companyToken);
    await testAuctionCompletion(auctionId, highestBid);
    await testUserExperience(auctionId, userTokens);

    // Display comprehensive results
    console.log('\n📊 Auction System Testing Results'.bold.cyan);
    console.log('=' .repeat(60));

    console.log(`\n📈 Overall Results:`);
    console.log(`Total Tests: ${auctionTestResults.total}`);
    console.log(`Passed: ${auctionTestResults.passed}`.green);
    console.log(`Failed: ${auctionTestResults.failed}`.red);
    console.log(`Success Rate: ${((auctionTestResults.passed / auctionTestResults.total) * 100).toFixed(1)}%`);

    console.log(`\n🔍 Results by Phase:`);
    Object.keys(auctionTestResults.phases).forEach(phase => {
      const stats = auctionTestResults.phases[phase];
      const successRate = ((stats.passed / stats.total) * 100).toFixed(1);
      const status = stats.failed === 0 ? '✅' : '⚠️';

      console.log(`${status} ${phase.toUpperCase()}: ${stats.passed}/${stats.total} (${successRate}%)`);
      if (stats.failed > 0) {
        console.log(`   Failed: ${stats.failed} tests`.red);
      }
    });

    // Test summary
    console.log('\n🎯 Auction Lifecycle Summary:'.bold.green);
    console.log('✅ Phase 1: Auction Creation - Company creates auction with validation');
    console.log('✅ Phase 2: Bidding Process - Multiple users place competitive bids');
    console.log('✅ Phase 3: Bid Management - Company manages and accepts bids');
    console.log('✅ Phase 4: Auction Completion - Winner determination and finalization');
    console.log('✅ Phase 5: User Experience - All users see correct auction status');

    // Recommendations
    console.log('\n💡 Recommendations:'.bold.yellow);
    if (auctionTestResults.failed === 0) {
      console.log('🎉 All auction system tests passed! The auction system is fully functional.'.green);
      console.log('🚀 The platform is ready for real auction transactions.'.green);
    } else {
      console.log('⚠️  Some auction tests failed. Please review the errors above.'.yellow);
      console.log('🔧 Consider fixing auction-related issues before production deployment.'.yellow);
    }

    console.log('\n📋 Auction System Features Verified:');
    console.log('• Auction creation with validation');
    console.log('• Real-time bidding with price validation');
    console.log('• Competitive bidding between multiple users');
    console.log('• Bid management (accept/reject) by auction owners');
    console.log('• Winner determination and auction completion');
    console.log('• User experience for winners and losers');
    console.log('• Data integrity and bid history tracking');
    console.log('• Security and authorization controls');

    console.log(`\n🏷️ Test Auction Details:`);
    console.log(`Auction ID: ${auctionId}`);
    console.log(`Final Status: Completed with winner determined`);
    console.log(`Total Bids: Multiple competitive bids placed`);
    console.log(`Winner: Highest bidder (Fatima - 10,000 SAR)`);

  } catch (error) {
    log.error(`Auction system testing failed: ${error.message}`);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Ensure MongoDB is running with test data');
    console.log('2. Ensure backend server is running on port 5000');
    console.log('3. Verify test users exist and are approved');
    console.log('4. Check auction API endpoints are working');
    console.log('5. Review server logs for detailed errors');
    process.exit(1);
  }
};

// Test specific auction phase
const runSpecificAuctionPhase = async (phaseName) => {
  console.log(`🏷️ Testing Auction ${phaseName} Phase\n`.bold.cyan);

  try {
    // For specific phase testing, we need to create a test auction first
    const { companyToken, auctionId } = await testAuctionCreation();

    switch (phaseName.toLowerCase()) {
      case 'creation':
        // Already tested above
        break;
      case 'bidding':
        await testBiddingProcess(auctionId);
        break;
      case 'management':
        const userTokens = await testBiddingProcess(auctionId);
        await testBidManagement(auctionId, companyToken);
        break;
      case 'completion':
        const tokens = await testBiddingProcess(auctionId);
        const { highestBid } = await testBidManagement(auctionId, companyToken);
        await testAuctionCompletion(auctionId, highestBid);
        break;
      case 'experience':
        const userToks = await testBiddingProcess(auctionId);
        const { highestBid: hBid } = await testBidManagement(auctionId, companyToken);
        await testAuctionCompletion(auctionId, hBid);
        await testUserExperience(auctionId, userToks);
        break;
      default:
        console.log('❌ Unknown phase. Available phases:');
        console.log('  - creation');
        console.log('  - bidding');
        console.log('  - management');
        console.log('  - completion');
        console.log('  - experience');
        process.exit(1);
    }

    // Display results for specific phase
    const phase = phaseName.toLowerCase();
    if (auctionTestResults.phases[phase]) {
      const stats = auctionTestResults.phases[phase];
      console.log(`\n📊 ${phaseName} Phase Results:`);
      console.log(`Passed: ${stats.passed}/${stats.total}`);
      console.log(`Success Rate: ${((stats.passed / stats.total) * 100).toFixed(1)}%`);
    }

  } catch (error) {
    log.error(`${phaseName} phase testing failed: ${error.message}`);
    process.exit(1);
  }
};

// Command line interface
const main = async () => {
  const command = process.argv[2];

  if (command && command !== 'all') {
    await runSpecificAuctionPhase(command);
  } else {
    await runCompleteAuctionSystemTest();
  }
};

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  log.error(`Unhandled error: ${error.message}`);
  process.exit(1);
});

// Run tests if this script is executed directly
if (require.main === module) {
  main().catch((error) => {
    log.error(`Auction system testing failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runCompleteAuctionSystemTest,
  runSpecificAuctionPhase,
  testAuctionCreation,
  testBiddingProcess,
  testBidManagement,
  testAuctionCompletion,
  testUserExperience
};
