# 👥 Comprehensive Test Users Guide
## Auction and Tender Platform

This guide provides detailed information about all test users created for comprehensive testing of the platform.

## 🔑 Universal Password
**All test accounts use the same password: `password123`**

---

## 🛡️ ADMIN USERS

### Super Admin
- **Email**: `<EMAIL>`
- **Name**: المدير العام للمنصة
- **Role**: super_admin
- **Status**: ✅ Approved
- **Permissions**: Full system access, all admin functions
- **Use Case**: System administration, platform management

### System Admin
- **Email**: `<EMAIL>`
- **Name**: مدير النظام الرئيسي
- **Role**: admin
- **Status**: ✅ Approved
- **Permissions**: User management, content moderation
- **Use Case**: Daily administration tasks

### Support Admin
- **Email**: `<EMAIL>`
- **Name**: مدير الدعم الفني
- **Role**: admin
- **Status**: ✅ Approved
- **Department**: الدعم الفني
- **Use Case**: Customer support, ticket management

---

## 👤 INDIVIDUAL USERS

### Ahmed (Active User)
- **Email**: `<EMAIL>`
- **Name**: أحمد محمد علي الشهري
- **Status**: ✅ Approved
- **Occupation**: مهندس برمجيات
- **Location**: الرياض
- **Use Case**: Active bidder, frequent platform user

### Fatima (Professional User)
- **Email**: `<EMAIL>`
- **Name**: فاطمة أحمد السالم القحطاني
- **Status**: ✅ Approved
- **Occupation**: طبيبة أسنان
- **Location**: جدة
- **Use Case**: Professional user, high-value bidder

### Mohammed (Pending User)
- **Email**: `<EMAIL>`
- **Name**: محمد عبدالله الخالد العتيبي
- **Status**: ⏳ Pending
- **Occupation**: محاسب
- **Location**: الدمام
- **Use Case**: Test pending approval workflow

### Sara (Designer)
- **Email**: `<EMAIL>`
- **Name**: سارة عبدالرحمن المطيري
- **Status**: ✅ Approved
- **Occupation**: مصممة جرافيك
- **Location**: الرياض
- **Use Case**: Creative professional, art/design auctions

### Khalid (Blocked User)
- **Email**: `<EMAIL>`
- **Name**: خالد سعد الغامدي
- **Status**: 🚫 Blocked
- **Reason**: مخالفة شروط الاستخدام
- **Use Case**: Test blocked user scenarios

---

## 🏢 COMPANY USERS

### Tech Company (Large Enterprise)
- **Email**: `<EMAIL>`
- **Name**: شركة التقنية المتقدمة للحلول الذكية
- **Status**: ✅ Approved
- **Type**: Technology
- **Employees**: 150
- **Revenue**: 25M SAR
- **Use Case**: Large tech company, high-value auctions

### Construction Company (Established)
- **Email**: `<EMAIL>`
- **Name**: مؤسسة البناء والتشييد الحديث
- **Status**: ✅ Approved
- **Type**: Construction
- **Employees**: 300
- **Revenue**: 75M SAR
- **Use Case**: Construction tenders, equipment auctions

### Logistics Company (Pending)
- **Email**: `<EMAIL>`
- **Name**: شركة الخدمات اللوجستية المتكاملة
- **Status**: ⏳ Pending
- **Type**: Logistics
- **Employees**: 80
- **Use Case**: Test company approval process

### Energy Company (Green Tech)
- **Email**: `<EMAIL>`
- **Name**: شركة الطاقة المتجددة والبيئة
- **Status**: ✅ Approved
- **Type**: Energy
- **Employees**: 120
- **Revenue**: 30M SAR
- **Use Case**: Renewable energy projects, green tenders

### Medical Company (Blocked)
- **Email**: `<EMAIL>`
- **Name**: مؤسسة الخدمات الطبية المتخصصة
- **Status**: 🚫 Blocked
- **Reason**: عدم استكمال الوثائق المطلوبة
- **Use Case**: Test blocked company scenarios

---

## 🏛️ GOVERNMENT USERS

### Commerce Ministry
- **Email**: `<EMAIL>`
- **Name**: أحمد بن سعد المالكي
- **Department**: وزارة التجارة والاستثمار
- **Position**: مدير عام المشتريات الحكومية
- **Status**: ✅ Approved
- **Clearance**: سري
- **Use Case**: Commercial tenders, trade-related procurement

### Health Ministry
- **Email**: `<EMAIL>`
- **Name**: فاطمة بنت عبدالله الزهراني
- **Department**: وزارة الصحة
- **Position**: مديرة المشتريات الطبية والمعدات
- **Status**: ✅ Approved
- **Clearance**: محدود
- **Use Case**: Medical equipment tenders, healthcare procurement

### Education Ministry
- **Email**: `<EMAIL>`
- **Name**: محمد بن عبدالرحمن القحطاني
- **Department**: وزارة التعليم
- **Position**: مدير المشتريات التعليمية
- **Status**: ✅ Approved
- **Clearance**: عام
- **Use Case**: Educational equipment, school supplies

### Transport Ministry (Pending)
- **Email**: `<EMAIL>`
- **Name**: سارة بنت خالد العتيبي
- **Department**: وزارة النقل والخدمات اللوجستية
- **Position**: مديرة المشاريع والمناقصات
- **Status**: ⏳ Pending
- **Use Case**: Test government approval process

### Defense Ministry
- **Email**: `<EMAIL>`
- **Name**: عبدالله بن أحمد الشهري
- **Department**: وزارة الدفاع
- **Position**: مدير المشتريات العسكرية
- **Status**: ✅ Approved
- **Clearance**: سري للغاية
- **Use Case**: Defense contracts, military procurement

---

## 🧪 Testing Scenarios by User Type

### Individual User Testing
1. **Registration & Verification**
2. **Profile Management**
3. **Auction Browsing & Bidding**
4. **Tender Applications**
5. **Payment Processing**
6. **Notification Management**

### Company User Testing
1. **Company Registration**
2. **Document Verification**
3. **Auction Creation & Management**
4. **Bid Management (Accept/Reject)**
5. **Tender Applications**
6. **Financial Reporting**

### Government User Testing
1. **Department Registration**
2. **Tender Creation & Management**
3. **Application Review & Scoring**
4. **Award Process**
5. **Compliance Monitoring**
6. **Reporting & Analytics**

### Admin User Testing
1. **User Management & Approval**
2. **Content Moderation**
3. **System Monitoring**
4. **Email Template Management**
5. **Support Ticket Management**
6. **Platform Analytics**

---

## 🛠️ User Management Commands

### List All Users
```bash
node backend/user-management-utility.js list
```

### Show User Statistics
```bash
node backend/user-management-utility.js stats
```

### Approve All Pending Users
```bash
node backend/user-management-utility.js approve
```

### Block a User
```bash
node backend/user-management-utility.<NAME_EMAIL> "Reason"
```

### Unblock a User
```bash
node backend/user-management-utility.<NAME_EMAIL>
```

---

## 📊 Test Data Summary

| User Type | Total | Approved | Pending | Blocked |
|-----------|-------|----------|---------|---------|
| **Admin** | 3 | 3 | 0 | 0 |
| **Individual** | 5 | 3 | 1 | 1 |
| **Company** | 5 | 3 | 1 | 1 |
| **Government** | 5 | 3 | 1 | 0 |
| **TOTAL** | **18** | **12** | **3** | **2** |

---

## 🎯 Testing Priorities

### High Priority
1. ✅ **Approved Users**: Test core functionality
2. ⏳ **Pending Users**: Test approval workflows
3. 🚫 **Blocked Users**: Test access restrictions

### Medium Priority
1. **Role Transitions**: Admin approval/rejection
2. **Data Validation**: Profile completeness
3. **Security**: Access control testing

### Low Priority
1. **Edge Cases**: Unusual user scenarios
2. **Performance**: Large user datasets
3. **Integration**: Third-party services

This comprehensive test user setup ensures thorough testing of all platform features and user workflows.
