# 🧪 Manual User Workflow Testing Guide
## Complete End-to-End Testing for All User Types

This guide provides step-by-step instructions for manually testing all user workflows in the Auction and Tender Platform.

## 🚀 Prerequisites

### 1. Environment Setup
```bash
# Ensure the application is running
cd backend && npm run dev
cd frontend && npm run dev

# Seed test data
cd backend && npm run seed

# Run automated workflow tests (optional)
node workflow-testing-suite.js
```

### 2. Access Points
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **Health Check**: http://localhost:5000/api/health

---

## 👤 INDIVIDUAL USER WORKFLOW TESTING

### Test Account: Ahmed (<EMAIL> / password123)

#### 1. Registration & Authentication Workflow
**New User Registration:**
1. Navigate to http://localhost:3000/auth/register
2. Select "Individual" user type
3. Fill in registration form:
   - Name: Test Individual User
   - Email: <EMAIL>
   - Password: password123
   - Phone: +************
   - National ID: **********
   - Date of Birth: 1990-01-01
4. Submit form
5. ✅ **Expected**: Success message, redirect to login
6. ✅ **Verify**: User created with "pending" status

**User Login:**
1. Navigate to http://localhost:3000/auth/login
2. Enter credentials: <EMAIL> / password123
3. Click "Login"
4. ✅ **Expected**: Successful login, redirect to user dashboard

#### 2. Dashboard Access Workflow
1. After login, verify dashboard loads
2. ✅ **Check**: User statistics display correctly
3. ✅ **Check**: Recent bids section shows data
4. ✅ **Check**: Recent auctions section shows data
5. ✅ **Check**: Notifications section works

#### 3. Profile Management Workflow
1. Navigate to Profile section
2. Update profile information:
   - Change phone number
   - Update address
   - Add profile picture
3. Save changes
4. ✅ **Expected**: Success message, data saved
5. ✅ **Verify**: Changes persist after page refresh

#### 4. Auction Browsing & Bidding Workflow
1. Navigate to Auctions page
2. ✅ **Check**: Auction list loads with real data
3. ✅ **Check**: Filters work (category, price range)
4. ✅ **Check**: Search functionality works
5. Click on an auction to view details
6. ✅ **Check**: Auction details page loads correctly
7. Place a bid:
   - Enter bid amount (higher than current price)
   - Submit bid
8. ✅ **Expected**: Bid placed successfully
9. ✅ **Verify**: Bid appears in user's bid history

#### 5. Tender Application Workflow
1. Navigate to Tenders page
2. ✅ **Check**: Tender list loads with government tenders
3. ✅ **Check**: Tender details page loads
4. Apply for a tender:
   - Fill application form
   - Upload required documents
   - Submit application
5. ✅ **Expected**: Application submitted successfully
6. ✅ **Verify**: Application appears in user's applications

#### 6. Bid History & Management
1. Navigate to "My Bids" page
2. ✅ **Check**: All user bids display correctly
3. ✅ **Check**: Bid status updates (active, won, lost)
4. ✅ **Check**: Filtering by status works
5. ✅ **Check**: Bid details are accurate

---

## 🏢 COMPANY USER WORKFLOW TESTING

### Test Account: Tech Company (<EMAIL> / password123)

#### 1. Company Authentication & Dashboard
1. Login with company credentials
2. ✅ **Expected**: Redirect to company dashboard
3. ✅ **Check**: Company statistics display
4. ✅ **Check**: Active auctions count
5. ✅ **Check**: Recent bids on company auctions

#### 2. Auction Creation Workflow
1. Navigate to "Create Auction" page
2. Fill auction creation form:
   - Title: Test Laptop Auction
   - Description: High-performance laptop
   - Starting Price: 5000 SAR
   - End Date: 7 days from now
   - Category: Electronics
   - Upload images
3. Submit auction
4. ✅ **Expected**: Auction created successfully
5. ✅ **Verify**: Auction appears in company's auction list

#### 3. Auction Management Workflow
1. Navigate to "My Auctions" page
2. ✅ **Check**: All company auctions display
3. ✅ **Check**: Auction status (active, ended, draft)
4. Edit an auction:
   - Update description
   - Change end date
   - Save changes
5. ✅ **Expected**: Changes saved successfully

#### 4. Bid Management Workflow
1. Navigate to "Bids Management" page
2. ✅ **Check**: All bids on company auctions display
3. ✅ **Check**: Bid details (bidder, amount, time)
4. Accept a bid:
   - Click "Accept" on a bid
   - Confirm action
5. ✅ **Expected**: Bid accepted, auction status updated
6. Reject a bid:
   - Click "Reject" on a bid
   - Provide reason
7. ✅ **Expected**: Bid rejected with reason

#### 5. Tender Application Workflow
1. Navigate to Government Tenders
2. ✅ **Check**: Available tenders display
3. Apply for a tender:
   - Fill company application
   - Upload company documents
   - Submit proposal
4. ✅ **Expected**: Application submitted
5. ✅ **Verify**: Application appears in company's applications

---

## 🏛️ GOVERNMENT USER WORKFLOW TESTING

### Test Account: Commerce Ministry (<EMAIL> / password123)

#### 1. Government Authentication & Dashboard
1. Login with government credentials
2. ✅ **Expected**: Redirect to government dashboard
3. ✅ **Check**: Department information displays
4. ✅ **Check**: Active tenders count
5. ✅ **Check**: Pending applications count

#### 2. Tender Creation Workflow
1. Navigate to "Create Tender" page
2. Fill tender creation form:
   - Title: Office Equipment Procurement
   - Description: Procurement of computers and printers
   - Budget: 500,000 SAR
   - Category: Technology
   - Submission Deadline: 30 days from now
   - Opening Date: 35 days from now
   - Requirements: List of requirements
3. Upload tender documents
4. Submit tender
5. ✅ **Expected**: Tender created successfully
6. ✅ **Verify**: Tender appears in public tender list

#### 3. Tender Management Workflow
1. Navigate to "My Tenders" page
2. ✅ **Check**: All department tenders display
3. ✅ **Check**: Tender status (open, closed, awarded)
4. Edit a tender:
   - Update description
   - Extend deadline
   - Save changes
5. ✅ **Expected**: Changes saved successfully

#### 4. Application Review Workflow
1. Navigate to tender applications
2. ✅ **Check**: All applications for tenders display
3. Review an application:
   - View company details
   - Download submitted documents
   - Score the application
4. ✅ **Check**: Scoring system works
5. Award tender:
   - Select winning application
   - Confirm award
6. ✅ **Expected**: Tender awarded successfully

---

## 🛡️ ADMIN USER WORKFLOW TESTING

### Test Account: Admin (<EMAIL> / password123)

#### 1. Admin Authentication & Dashboard
1. Login with admin credentials
2. ✅ **Expected**: Redirect to admin dashboard
3. ✅ **Check**: System statistics display
4. ✅ **Check**: User management section
5. ✅ **Check**: Platform activity overview

#### 2. User Management Workflow
1. Navigate to "User Management" page
2. ✅ **Check**: All users display with status
3. ✅ **Check**: Filter by role and status works
4. Approve a pending user:
   - Find pending user
   - Click "Approve"
   - Confirm action
5. ✅ **Expected**: User approved, status updated
6. Block a user:
   - Select user
   - Click "Block"
   - Provide reason
7. ✅ **Expected**: User blocked with reason

#### 3. Content Moderation Workflow
1. Navigate to content moderation
2. ✅ **Check**: Flagged auctions/tenders display
3. Review flagged content:
   - View content details
   - Make moderation decision
   - Take action (approve/reject/remove)
4. ✅ **Expected**: Moderation actions applied

#### 4. Email Template Management
1. Navigate to "Email Templates" page
2. ✅ **Check**: All templates display
3. Edit a template:
   - Update subject line
   - Modify content
   - Save changes
4. ✅ **Expected**: Template updated successfully
5. Test email template:
   - Send test email
   - Verify email received

#### 5. Support Ticket Management
1. Navigate to "Support Tickets" page
2. ✅ **Check**: All tickets display with priority
3. Respond to a ticket:
   - Open ticket details
   - Add response
   - Update status
4. ✅ **Expected**: Response sent, status updated

---

## 🚨 ERROR HANDLING & EDGE CASES

### 1. Authentication Error Testing
- [ ] Try login with invalid credentials
- [ ] Try login with blocked user account
- [ ] Try login with pending user account
- [ ] Test session timeout handling
- [ ] Test password reset flow

### 2. Authorization Error Testing
- [ ] Try accessing admin pages as regular user
- [ ] Try accessing company features as individual
- [ ] Try modifying other users' data
- [ ] Test role-based access control

### 3. Data Validation Testing
- [ ] Submit forms with invalid data
- [ ] Test file upload limits
- [ ] Test required field validation
- [ ] Test data format validation

### 4. Network Error Testing
- [ ] Test with slow network connection
- [ ] Test with intermittent connectivity
- [ ] Test API timeout handling
- [ ] Test offline behavior

---

## 📊 Testing Checklist Summary

### Individual User (7 workflows)
- [ ] Registration & Authentication
- [ ] Dashboard Access
- [ ] Profile Management
- [ ] Auction Browsing & Bidding
- [ ] Tender Applications
- [ ] Bid History Management
- [ ] Notifications & Settings

### Company User (5 workflows)
- [ ] Company Authentication & Dashboard
- [ ] Auction Creation & Management
- [ ] Bid Management (Accept/Reject)
- [ ] Tender Applications
- [ ] Company Profile Management

### Government User (4 workflows)
- [ ] Government Authentication & Dashboard
- [ ] Tender Creation & Management
- [ ] Application Review & Scoring
- [ ] Award Process

### Admin User (5 workflows)
- [ ] Admin Authentication & Dashboard
- [ ] User Management & Approval
- [ ] Content Moderation
- [ ] Email Template Management
- [ ] Support Ticket Management

### Error Handling (4 categories)
- [ ] Authentication Errors
- [ ] Authorization Errors
- [ ] Data Validation Errors
- [ ] Network Errors

---

## 🎯 Success Criteria

✅ **All workflows complete without errors**
✅ **Data persists correctly across sessions**
✅ **Role-based access control works properly**
✅ **Error handling is graceful and informative**
✅ **UI/UX is intuitive and responsive**
✅ **Performance is acceptable (< 3 seconds load time)**

## 📞 Troubleshooting

If tests fail:
1. Check browser console for JavaScript errors
2. Verify backend server is running
3. Ensure MongoDB is connected
4. Check network connectivity
5. Verify test data is seeded
6. Review server logs for API errors
