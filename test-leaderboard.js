// Simple test to verify leaderboard page compilation
const { execSync } = require('child_process');
const path = require('path');

try {
  console.log('Testing leaderboard page compilation...');
  
  // Change to frontend directory
  process.chdir(path.join(__dirname, 'frontend'));
  
  // Run Next.js build check
  const result = execSync('npm run build', { 
    encoding: 'utf8',
    timeout: 60000 
  });
  
  console.log('✅ Leaderboard page compiled successfully!');
  console.log('Build output:', result);
  
} catch (error) {
  console.error('❌ Compilation failed:', error.message);
  if (error.stdout) {
    console.log('STDOUT:', error.stdout);
  }
  if (error.stderr) {
    console.log('STDERR:', error.stderr);
  }
}
