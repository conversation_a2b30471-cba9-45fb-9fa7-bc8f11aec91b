#!/usr/bin/env node

/**
 * Comprehensive User Workflow Testing Suite
 * Tests end-to-end workflows for all user types
 */

const axios = require('axios');
const colors = require('colors');

const API_BASE = 'http://localhost:5000/api';
const FRONTEND_URL = 'http://localhost:3000';

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  workflows: {}
};

const log = {
  success: (msg) => console.log('✅'.green + ' ' + msg),
  error: (msg) => console.log('❌'.red + ' ' + msg),
  info: (msg) => console.log('ℹ️'.blue + ' ' + msg),
  warning: (msg) => console.log('⚠️'.yellow + ' ' + msg),
  workflow: (msg) => console.log('🔄'.cyan + ' ' + msg.bold)
};

const runTest = async (testName, testFn, workflow = 'general') => {
  testResults.total++;
  if (!testResults.workflows[workflow]) {
    testResults.workflows[workflow] = { passed: 0, failed: 0, total: 0 };
  }
  testResults.workflows[workflow].total++;

  try {
    await testFn();
    testResults.passed++;
    testResults.workflows[workflow].passed++;
    log.success(testName);
    return true;
  } catch (error) {
    testResults.failed++;
    testResults.workflows[workflow].failed++;
    log.error(`${testName}: ${error.message}`);
    return false;
  }
};

// Authentication helper
const authenticateUser = async (email, password) => {
  const response = await axios.post(`${API_BASE}/auth/login`, {
    email,
    password
  });
  
  if (!response.data.success) {
    throw new Error(response.data.message);
  }
  
  return response.data.data.accessToken;
};

// Test Individual User Workflow
const testIndividualUserWorkflow = async () => {
  log.workflow('Testing Individual User Workflow');
  
  // Test 1: User Authentication
  await runTest('Individual User Login', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    if (!token) throw new Error('No token received');
  }, 'individual');

  // Test 2: Dashboard Access
  await runTest('Individual Dashboard Access', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/users/profile`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!response.data.success) throw new Error('Dashboard access failed');
  }, 'individual');

  // Test 3: Profile Management
  await runTest('Individual Profile Update', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.put(`${API_BASE}/users/profile`, {
      profile: { phone: '+966501234999' }
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!response.data.success) throw new Error('Profile update failed');
  }, 'individual');

  // Test 4: Auction Browsing
  await runTest('Individual Auction Browsing', async () => {
    const response = await axios.get(`${API_BASE}/auctions`);
    if (!response.data.success) throw new Error('Auction browsing failed');
    if (!Array.isArray(response.data.data.auctions)) throw new Error('Invalid auction data');
  }, 'individual');

  // Test 5: Bid Placement
  await runTest('Individual Bid Placement', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    
    // First get available auctions
    const auctionsResponse = await axios.get(`${API_BASE}/auctions`);
    const auctions = auctionsResponse.data.data.auctions;
    
    if (auctions.length > 0) {
      const auction = auctions[0];
      const bidAmount = auction.currentPrice + 100;
      
      const response = await axios.post(`${API_BASE}/auctions/${auction._id}/bid`, {
        bidAmount: bidAmount
      }, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (!response.data.success) throw new Error('Bid placement failed');
    }
  }, 'individual');

  // Test 6: Bid History
  await runTest('Individual Bid History', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/users/bids`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!response.data.success) throw new Error('Bid history access failed');
  }, 'individual');

  // Test 7: Tender Applications
  await runTest('Individual Tender Applications', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/users/applications`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!response.data.success) throw new Error('Tender applications access failed');
  }, 'individual');
};

// Test Company User Workflow
const testCompanyUserWorkflow = async () => {
  log.workflow('Testing Company User Workflow');
  
  // Test 1: Company Authentication
  await runTest('Company User Login', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    if (!token) throw new Error('No token received');
  }, 'company');

  // Test 2: Company Dashboard
  await runTest('Company Dashboard Access', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/company/dashboard/stats`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!response.data.success) throw new Error('Company dashboard access failed');
  }, 'company');

  // Test 3: Auction Creation
  await runTest('Company Auction Creation', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.post(`${API_BASE}/auctions`, {
      title: 'Test Auction - High-Performance Laptop',
      description: 'High-performance laptop for testing purposes. Dell XPS 15 with Intel i7 processor, 16GB RAM, and 512GB SSD. Excellent condition with minimal usage.',
      startingPrice: 5000,
      startDate: new Date(Date.now() + 60 * 1000).toISOString(), // 1 minute from now
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      category: 'electronics',
      condition: 'excellent'
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!response.data.success) throw new Error('Auction creation failed');
  }, 'company');

  // Test 4: Company Auctions Management
  await runTest('Company Auctions List', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/company/auctions`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!response.data.success) throw new Error('Company auctions access failed');
  }, 'company');

  // Test 5: Bid Management
  await runTest('Company Bid Management', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/company/bids`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!response.data.success) throw new Error('Company bid management access failed');
  }, 'company');

  // Test 6: Tender Applications
  await runTest('Company Tender Applications', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/company/applications`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    // This might return 404 if endpoint doesn't exist, which is acceptable
    if (response.status !== 404 && !response.data.success) {
      throw new Error('Company tender applications access failed');
    }
  }, 'company');
};

// Test Government User Workflow
const testGovernmentUserWorkflow = async () => {
  log.workflow('Testing Government User Workflow');
  
  // Test 1: Government Authentication
  await runTest('Government User Login', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    if (!token) throw new Error('No token received');
  }, 'government');

  // Test 2: Government Dashboard
  await runTest('Government Dashboard Access', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/government/dashboard`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    // This might return 404 if endpoint doesn't exist yet
    if (response.status !== 404 && !response.data.success) {
      throw new Error('Government dashboard access failed');
    }
  }, 'government');

  // Test 3: Tender Creation
  await runTest('Government Tender Creation', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.post(`${API_BASE}/tenders`, {
      title: 'Test Tender - Office Supplies',
      description: 'Procurement of office supplies for government department',
      budget: 100000,
      category: 'other',
      startDate: new Date(Date.now() + 60 * 1000).toISOString(), // 1 minute from now
      deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      requirements: ['Office supplies procurement', 'Quality assurance required']
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!response.data.success) throw new Error('Tender creation failed');
  }, 'government');

  // Test 4: Tender Management
  await runTest('Government Tender Management', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/government/tenders`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    // This might return 404 if endpoint doesn't exist yet
    if (response.status !== 404 && !response.data.success) {
      throw new Error('Government tender management access failed');
    }
  }, 'government');

  // Test 5: Tender Applications Review
  await runTest('Government Applications Review', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/tenders`);
    if (!response.data.success) throw new Error('Tender applications review failed');
  }, 'government');
};

// Test Admin User Workflow
const testAdminUserWorkflow = async () => {
  log.workflow('Testing Admin User Workflow');

  // Test 1: Admin Authentication
  await runTest('Admin User Login', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    if (!token) throw new Error('No token received');
  }, 'admin');

  // Test 2: Admin Dashboard
  await runTest('Admin Dashboard Access', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/admin/dashboard`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    // This might return 404 if endpoint doesn't exist yet
    if (response.status !== 404 && !response.data.success) {
      throw new Error('Admin dashboard access failed');
    }
  }, 'admin');

  // Test 3: User Management
  await runTest('Admin User Management', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/admin/users`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!response.data.success) throw new Error('Admin user management access failed');
  }, 'admin');

  // Test 4: User Approval
  await runTest('Admin User Approval', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');

    // Get pending users
    const usersResponse = await axios.get(`${API_BASE}/admin/users?status=documents_submitted`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (usersResponse.data.success && usersResponse.data.data.users.length > 0) {
      const pendingUser = usersResponse.data.data.users[0];
      const response = await axios.post(`${API_BASE}/admin/approve-account/${pendingUser._id}`, {}, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      if (!response.data.success) throw new Error('User approval failed');
    }
  }, 'admin');

  // Test 5: Email Templates Management
  await runTest('Admin Email Templates', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/templates`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!response.data.success) throw new Error('Email templates access failed');
  }, 'admin');

  // Test 6: Support Tickets Management
  await runTest('Admin Support Tickets', async () => {
    const token = await authenticateUser('<EMAIL>', 'password123');
    const response = await axios.get(`${API_BASE}/support/tickets`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    if (!response.data.success) throw new Error('Support tickets access failed');
  }, 'admin');
};

// Test User Registration Workflow
const testUserRegistrationWorkflow = async () => {
  log.workflow('Testing User Registration Workflow');

  // Test 1: Individual Registration
  await runTest('Individual User Registration', async () => {
    const userData = {
      email: `individual${Date.now()}@test.com`,
      password: 'password123',
      role: 'individual',
      profile: {
        fullName: 'Test Individual User',
        phone: '+966501234567',
        nationalId: '1234567890',
        dateOfBirth: '1990-01-01'
      }
    };

    const response = await axios.post(`${API_BASE}/auth/register`, userData);
    if (!response.data.success) throw new Error('Individual registration failed');
  }, 'registration');

  // Test 2: Company Registration
  await runTest('Company User Registration', async () => {
    const companyData = {
      email: `company${Date.now()}@test.com`,
      password: 'password123',
      role: 'company',
      profile: {
        fullName: 'Test Company Ltd',
        phone: '+966112345678',
        companyName: 'Test Company Ltd',
        commercialRegister: `CR${Date.now()}`,
        companyType: 'technology',
        establishedYear: 2020
      }
    };

    const response = await axios.post(`${API_BASE}/auth/register`, companyData);
    if (!response.data.success) throw new Error('Company registration failed');
  }, 'registration');

  // Test 3: Government Registration
  await runTest('Government User Registration', async () => {
    const govData = {
      email: `gov${Date.now()}@gov.sa`,
      password: 'password123',
      role: 'government',
      profile: {
        fullName: 'Test Government Department',
        phone: '+966114567890',
        governmentEntity: 'Test Department',
        position: 'Test Position',
        governmentId: `GOV${Date.now()}`
      }
    };

    const response = await axios.post(`${API_BASE}/auth/register`, govData);
    if (!response.data.success) throw new Error('Government registration failed');
  }, 'registration');
};

// Test Error Handling and Edge Cases
const testErrorHandlingWorkflow = async () => {
  log.workflow('Testing Error Handling and Edge Cases');

  // Test 1: Invalid Login
  await runTest('Invalid Login Handling', async () => {
    try {
      await authenticateUser('<EMAIL>', 'wrongpassword');
      throw new Error('Should have failed with invalid credentials');
    } catch (error) {
      if (error.message.includes('Should have failed')) throw error;
      // Expected to fail, so this is success
    }
  }, 'error_handling');

  // Test 2: Blocked User Login
  await runTest('Blocked User Login Handling', async () => {
    try {
      await authenticateUser('<EMAIL>', 'password123');
      throw new Error('Should have failed for blocked user');
    } catch (error) {
      if (error.message.includes('Should have failed')) throw error;
      // Expected to fail, so this is success
    }
  }, 'error_handling');

  // Test 3: Unauthorized Access
  await runTest('Unauthorized Access Handling', async () => {
    try {
      await axios.get(`${API_BASE}/admin/users`);
      throw new Error('Should have failed without authentication');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Expected 401, this is success
        return;
      }
      throw new Error('Should have returned 401 Unauthorized');
    }
  }, 'error_handling');

  // Test 4: Invalid Data Submission
  await runTest('Invalid Data Handling', async () => {
    try {
      await axios.post(`${API_BASE}/auth/register`, {
        name: '', // Invalid empty name
        email: 'invalid-email', // Invalid email format
        password: '123' // Too short password
      });
      throw new Error('Should have failed with invalid data');
    } catch (error) {
      if (error.response && error.response.status >= 400) {
        // Expected error response, this is success
        return;
      }
      throw new Error('Should have returned error for invalid data');
    }
  }, 'error_handling');
};

// Main test execution function
const runAllWorkflowTests = async () => {
  console.log('🧪 Starting Comprehensive User Workflow Testing\n'.bold.cyan);

  try {
    // Test basic connectivity first
    log.info('Checking system connectivity...');
    const healthResponse = await axios.get(`${API_BASE}/health`);
    if (healthResponse.data.status !== 'OK') {
      throw new Error('System health check failed');
    }
    log.success('System is healthy and ready for testing');

    // Run all workflow tests
    await testUserRegistrationWorkflow();
    await testIndividualUserWorkflow();
    await testCompanyUserWorkflow();
    await testGovernmentUserWorkflow();
    await testAdminUserWorkflow();
    await testErrorHandlingWorkflow();

    // Display comprehensive results
    console.log('\n📊 Workflow Testing Results Summary'.bold.cyan);
    console.log('=' .repeat(60));

    console.log(`\n📈 Overall Results:`);
    console.log(`Total Tests: ${testResults.total}`);
    console.log(`Passed: ${testResults.passed}`.green);
    console.log(`Failed: ${testResults.failed}`.red);
    console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);

    console.log(`\n🔍 Results by Workflow:`);
    Object.keys(testResults.workflows).forEach(workflow => {
      const stats = testResults.workflows[workflow];
      const successRate = ((stats.passed / stats.total) * 100).toFixed(1);
      const status = stats.failed === 0 ? '✅' : '⚠️';

      console.log(`${status} ${workflow.toUpperCase()}: ${stats.passed}/${stats.total} (${successRate}%)`);
      if (stats.failed > 0) {
        console.log(`   Failed: ${stats.failed} tests`.red);
      }
    });

    // Recommendations
    console.log('\n💡 Recommendations:'.bold.yellow);
    if (testResults.failed === 0) {
      console.log('🎉 All workflow tests passed! The application is ready for production.'.green);
    } else {
      console.log('⚠️  Some workflow tests failed. Please review the errors above.'.yellow);
      console.log('🔧 Consider implementing missing endpoints or fixing authentication issues.'.yellow);
    }

    console.log('\n📋 Next Steps:');
    console.log('1. Review any failed tests and implement missing functionality');
    console.log('2. Test the workflows manually in the browser');
    console.log('3. Proceed with auction and tender system testing');
    console.log('4. Perform performance and security testing');

  } catch (error) {
    log.error(`Workflow testing failed: ${error.message}`);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Ensure MongoDB is running');
    console.log('2. Ensure backend server is running on port 5000');
    console.log('3. Ensure test data has been seeded');
    console.log('4. Check network connectivity');
    process.exit(1);
  }
};

// Test specific workflow
const runSpecificWorkflow = async (workflowName) => {
  console.log(`🧪 Testing ${workflowName} Workflow\n`.bold.cyan);

  try {
    switch (workflowName.toLowerCase()) {
      case 'individual':
        await testIndividualUserWorkflow();
        break;
      case 'company':
        await testCompanyUserWorkflow();
        break;
      case 'government':
        await testGovernmentUserWorkflow();
        break;
      case 'admin':
        await testAdminUserWorkflow();
        break;
      case 'registration':
        await testUserRegistrationWorkflow();
        break;
      case 'errors':
        await testErrorHandlingWorkflow();
        break;
      default:
        console.log('❌ Unknown workflow. Available workflows:');
        console.log('  - individual');
        console.log('  - company');
        console.log('  - government');
        console.log('  - admin');
        console.log('  - registration');
        console.log('  - errors');
        process.exit(1);
    }

    // Display results for specific workflow
    const workflow = workflowName.toLowerCase();
    if (testResults.workflows[workflow]) {
      const stats = testResults.workflows[workflow];
      console.log(`\n📊 ${workflowName} Workflow Results:`);
      console.log(`Passed: ${stats.passed}/${stats.total}`);
      console.log(`Success Rate: ${((stats.passed / stats.total) * 100).toFixed(1)}%`);
    }

  } catch (error) {
    log.error(`${workflowName} workflow testing failed: ${error.message}`);
    process.exit(1);
  }
};

// Command line interface
const main = async () => {
  const command = process.argv[2];

  if (command && command !== 'all') {
    await runSpecificWorkflow(command);
  } else {
    await runAllWorkflowTests();
  }
};

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  log.error(`Unhandled error: ${error.message}`);
  process.exit(1);
});

// Run tests if this script is executed directly
if (require.main === module) {
  main().catch((error) => {
    log.error(`Workflow testing failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  runAllWorkflowTests,
  runSpecificWorkflow,
  testIndividualUserWorkflow,
  testCompanyUserWorkflow,
  testGovernmentUserWorkflow,
  testAdminUserWorkflow,
  testUserRegistrationWorkflow,
  testErrorHandlingWorkflow
};
